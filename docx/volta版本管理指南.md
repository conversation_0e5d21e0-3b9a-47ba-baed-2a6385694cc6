# Volta版本管理指南

## 项目概述
本项目已集成Volta作为Node.js版本管理工具，确保团队成员使用统一的Node.js版本，避免版本不一致导致的兼容性问题。

## Volta简介
Volta是一个现代化的JavaScript工具管理器，它提供了快速、可靠的Node.js版本管理方案。与传统的nvm相比，Volta具有以下优势：
- ⚡ **极速切换**：毫秒级版本切换
- 🔧 **自动管理**：根据项目配置自动切换Node版本
- 🔄 **无缝集成**：与现有工作流完美兼容
- 📦 **轻量级**：占用资源少，运行高效

## 安装指南

### 1. 安装Volta

#### Windows系统
```bash
# 使用PowerShell (管理员权限)
iwr https://get.volta.sh | iex
```

#### macOS/Linux系统
```bash
# 使用curl
curl https://get.volta.sh | bash

# 或使用wget
wget -qO- https://get.volta.sh | bash
```

#### 验证安装
```bash
volta --version
# 输出示例：1.1.0
```

### 2. 重启终端
安装完成后，请关闭并重新打开终端，或执行：
```bash
source ~/.bashrc  # 或 ~/.zshrc, ~/.profile
```

### 3. 添加 NodeJS
```bash
# 版本1
volata install node@12.1.0

# 版本2（默认是最新lts版本）
volata install node

# 此时volta已安装了上述两个node版本，最后一次安装的node为全局默认
volata list node
```

## 项目使用

### 1. 首次使用项目
```bash
# 克隆项目
git clone [项目地址]
cd pdfc-web-4.1.0.0

# Volta会自动检测并使用配置的Node版本
# 无需手动切换，Volta会根据package.json中的volta配置自动设置
{
  "volta": {
    "node": "12.1.0"
  }
}

# 验证当前使用的Node版本
node --version
# 应该输出：v12.1.0
```

## Volta文档
 - [官方文档](https://volta.sh/)


---

