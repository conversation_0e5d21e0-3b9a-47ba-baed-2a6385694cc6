# 项目全局配置文档

## 项目概述

- **项目名称**: pdfc3-web
- **版本**: 4.0.2
- **技术栈**: Vue.js 2.6.14 + Element-UI + Vue Router + Vuex
- **构建工具**: Vue CLI 5.x + Webpack
- **开发语言**: JavaScript (ES6+)

## 核心配置文件

### 1. package.json

项目依赖和脚本配置的核心文件。

#### 关键依赖
- **Vue.js**: 2.6.14 - 主框架
- **Element-UI**: 2.15.6 - UI组件库
- **Vue Router**: 3.5.3 - 路由管理
- **Vuex**: 3.6.2 - 状态管理
- **Axios**: 1.4.0 - HTTP客户端

#### 构建脚本
```bash
npm run dev                 # 开发环境启动
npm run build:prod         # 生产环境打包
npm run build:stage        # 测试环境打包
npm run build:internetprod # 外网生产打包
npm run build:intranetprod # 内网生产打包
npm run lint               # 代码检查
npm run test:unit          # 单元测试
```

### 2. 全局配置文件

#### settings.js
项目核心配置位于 `src/settings.js`：

```javascript
module.exports = {
    // 登录配置
    loginActive: false,        // 是否启用登录页
    loginInfo: {               // 测试用户信息
        user: '123456456',
        token: Math.random(),
        userId: '100000'
    },
    
    // 基础配置
    homePath: '/dashboard',    // 首页路由
    title: 'PICC',             // 页面标题
    
    // 功能配置
    errorLog: 'production',    // 错误日志级别
    isTagsBar: true,           // 是否显示标签栏
    manualSideBar: false       // 是否手动控制侧边栏
};
```

### 3. Vue CLI配置

#### vue.config.js
动态加载webpack配置：

```javascript
"use strict";
const fs = require("fs")
let webpackConfig;
let env = process.env.VUE_APP_ENV;

// 根据环境加载对应配置
try {
    fs.accessSync(`./webpackConfig/${env}.js`, fs.constants.F_OK)
    webpackConfig = require(`./webpackConfig/${env}.js`)
} catch (err) {
    webpackConfig = require(`./webpackConfig/production.js`)
}

module.exports = webpackConfig;
```

#### webpackConfig目录
包含不同环境的webpack配置：

- **base.js**: 基础配置
- **development.js**: 开发环境配置
- **production.js**: 生产环境配置
- **internet.js**: 外网环境配置
- **intranet.js**: 内网环境配置
- **performance.js**: 性能优化配置
- **proxy.js**: 代理配置
- **externals.js**: 外部依赖配置

## 环境变量配置

### 环境文件规范

项目支持多环境配置，通过设置 `VUE_APP_ENV` 环境变量来选择对应配置：

```bash
# 开发环境
export VUE_APP_ENV=development

# 生产环境
export VUE_APP_ENV=production

# 外网环境
export VUE_APP_ENV=internet

# 内网环境
export VUE_APP_ENV=intranet
```

### 环境变量列表

#### 开发环境变量
```bash
NODE_ENV=development
VUE_APP_ENV=development
VUE_APP_BASE_API=/dev-api
VUE_APP_TITLE=PICC开发环境
```

#### 生产环境变量
```bash
NODE_ENV=production
VUE_APP_ENV=production
VUE_APP_BASE_API=/prod-api
VUE_APP_TITLE=PICC生产环境
```

## 目录结构配置

### 核心目录说明

```
src/
├── api/              # API接口定义
├── assets/           # 静态资源
├── components/       # 公共组件
├── directive/        # 自定义指令
├── filters/          # 全局过滤器
├── icons/            # 图标资源
├── lang/             # 国际化配置
├── layout/           # 布局组件
├── mixins/           # 混入文件
├── router/           # 路由配置
├── store/            # Vuex状态管理
├── styles/           # 全局样式
├── utils/            # 工具函数
└── views/            # 页面组件
```

### 路由配置

#### 路由结构
- **index.js**: 主路由配置
- **dealRouter.js**: 业务路由
- **internetRouter.js**: 外网路由
- **intranetRouter.js**: 内网路由
- **menu.js**: 菜单配置

#### 路由模式
采用Vue Router的history模式：

```javascript
const router = new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
```

### 状态管理配置

#### Vuex模块结构
```javascript
store/
├── modules/
│   ├── app.js         # 应用状态
│   ├── user.js        # 用户状态
│   ├── permission.js  # 权限状态
│   ├── tagsView.js    # 标签页状态
│   ├── settings.js    # 设置状态
│   └── errorLog.js    # 错误日志
├── getters.js         # 全局getters
└── index.js           # 主store文件
```

## 构建配置

### 构建输出配置

#### 输出目录
- **dist/car**: 生产构建输出目录
- **dist/report.html**: 构建报告

#### 构建优化
- **代码分割**: 自动代码分割和懒加载
- **压缩优化**: JS/CSS/HTML压缩
- **缓存优化**: 文件哈希缓存策略
- **CDN配置**: 支持外部CDN加载

### 开发服务器配置

#### 开发代理配置
```javascript
// webpackConfig/development.js
proxy: {
  '/dev-api': {
    target: 'http://localhost:3000',
    changeOrigin: true,
    pathRewrite: {
      '^/dev-api': ''
    }
  }
}
```

## 国际化配置

### 语言文件
支持多语言配置，位于 `src/lang/`：

- **zh.js**: 中文简体
- **zhTW.js**: 中文繁体
- **en.js**: 英文
- **es.js**: 西班牙语
- **ja.js**: 日语

### 使用示例
```javascript
// 在组件中使用
this.$t('login.username')

// 在模板中使用
{{ $t('login.username') }}
```

## 样式配置

### 样式架构
- **主题样式**: `src/assets/style/theme.less`
- **公共样式**: `src/styles/index.scss`
- **响应式**: `src/styles/responsive.scss`

### 样式变量
使用Less变量定义主题色：

```less
// 主题色
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// 布局
@layout-header-background: #001529;
@layout-sider-background: #001529;
```

## 开发工具配置

### ESLint配置
- **配置文件**: `.eslintrc.js`
- **规则集**: Vue官方推荐规则
- **自动修复**: 保存时自动格式化

### Prettier配置
- **配置文件**: `prettier.config.js`
- **代码风格**: 统一的代码格式化规则

### Git Hooks
- **pre-commit**: 提交前自动代码检查和格式化
- **lint-staged**: 仅对暂存文件进行检查

## 性能优化配置

### 代码分割策略
- **路由级**: 按路由进行代码分割
- **组件级**: 大型组件异步加载
- **第三方库**: 单独打包第三方依赖

### 缓存策略
- **文件哈希**: 使用contenthash进行缓存控制
- **CDN配置**: 静态资源CDN加速
- **浏览器缓存**: 合理的缓存头设置