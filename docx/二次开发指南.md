# 二次开发指南

## 快速开始

### 1. 项目克隆与初始化

```bash
# 克隆项目
git clone [项目仓库地址]
cd pdfc-web-4.1.0.0

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 2. 基本开发流程

```mermaid
graph TD
    A[需求分析] --> B[设计组件结构]
    B --> C[创建组件文件]
    C --> D[实现功能逻辑]
    D --> E[添加样式]
    E --> F[测试验证]
    F --> G[代码审查]
    G --> H[合并发布]
```

## 开发规范

### 1. 代码规范

#### 1.1 文件命名规范
- **组件文件**: 使用大驼峰命名法（PascalCase）
  - 正确：`UserList.vue`
  - 错误：`user-list.vue`

- **样式文件**: 使用小写连字符命名法
  - 正确：`user-profile.scss`
  - 错误：`userProfile.css`

#### 1.2 代码风格规范
```javascript
// ✅ 推荐的代码风格
export default {
  name: 'UserList',
  props: {
    userData: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      selectedUsers: []
    }
  },
  methods: {
    async fetchUsers() {
      try {
        this.loading = true
        const res = await getUserList()
        return res.data
      } catch (error) {
        console.error('获取用户列表失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
```

### 2. 组件开发规范

#### 2.1 组件结构
```
ComponentName/
├── index.vue          # 主组件文件
├── components/        # 子组件目录
├── api/              # API接口定义
├── utils/            # 工具函数
├── styles/           # 样式文件
└── README.md         # 组件说明文档
```

#### 2.2 单文件组件结构
```vue
<template>
  <!-- 模板部分 -->
  <div class="component-name">
    <!-- 内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
  beforeDestroy() {}
}
</script>

<style lang="scss" scoped>
.component-name {
  /* 样式定义 */
}
</style>
```

## 功能开发指南

### 1. 创建新页面

#### 1.1 使用脚手架生成页面
```bash
# 使用plop生成器创建新页面
npm run new

# 选择page类型，输入页面名称
# 生成的文件结构：
# src/views/[page-name]/
# ├── index.vue
# └── components/
```

#### 1.2 手动创建页面
```bash
# 创建页面目录
mkdir src/views/user-management

# 创建页面组件
touch src/views/user-management/index.vue
```

#### 1.3 注册路由
在 `src/router/dealRouter.js` 中添加路由：

```javascript
{
  path: '/user-management',
  component: () => import('@/views/user-management/index'),
  name: 'UserManagement',
  meta: {
    title: '用户管理',
    icon: 'user'
  }
}
```

### 2. 创建新组件

#### 2.1 通用组件
```bash
# 创建通用组件目录
mkdir src/components/custom-component

# 创建组件文件
touch src/components/custom-component/index.vue
```

#### 2.2 业务组件
```bash
# 创建业务组件
mkdir src/views/user-management/components/user-form
```

### 3. 状态管理

#### 3.1 创建新的Vuex模块
```javascript
// src/store/modules/[module-name].js
const state = {
  // 状态定义
}

const mutations = {
  // 同步修改状态
}

const actions = {
  // 异步操作
  async fetchData({ commit }, params) {
    try {
      const res = await api.getData(params)
      commit('SET_DATA', res.data)
      return res
    } catch (error) {
      console.error('获取数据失败:', error)
      throw error
    }
  }
}

const getters = {
  // 计算属性
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

#### 3.2 注册模块
在 `src/store/index.js` 中注册：

```javascript
import newModule from './modules/[module-name]'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    permission,
    newModule // 注册新模块
  },
  getters
})
```

### 4. API接口开发

#### 4.1 创建API模块
```javascript
// src/api/[module-name].js
import request from '@/utils/request'

// 获取列表
export function getList(params) {
  return request({
    url: '/api/[endpoint]',
    method: 'get',
    params
  })
}

// 创建记录
export function createData(data) {
  return request({
    url: '/api/[endpoint]',
    method: 'post',
    data
  })
}

// 更新记录
export function updateData(id, data) {
  return request({
    url: `/api/[endpoint]/${id}`,
    method: 'put',
    data
  })
}

// 删除记录
export function deleteData(id) {
  return request({
    url: `/api/[endpoint]/${id}`,
    method: 'delete'
  })
}
```

### 5. 样式开发

#### 5.1 使用SCSS变量
```scss
// 在组件中使用全局变量
@import '~@/styles/variables.scss';

.custom-component {
  color: $primary-color;
  background: $background-color;
}
```

#### 5.2 CSS Modules
```vue
<template>
  <div :class="$style.container">
    <!-- 使用CSS Modules -->
  </div>
</template>

<style module>
.container {
  padding: 20px;
}
</style>
```

## 数据交互开发

### 1. 表单开发

#### 1.1 基础表单组件
```vue
<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="100px">
    <el-form-item label="用户名" prop="username">
      <el-input v-model="form.username" placeholder="请输入用户名" />
    </el-form-item>
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="form.email" placeholder="请输入邮箱" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        提交
      </el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { createUser } from '@/api/user'

export default {
  name: 'UserForm',
  data() {
    return {
      form: {
        username: '',
        email: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        this.loading = true
        await createUser(this.form)
        this.$message.success('创建成功')
        this.$emit('success')
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleReset() {
      this.$refs.form.resetFields()
    }
  }
}
</script>
```

### 2. 表格开发

#### 2.1 基础表格组件
```vue
<template>
  <div>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'">
            {{ scope.row.status ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    />
  </div>
</template>

<script>
import { getUserList, deleteUser } from '@/api/user'

export default {
  name: 'UserTable',
  data() {
    return {
      tableData: [],
      loading: false,
      page: 1,
      pageSize: 10,
      total: 0
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      try {
        const res = await getUserList({
          page: this.page,
          pageSize: this.pageSize
        })
        this.tableData = res.data.list
        this.total = res.data.total
      } catch (error) {
        console.error('获取数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleEdit(row) {
      this.$emit('edit', row)
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该用户吗？', '提示', {
          type: 'warning'
        })
        await deleteUser(row.id)
        this.$message.success('删除成功')
        this.fetchData()
      } catch (error) {
        console.error('删除失败:', error)
      }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.page = val
      this.fetchData()
    }
  }
}
</script>
```

## 权限管理开发

### 1. 路由权限控制

#### 1.1 定义路由元信息
```javascript
{
  path: '/admin',
  component: () => import('@/views/admin/index'),
  name: 'Admin',
  meta: {
    title: '管理员页面',
    roles: ['admin', 'super_admin']  // 允许的角色
  }
}
```

#### 1.2 路由守卫配置
在 `src/permission.js` 中添加权限验证：

```javascript
router.beforeEach(async (to, from, next) => {
  // 权限验证逻辑
  const hasRoles = store.getters.roles && store.getters.roles.length > 0
  
  if (hasRoles) {
    if (to.meta && to.meta.roles) {
      const hasPermission = roles.some(role => to.meta.roles.includes(role))
      if (hasPermission) {
        next()
      } else {
        next('/401')
      }
    } else {
      next()
    }
  }
})
```

### 2. 按钮级权限控制

#### 2.1 自定义指令
```javascript
// src/directive/permission/has.js
import store from '@/store'

function checkPermission(el, binding) {
  const { value } = binding
  const roles = store.getters && store.getters.roles

  if (value && value instanceof Array && value.length > 0) {
    const permissionRoles = value
    const hasPermission = roles.some(role => permissionRoles.includes(role))

    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
}

export default {
  inserted(el, binding) {
    checkPermission(el, binding)
  },
  update(el, binding) {
    checkPermission(el, binding)
  }
}
```

#### 2.2 使用权限指令
```vue
<template>
  <div>
    <!-- 只有管理员可见 -->
    <el-button v-has="['admin']">管理员按钮</el-button>
    
    <!-- 管理员和编辑都可见 -->
    <el-button v-has="['admin', 'editor']">编辑按钮</el-button>
  </div>
</template>
```

## 调试与测试

### 1. 开发调试

#### 1.1 Vue DevTools
```javascript
// 确保在开发环境中启用
Vue.config.devtools = process.env.NODE_ENV === 'development'
```

#### 1.2 调试工具
```javascript
// 添加调试信息
console.group('User Component Debug')
console.log('userData:', this.userData)
console.log('computedData:', this.computedData)
console.groupEnd()
```

### 2. 单元测试

#### 2.1 创建测试文件
```javascript
// tests/unit/components/UserForm.spec.js
import { shallowMount } from '@vue/test-utils'
import UserForm from '@/components/UserForm.vue'

describe('UserForm.vue', () => {
  it('renders correctly', () => {
    const wrapper = shallowMount(UserForm)
    expect(wrapper.exists()).toBe(true)
  })

  it('emits success event on submit', async () => {
    const wrapper = shallowMount(UserForm)
    await wrapper.vm.handleSubmit()
    expect(wrapper.emitted('success')).toBeTruthy()
  })
})
```

#### 2.2 运行测试
```bash
# 运行所有测试
npm run test:unit

# 运行特定测试
npm run test:unit -- --testNamePattern="UserForm"
```

## 部署与发布

### 1. 构建命令

#### 1.1 不同环境构建
```bash
# 开发环境测试
npm run build:stage

# 生产环境
npm run build:prod

# 外网环境
npm run build:internetprod

# 内网环境
npm run build:intranetprod
```

#### 1.2 构建优化
```bash
# 性能报告
npm run build:performance

# 分析包大小
npm run build:prod -- --report
```

### 2. 部署检查清单

#### 2.1 功能检查
- [ ] 所有路由正常工作
- [ ] 接口调用正常
- [ ] 权限控制生效
- [ ] 国际化正常
- [ ] 响应式布局正常

#### 2.2 性能检查
- [ ] 首屏加载时间 < 3s
- [ ] 静态资源缓存配置
- [ ] CDN配置正确
- [ ] 错误监控配置
- [ ] 性能监控配置

#### 2.3 安全检查
- [ ] 生产环境变量配置
- [ ] 敏感信息隐藏
- [ ] HTTPS配置
- [ ] 安全响应头配置
- [ ] XSS防护检查

## 性能优化

### 1. 代码分割

#### 1.1 路由级代码分割
```javascript
const UserManagement = () => import(/* webpackChunkName: "user" */ '@/views/user-management/index')
```

#### 1.2 组件级代码分割
```javascript
const UserDetail = () => import(/* webpackChunkName: "user-detail" */ '@/components/UserDetail.vue')
```

### 2. 图片优化

#### 2.1 使用WebP格式
```vue
<picture>
  <source srcset="image.webp" type="image/webp">
  <img src="image.jpg" alt="description">
</picture>
```

#### 2.2 图片懒加载
```vue
<img v-lazy="imageUrl" alt="description">
```

### 3. 缓存策略

#### 3.1 组件缓存
```javascript
// 使用keep-alive缓存组件
<keep-alive :include="cachedViews">
  <router-view />
</keep-alive>
```

## 开发工具与调试

### 1. 推荐开发工具

#### 1.1 VS Code插件
- **Vue Language Features (Volar)**
- **ESLint**
- **Prettier**
- **Auto Rename Tag**
- **Path Intellisense**

#### 1.2 浏览器插件
- **Vue DevTools**
- **React Developer Tools**
- **Redux DevTools**

### 2. 调试技巧

#### 2.1 使用Vue DevTools
```javascript
// 在控制台中访问组件
$vm0.$data
$vm0.$props
$vm0.$store
```

#### 2.2 性能分析
```javascript
// 性能标记
performance.mark('start-render')
// ...渲染逻辑
performance.mark('end-render')
performance.measure('render-time', 'start-render', 'end-render')
```

## 错误处理

### 1. 全局错误处理

#### 1.1 错误边界
```javascript
// src/utils/error-handler.js
Vue.config.errorHandler = function(err, vm, info) {
  console.error('Global Error:', err)
  console.error('Component:', vm.$options.name)
  console.error('Info:', info)
  
  // 发送错误到监控系统
  logErrorToService(err, vm, info)
}
```

#### 1.2 网络错误处理
```javascript
// src/utils/request.js
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权
          router.push('/login')
          break
        case 403:
          // 无权限
          router.push('/403')
          break
        case 500:
          // 服务器错误
          Message.error('服务器内部错误')
          break
        default:
          Message.error(error.response.data.message || '请求失败')
      }
    }
    return Promise.reject(error)
  }
)
```

### 2. 用户反馈

#### 2.1 错误提示
```javascript
// 统一错误提示
export const handleError = (error, message = '操作失败') => {
  console.error(message, error)
  
  if (error.response?.data?.message) {
    Message.error(error.response.data.message)
  } else {
    Message.error(message)
  }
}
```