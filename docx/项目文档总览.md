# 项目文档总览

## 📋 文档目录

### 1. 项目全局配置文档
**文件**: `项目全局配置文档.md`
**内容**: 
- 项目基本介绍和技术栈
- 核心配置文件说明
- 环境变量配置
- 构建和部署配置
- 国际化和样式配置

### 2. 环境配置文档
**文件**: `环境配置文档.md`
**内容**:
- 开发环境搭建步骤
- 内网仓库配置
- 环境变量配置
- 代理和端口配置
- 常见问题解决方案

### 3. 二次开发指南
**文件**: `二次开发指南.md`
**内容**:
- 快速开始和开发流程
- 代码规范和组件开发
- 功能开发详细步骤
- 权限管理和调试技巧
- 性能优化和部署发布

## 🎯 使用指南

### 新手入门
1. **环境准备** → 阅读 `环境配置文档.md`
2. **项目了解** → 阅读 `项目全局配置文档.md`
3. **开始开发** → 阅读 `二次开发指南.md`

### 开发流程
```mermaid
graph TD
    A[开始开发] --> B[环境配置]
    B --> C[项目配置]
    C --> D[二次开发]
    D --> E[测试部署]
    
    B -.-> |参考| 环境配置文档
    C -.-> |参考| 项目全局配置文档
    D -.-> |参考| 二次开发指南
```

## 🚀 快速索引

### 常用命令
```bash
# 环境配置
npm config set registry http://10.10.1.68:8082/repository/npm-all/
npm install

# 开发启动
npm run dev

# 构建部署
npm run build:prod
npm run build:internetprod
npm run build:intranetprod
```

### 关键目录
```
├── src/
│   ├── api/           # API接口
│   ├── components/    # 公共组件
│   ├── views/         # 页面组件
│   ├── router/        # 路由配置
│   ├── store/         # 状态管理
│   ├── utils/         # 工具函数
│   └── styles/        # 全局样式
├── webpackConfig/     # 构建配置
├── docx/             # 项目文档
└── tests/            # 测试文件
```

### 配置文件
- **package.json**: 项目依赖和脚本
- **vue.config.js**: Vue CLI配置
- **src/settings.js**: 全局设置
- **webpackConfig/**: 构建配置
- **src/router/**: 路由配置

## 💡 开发提示

### 1. 开发环境
- **Node.js**: **12.1.0** (精确版本要求)
- **npm**: >= 6.9.0
- **推荐**: Node.js 12.1.0 LTS

### 2. 内网仓库
- **npm仓库**: http://10.10.1.68:8082/repository/npm-all/
- **sass二进制**: http://10.9.18.171:10001/sass/node-sass/releases/download

### 3. 技术栈
- **框架**: Vue.js 2.6.14
- **UI库**: Element-UI 2.15.6
- **路由**: Vue Router 3.5.3
- **状态管理**: Vuex 3.6.2
- **构建**: Vue CLI 5.x

### 4. 代码规范
- **命名**: PascalCase组件, kebab-case文件
- **风格**: ESLint + Prettier
- **测试**: Jest单元测试
- **文档**: 中文文档

## 📞 支持联系

### 技术支持
- **项目维护**: [维护团队]
- **技术文档**: 本docx目录
- **问题反馈**: [问题跟踪系统]

### 更新日志
- **版本**: 4.1.0.0
- **更新日期**: 2025-08-06
- **文档版本**: 1.0.0

---

## 🎉 开始使用

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd pdfc-web-4.1.0.0
   ```

2. **配置环境**
   - 阅读 `环境配置文档.md`
   - 配置内网npm仓库
   - 安装依赖

3. **开始开发**
   - 阅读 `项目全局配置文档.md`
   - 参考 `二次开发指南.md`
   - 运行 `npm run dev`

## 📖 文档维护

### 更新记录
- 2025-08-06: 创建完整文档
- 包含三个核心文档
- 提供完整的开发指导

### 贡献指南
- 发现问题请提交issue
- 文档更新请同步docx目录
- 保持文档的时效性

---

**祝开发愉快！** 🎊