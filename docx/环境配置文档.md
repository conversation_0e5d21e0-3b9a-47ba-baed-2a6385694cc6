# 环境配置文档

## 开发环境配置

### 1. Node.js环境要求

- **Node.js版本**: **12.1.0** (精确版本要求)
- **npm版本**: >= 6.9.0
- **推荐版本**: Node.js 12.1.0 LTS

### 2. 安装Node.js

#### Windows系统
```bash
# 下载地址：https://nodejs.org/download/release/v12.1.0/
# 下载 node-v12.1.0-x64.msi
# 安装时勾选"Add to PATH"
node --version  # 验证安装，应显示 v12.1.0
npm --version   # 验证npm
```

#### macOS系统
```bash
# 使用nvm安装指定版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bash_profile
nvm install 12.1.0
nvm use 12.1.0

# 验证安装
node --version  # 应显示 v12.1.0
npm --version   # 应显示 6.9.0+
```

#### Linux系统
```bash
# 使用nvm安装指定版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 12.1.0
nvm use 12.1.0

# 验证安装
node --version  # 应显示 v12.1.0
npm --version   # 应显示 6.9.0+
```

### 3. 内网仓库配置

#### 3.1 内网npm仓库配置（4.0.0以后版本）

```bash
# 配置内网npm仓库地址
npm config set registry http://10.10.1.68:8082/repository/npm-all/

# 配置sass二进制文件下载地址
npm config set sass_binary_site http://10.9.18.171:10001/sass/node-sass/releases/download

# 验证配置
npm config get registry
npm config get sass_binary_site
```

#### 3.2 内网npm仓库配置（4.0.0以前版本）

```bash
# 旧版本的额外配置
npm config set registry http://10.10.1.68:8082/repository/npm-all/
npm config set sass_binary_site http://10.9.18.171:10001/sass/node-sass/releases/download
```

### 4. 项目依赖安装

#### 4.1 首次安装
```bash
# 克隆项目
git clone [项目地址]
cd pdfc-web-4.1.0.0

# 安装依赖
npm install

# 或使用淘宝镜像（国内用户）
npm install --registry=https://registry.npmmirror.com
```

#### 4.2 私有包说明
项目使用以下私有包，需要内网环境：
- `@picc/verifition`: 验证码组件
- `@picc/watermark`: 水印组件
- `web-plugin`: web插件包
- `img-async-load`: 图片异步加载组件

#### 4.3 依赖更新
```bash
# 更新依赖
npm update

# 强制重新安装
rm -rf node_modules package-lock.json
npm install
```

### 5. 环境变量配置

#### 5.1 开发环境
创建 `.env.development` 文件：
```bash
NODE_ENV=development
VUE_APP_ENV=development
VUE_APP_BASE_API=/dev-api
VUE_APP_TITLE=PICC开发环境
VUE_APP_PORT=9527
```

#### 5.2 生产环境
创建 `.env.production` 文件：
```bash
NODE_ENV=production
VUE_APP_ENV=production
VUE_APP_BASE_API=/prod-api
VUE_APP_TITLE=PICC生产环境
```

#### 5.3 外网环境
创建 `.env.internet` 文件：
```bash
NODE_ENV=production
VUE_APP_ENV=internet
VUE_APP_BASE_API=/internet-api
VUE_APP_TITLE=PICC外网环境
```

#### 5.4 内网环境
创建 `.env.intranet` 文件：
```bash
NODE_ENV=production
VUE_APP_ENV=intranet
VUE_APP_BASE_API=/intranet-api
VUE_APP_TITLE=PICC内网环境
```

### 6. 代理配置

#### 6.1 开发代理配置
在 `vue.config.js` 中配置代理：

```javascript
module.exports = {
  devServer: {
    port: 9527,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/dev-api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        pathRewrite: {
          '^/dev-api': ''
        }
      }
    }
  }
}
```

#### 6.2 常见后端服务地址
```bash
# 开发环境
API_BASE_URL=http://localhost:3000

# 测试环境
API_BASE_URL=http://test-server:8080

# 生产环境
API_BASE_URL=http://prod-server:8080
```

### 7. 开发工具配置

#### 7.1 VS Code推荐插件
```json
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### 7.2 ESLint配置
确保VS Code设置：
```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue"
  ]
}
```

### 8. 网络配置

#### 8.1 防火墙配置
确保以下端口开放：
- 开发服务端口：9527
- 后端API端口：3000, 8080, 8082
- 内网仓库端口：8082

#### 8.2 代理配置（如有需要）
```bash
# 设置npm代理
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# 取消代理
npm config delete proxy
npm config delete https-proxy
```

### 9. Docker环境配置

#### 9.1 Docker开发环境
```bash
# 使用Dockerfile构建镜像
docker build -t pdfc-web-dev -f apirouter.Dockerfile .

# 运行容器
docker run -d \
  --name pdfc-web-dev \
  -p 9527:9527 \
  -v $(pwd):/app \
  -v /app/node_modules \
  pdfc-web-dev
```

#### 9.2 Docker生产环境
```bash
# 构建生产镜像
docker build -t pdfc-web-prod .

# 运行生产容器
docker run -d \
  --name pdfc-web-prod \
  -p 80:80 \
  pdfc-web-prod
```

### 10. 环境验证

#### 10.1 环境检查脚本
创建 `env-check.js`：

```javascript
const fs = require('fs');
const path = require('path');

console.log('=== 环境检查 ===');

// 检查Node.js版本
console.log('Node.js版本:', process.version);

// 检查npm版本
const { execSync } = require('child_process');
const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
console.log('npm版本:', npmVersion);

// 检查依赖
const packageJson = require('./package.json');
console.log('\n=== 依赖检查 ===');

try {
  const nodeModulesExists = fs.existsSync(path.join(__dirname, 'node_modules'));
  console.log('node_modules:', nodeModulesExists ? '✅ 存在' : '❌ 不存在');
  
  if (nodeModulesExists) {
    console.log('依赖状态: ✅ 已安装');
  } else {
    console.log('依赖状态: ❌ 需要运行 npm install');
  }
} catch (error) {
  console.log('依赖检查错误:', error.message);
}

console.log('\n=== 环境变量检查 ===');
const envFiles = ['.env.development', '.env.production', '.env.internet', '.env.intranet'];
envFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`${file}:`, exists ? '✅ 存在' : '❌ 不存在');
});
```

#### 10.2 运行检查
```bash
node env-check.js
```

### 11. 常见问题解决

#### 11.1 依赖安装失败
```bash
# 清除缓存
npm cache clean --force

# 重新安装
rm -rf node_modules package-lock.json
npm install

# 使用内网镜像
npm install --registry=http://10.10.1.68:8082/repository/npm-all/
```

#### 11.2 端口占用
```bash
# 查看端口占用
lsof -i :9527

# 终止占用进程
kill -9 [PID]

# 或修改端口
# 修改 vue.config.js 中的 port 配置
```

#### 11.3 权限问题
```bash
# macOS/Linux 权限问题
sudo npm install -g @vue/cli

# Windows 管理员权限运行
# 右键 PowerShell -> 以管理员身份运行
```

#### 11.4 网络连接问题
```bash
# 测试内网仓库连接
curl -I http://10.10.1.68:8082/repository/npm-all/

# 测试内网下载
npm view @picc/verifition version --registry=http://10.10.1.68:8082/repository/npm-all/
```

### 12. 性能优化建议

#### 12.1 开发环境优化
```bash
# 使用淘宝镜像
npm config set registry https://registry.npmmirror.com

# 使用yarn（可选）
npm install -g yarn
yarn install
```

#### 12.2 构建优化
```bash
# 使用happypack加速构建
npm install --save-dev happypack

# 使用thread-loader
npm install --save-dev thread-loader
```

### 13. 安全检查清单

#### 13.1 开发安全检查
- [ ] Node.js版本符合要求
- [ ] npm仓库地址正确
- [ ] 依赖版本无高危漏洞
- [ ] 环境变量配置完整
- [ ] 代理配置正确

#### 13.2 生产环境检查
- [ ] 生产环境变量配置
- [ ] 构建输出检查
- [ ] 静态资源CDN配置
- [ ] 错误监控配置
- [ ] 性能监控配置