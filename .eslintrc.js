module.exports = {
    // 使用的扩展库
    extends: [ "plugin:vue/recommended"],
    // 解析器配置
    parser: 'vue-eslint-parser',
    parserOptions: {
        sourceType: 'module'
    },
    // 可以全局使用变量
    globals: {},
    // 第三方插件
    plugins: ["vue","html"],
    // 规则配置
    rules: {
        "no-console": process.env.NODE_ENV === "production" ? "error" : "off",
        "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
        // 关闭-函数括号前的空格验证
        "space-before-function-paren": 0,
        // 使用分号
        semi: ["error", "always"],
        // 缩进使用4个空格
        indent: ["error", 4, { SwitchCase: 1, MemberExpression: "off" }],
        // 关闭-字符串统一使用单引号
        quotes: 0,
        // 避免使用常量作为条件表达式的条件 - warn
        "no-constant-condition": 1,
        // 不要扩展原生对象 - warn
        "no-extend-native": 1,
        // 避免不必要的布尔转换 - warn
        "no-extra-boolean-cast": 1,
        // 不要省去小数点前面的0 - warn
        "no-floating-decimal": 1,
        // new 创建对象实例后需要赋值给变量 - warn
        "no-new": 1,

        // vue文件缩进为4
        "vue/html-indent": ["warn", 4],
        // 允许对HTML void元素进行自动关闭
        "vue/html-self-closing": [
            "error",
            {
                html: {
                    void: "always",
                    normal: "never",
                    component: "always"
                },
                svg: "always",
                math: "always"
            }
        ],
        // 限制每行的最大属性数以提高可读性
        // (prettier会对单行超过80个字符后自动进行格式化，改为属性折行)
        "vue/max-attributes-per-line": 0,
        // template中使用组件遵循 kebab-case 约定
        "vue/component-name-in-template-casing": ["warn", "kebab-case"],
        // vue文件中name属性不能为单个单词问题
        "vue/multi-word-component-names": ["off"]
    }
};
