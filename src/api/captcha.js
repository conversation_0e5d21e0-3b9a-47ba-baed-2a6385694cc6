import request from "@/utils/request";
// import store from "@/store";
import qs from "qs";

// 初始化
export function queryCaptchaApi(query) {
    // store.dispatch("user/setquery", "fromData");
    return request({
        url: "/apicaptcha/captcha/generate",
        method: "GET",
        responseType: "arraybuffer",//或者是blob
        params: query
    });
}
// 校验填入的验证码值
export function checkResultApi(query) {
    // store.dispatch("user/setquery", "fromData");
    return request({
        url: "/apicaptcha/captcha/verifyCaptchaCode",
        method: "GET",
        params: query
    });
}
//登陆后请求接口，过一段时间弹出验证码
export function apicaptchaIsLogin() {
    return request({
        url: "/apicaptcha/mn/isLogin",
        method: "GET"
    });
}
//验证码续期
// 校验填入的验证码值
export function apicaptchaIsTimeOut(query) {
    return request({
        url: "/apicaptcha/mn/isTimeOut",
        method: "GET",
        params: { expire: query }
    });
}