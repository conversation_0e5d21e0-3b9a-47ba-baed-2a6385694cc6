import { getStorage } from "@/utils/storage.js";
import settings from "@/settings";
export function doLogin() {
    // console.log(data)
    return new Promise((resolve, reject) => {
        resolve({
            username: "<PERSON>",
            token: Math.random(),
            userId: 1000001
        });
    });
}
/**
 * getRoutes 方法模拟api接口请求
 * 实际开发后可以将相关删除
 */
export function getRoutes() {
    // return axios()
    return new Promise((resolve, reject) => {
        // TODO
        // wwr TODO
        if (getStorage("token") || (!settings.loginActive && settings.loginInfo)) {
            // console.log('routes存过token');
            resolve([
                {
                    routePath: "/demo",
                    filePath: "/layout",
                    cacheViewName: "",
                    menuName: "示例111",
                    name: "demo",
                    image: "education",
                    childMenu: [
                        // {
                        //     routePath: "nav",
                        //     filePath: "/nav",
                        //     cacheViewName: "",
                        //     menuName: "二级导航",
                        //     image: "",
                        //     name: "labels",
                        //     childMenu: [
                        // {
                        //     routePath: "path1",
                        //     filePath: "/nav/navd/nav1",
                        //     cacheViewName: "",
                        //     menuName: "三级导航1",
                        //     image: "",
                        //     name: "labels1",
                        //     childMenu: [],
                        //     hidden: false,
                        //     alwaysShow: true,
                        //     bg: "grey"
                        // },
                        // {
                        //     routePath: "path2",
                        //     filePath: "/nav/navd/nav2",
                        //     cacheViewName: "",
                        //     menuName: "三级导航2",
                        //     image: "",
                        //     name: "labels2",
                        //     childMenu: [],
                        //     hidden: false,
                        //     alwaysShow: true,
                        //     bg: "grey"
                        // }
                        //     ],
                        //     hidden: false,
                        //     alwaysShow: true,
                        //     bg: "grey"
                        // },
                        {
                            routePath: "labels",
                            filePath: "/demo/labels",
                            cacheViewName: "",
                            menuName: "标签",
                            image: "",
                            name: "Labels",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true,
                            bg: "grey"
                        },
                        {
                            routePath: "tabs",
                            filePath: "/demo/tabs",
                            cacheViewName: "",
                            menuName: "tab切换",
                            image: "",
                            name: "Tabs",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "anchor",
                            filePath: "/demo/anchor",
                            cacheViewName: "",
                            menuName: "锚点定位器",
                            image: "",
                            name: "Anchor",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "bottombar",
                            filePath: "/demo/bottombar",
                            cacheViewName: "",
                            menuName: "底部导航栏",
                            image: "",
                            name: "bottombar",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "handentry",
                            filePath: "/demo/handentry",
                            cacheViewName: "",
                            menuName: "快捷入口",
                            name: "Handentry",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "todolist",
                            filePath: "/demo/todolist",
                            cacheViewName: "",
                            menuName: "任务列表",
                            name: "Todolist",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "calendar",
                            filePath: "/demo/calendar",
                            cacheViewName: "",
                            menuName: "日历",
                            name: "CalendarDemo",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "upload",
                            filePath: "/demo/upload",
                            cacheViewName: "",
                            menuName: "上传",
                            name: "Upload",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        // {
                        //     routePath: 'statictics',
                        //     filePath: '/demo/statictics',
                        //     cacheViewName: '',
                        //     menuName: '图表统计',
                        //     image: '',
                        //     childMenu: [],
                        //     hidden: false,
                        //     alwaysShow: true
                        // },
                        {
                            routePath: "list",
                            filePath: "/demo/list",
                            cacheViewName: "",
                            menuName: "列表",
                            name: "List",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true,
                            bg: "grey"
                        },
                        {
                            routePath: "titlebar",
                            filePath: "/demo/titlebar",
                            cacheViewName: "",
                            menuName: "通屏标题栏",
                            image: "",
                            name: "Titlebar",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "internalcard",
                            filePath: "/demo/internalcard",
                            cacheViewName: "",
                            menuName: "内部卡片标题",
                            image: "",
                            name: "Internalcard",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "button",
                            filePath: "/demo/button",
                            cacheViewName: "",
                            menuName: "按钮组",
                            image: "",
                            name: "Button",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true,
                            bg: "grey"
                        },
                        {
                            routePath: "card",
                            filePath: "/demo/card",
                            cacheViewName: "",
                            menuName: "卡片",
                            image: "",
                            name: "CardDemo",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "form",
                            filePath: "/demo/form/index",
                            cacheViewName: "",
                            menuName: "表单",
                            image: "",
                            name: "Form",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "message",
                            filePath: "/demo/message",
                            cacheViewName: "",
                            menuName: "系统消息",
                            image: "",
                            name: "Message",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "feedback",
                            filePath: "/demo/feedback",
                            cacheViewName: "",
                            menuName: "页面操作反馈",
                            image: "",
                            name: "Feedback",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "loading",
                            filePath: "/demo/loading",
                            cacheViewName: "",
                            menuName: "加载",
                            image: "",
                            name: "Loading",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "alert",
                            filePath: "/demo/alert",
                            cacheViewName: "",
                            menuName: "对话框",
                            image: "",
                            name: "Alert",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "modal",
                            filePath: "/demo/modal",
                            cacheViewName: "",
                            menuName: "模态框",
                            image: "",
                            name: "Modal",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "result",
                            filePath: "/demo/result",
                            cacheViewName: "",
                            menuName: "结果页",
                            image: "",
                            name: "ResultDemo",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "steps",
                            filePath: "/demo/steps",
                            cacheViewName: "",
                            menuName: "步骤导航",
                            image: "",
                            name: "Steps",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "payment",
                            filePath: "/demo/payment",
                            cacheViewName: "",
                            menuName: "支付",
                            image: "",
                            name: "Payment",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        // {
                        //     routePath: 'chart',
                        //     filePath: '/demo/chart',
                        //     cacheViewName: '',
                        //     menuName: 'chart',
                        //     image: '',
                        //     childMenu: [],
                        //     hidden: false,
                        //     alwaysShow: true
                        // },
                        {
                            routePath: "table",
                            filePath: "/demo/table",
                            cacheViewName: "",
                            menuName: "表格",
                            image: "",
                            name: "Table",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "tooltip",
                            filePath: "/demo/tooltip",
                            cacheViewName: "",
                            menuName: "气泡提示",
                            image: "",
                            name: "Tooltip",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "preview",
                            filePath: "/demo/preview",
                            cacheViewName: "",
                            menuName: "图片预览",
                            image: "",
                            name: "Preview",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "lang",
                            filePath: "/demo/i18n",
                            cacheViewName: "",
                            menuName: "国际化",
                            image: "",
                            name: "Language",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "editor",
                            filePath: "/demo/editor",
                            cacheViewName: "",
                            menuName: "富文本编辑器",
                            image: "",
                            name: "Editor",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "searchModel",
                            filePath: "/demo/searchModel",
                            cacheViewName: "",
                            menuName: "搜索弹窗",
                            image: "",
                            name: "SearchModel",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "submitfailed",
                            filePath: "/demo/Submitfailed",
                            cacheViewName: "",
                            menuName: "报错框",
                            image: "",
                            name: "Submitfailed",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "pagination",
                            filePath: "/demo/pagination",
                            cacheViewName: "",
                            menuName: "分页",
                            image: "",
                            name: "Pagination",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "watermark",
                            filePath: "/demo/watermark",
                            cacheViewName: "",
                            menuName: "水印",
                            image: "",
                            name: "watermark",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        // {
                        //     routePath: 'flowchart',
                        //     filePath: '/demo/flowchart',
                        //     cacheViewName: '',
                        //     menuName: 'flowchart',
                        //     image: '',
                        //     childMenu: [],
                        //     hidden: false,
                        //     alwaysShow: true
                        // },
                        // {
                        //     routePath: 'defaultpage',
                        //     filePath: '/demo/defaultpage',
                        //     cacheViewName: '',
                        //     menuName: '',
                        //     image: '',
                        //     childMenu: [],
                        //     hidden: false,
                        //     alwaysShow: true
                        // }
                        {
                            routePath: "printTest",
                            filePath: "/demo/print",
                            cacheViewName: "",
                            menuName: "打印测试",
                            image: "",
                            name: "PrintTest",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "testUtilsFunction",
                            filePath: "/demo/testUtilsFunction",
                            cacheViewName: "",
                            menuName: "公共方法测试",
                            image: "",
                            name: "TestUtilsFunction",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "textarea",
                            filePath: "/demo/textarea",
                            cacheViewName: "",
                            menuName: "多行文本输入框",
                            image: "",
                            name: "Textarea",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "pageTemplate",
                            filePath: "/demo/pageTemplate",
                            cacheViewName: "",
                            menuName: "页面模版",
                            image: "",
                            name: "PageTemplate",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "captchaPage",
                            filePath: "/demo/captcha",
                            cacheViewName: "",
                            menuName: "登录验证码",
                            image: "",
                            name: "captchaPage",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "WPSOnline",
                            filePath: "/demo/WPSOnline",
                            cacheViewName: "",
                            menuName: "wps在线文档",
                            image: "",
                            name: "WPSOnline",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        }
                    ],
                    hidden: false,
                    alwaysShow: true
                },
                {
                    routePath: "/robotDemo",
                    filePath: "/layout",
                    cacheViewName: "",
                    menuName: "请求示例",
                    image: "education",
                    hidden: false,
                    name: "robetDemo",
                    alwaysShow: true,
                    childMenu: [
                        {
                            routePath: "/robots",
                            // filePath: "/robotDemo/robots",
                            filePath: "/SubMenuContainer",
                            cacheViewName: "",
                            menuName: "robots",
                            image: "education",
                            hidden: false,
                            name: "robots",
                            alwaysShow: true,
                            childMenu: [
                                {
                                    routePath: "robot",
                                    filePath: "/robotDemo/robots/robot/robot_list",
                                    cacheViewName: "",
                                    menuName: "robot",
                                    name: "robot",
                                    image: "",
                                    childMenu: [],
                                    hidden: false,
                                    alwaysShow: true
                                }
                            ]
                        }
                    ]
                },
                {
                    routePath: "/paasPortal/test",
                    // filePath: "/layout",
                    cacheViewName: "SUB--1",
                    menuName: "SUB--1",
                    image: "education",
                    hidden: false,
                    name: "SUB--1",
                    alwaysShow: true,
                    childMenu: [
                        {
                            routePath: "dashboard",
                            // filePath: "/robotDemo/robots/robot/robot_list",
                            cacheViewName: "SUB--1-dashboard",
                            menuName: "dashboard",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "demo/labels",
                            // filePath: "/robotDemo/robots/robot/robot_list",
                            cacheViewName: "SUB--1-labels",
                            menuName: "标签",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "robetDemo/robots",
                            // filePath: "/robotDemo/robots/robot/robot_list",
                            cacheViewName: "SUB--1-robots",
                            menuName: "robots",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        }
                    ]
                },
                {
                    routePath: "/paasPortal/demo",
                    // filePath: "/layout",
                    cacheViewName: "SUB--2",
                    menuName: "SUB--2",
                    image: "education",
                    hidden: false,
                    name: "SUB--2",
                    alwaysShow: true,
                    childMenu: [
                        {
                            routePath: "dashboard",
                            // filePath: "/robotDemo/robots/robot/robot_list",
                            cacheViewName: "SUB--2-labels",
                            menuName: "dashboard",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "demo/labels",
                            // filePath: "/robotDemo/robots/robot/robot_list",
                            cacheViewName: "SUB--1-dashboard",
                            menuName: "标签",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        },
                        {
                            routePath: "robetDemo/robots",
                            // filePath: "/robotDemo/robots/robot/robot_list",
                            cacheViewName: "SUB--1-robots",
                            menuName: "robots",
                            image: "",
                            childMenu: [],
                            hidden: false,
                            alwaysShow: true
                        }
                    ]
                }
            ]);
        } else {
            // console.log('routes没有token');
            resolve([]);
        }
    });
}

export function getUserInfo() {
    return new Promise((resolve, reject) => {
        // TODO
        // wwr TODO
        if (getStorage("token")) {
            // console.log('userinfo存过token');
            resolve({
                username: "test",
                id: "test"
            });
        } else {
            // console.log('userinfo没有token');
            resolve(null);
        }
    });
}
