import request from "@/utils/request";
// import store from "@/store";
import qs from "qs";

// 查询列表
export function queryRobotList(query) {
    // store.dispatch("user/setquery", "fromData");
    return request({
        url: "/api/misc/robot/demo/search",
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded"
        },
        data: qs.stringify(query)
    });
}
// 添加
export function addRobot(query) {
    return request({
        url: "/api/misc/robot/demo/create",
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        data: query
    });
}
// 删除
export function deleteRobot(query) {
    return request({
        url: "/api/misc/robot/demo/delete/" + query,
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        }
    });
}
// 编辑
export function editRobot(query) {
    return request({
        url: "/api/misc/robot/demo/update",
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        data: query
    });
}
// 查询功能树
export function findTaskByRobotCode(query) {
    return request({
        url: "/api/misc/robot/demo/select/" + query,
        method: "GET"
    });
}
// 模拟搜索弹窗接口
export function getBatch() {
    return request({
        url: "http://10.10.0.153:7300/mock/5dce72a5da38b068580a70c3/api/ceshi",
        method: "GET"
    });
}
export function getBatch1() {
    return request({
        url: "http://10.10.0.153:7300/mock/5dce72a5da38b068580a70c3/api/batchList",
        method: "GET"
    });
}
