<template>
    <div>
        <navbar :person-info="info" :new-info="newInfo" :is-fixed="false" :work-state="workState" @changeStateBack="changeState" @loginOutBack="loginOut">
            <div slot="crumb">
                <!-- 在此选择面包屑或者切换导航 -->
                <el-breadcrumb separator="/" style="line-height: 56px">
                    <el-breadcrumb-item :to="{ path: '/' }">
                        首页
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>{{ breadValue }}</el-breadcrumb-item>
                </el-breadcrumb>
            </div>
            <template slot="search-input">
                <el-input v-model="test" placeholder="保单/车型/支付投保单/车险报价等关键词" prefix-icon="el-icon-search" clearable />
            </template>
            <template slot="dropdown-menu-list">
                <a href="#">查看登录日志</a>
                <a @click="toggleSideBarManualStatus()"> {{ manualSideBar ? "自动" : "手动" }}菜单 </a>
                <a @click="getCookie('get')"> 获取cookie </a>
                <a @click="setCookie('set')"> 设置cookie </a>
                <a @click="deleteCookie('delete')"> 删除cookie </a>
                <a href="#">其他</a>
            </template>
            <div slot="bell-message" style="padding: 0 20px 20px 20px">
                <el-tabs v-model="activeName" :stretch="true" class="bell-info">
                    <el-tab-pane label="案件2" name="first">
                        案件描述
                    </el-tab-pane>
                    <el-tab-pane label="业绩1" name="second">
                        业绩描述
                    </el-tab-pane>
                    <el-tab-pane label="公告1" name="third">
                        公告描述
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div slot="bell-operation" style="height: 48px">
                <div class="operation-btn">
                    全部已读
                </div>
                <div class="operation-btn operation-btn-right">
                    查看全部
                </div>
            </div>
        </navbar>
        <div>
            <el-dialog title="主题颜色选择" :visible.sync="centerDialogVisible" :modal-append-to-body="false" width="880px" center>
                <div>
                    <div
                        v-for="(item, index) in themeList"
                        :key="index"
                        style="
                            z-index: 100;
                            overflow: hidden;
                            width: 200px;
                            height: 100px;
                            float: left;
                            margin: 30px;
                            border: 1px solid #eeedeb;
                            border-radius: 9px;
                            cursor: pointer;
                        "
                        :style="selectedInColor === item.inColor ? 'border:1px solid #0358EA !important;' : ''"
                        @click="clickThemeColor(item)"
                    >
                        <div :style="'width:100%;height:70px;background:red;background:' + item.inColor + ';'">
                            <!-- <el-checkbox :label="item.color" /> -->
                            <i
                                v-if="selectedInColor === item.inColor"
                                class="el-icon-success"
                                style="color: #0358ea; position: relative; top: 10px; left: 170px; font-size: 20px"
                            ></i>
                        </div>
                        <div style="width: 100%; height: 30px; background: gray; color: #ffffff; text-align: center; line-height: 30px">
                            <span>{{ item.name }}</span>
                        </div>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer" style="">
                    <!-- <el-button @click="centerDialogVisible = false">取 消</el-button> -->
                    <el-button type="primary" @click="clickChangeColor">确 定</el-button>
                </span>
            </el-dialog>
            <el-dialog title="cookie操作" :visible.sync="dialogFormVisible" :modal-append-to-body="false" width="880px" center>
                <el-form :model="form">
                    <el-form-item label="key">
                        <el-input v-model="form.key" autocomplete="off" />
                    </el-form-item>
                    <el-form-item v-if="dialogType === 'set'" label="value">
                        <el-input v-model="form.value" autocomplete="off" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">
                        取 消
                    </el-button>
                    <el-button type="primary" @click="operateCookie">
                        确 定
                    </el-button>
                </div>
            </el-dialog>
            <!-- 退出登录对话框 -->
            <el-dialog title="提示" :visible.sync="logoutDialogVisible" :modal-append-to-body="false" width="360px" height="180px">
                <p style="display: flex; align-items: center">
                    <i class="picc-icon picc-icon-warning-sm login-out-icon"></i>
                    <span style="color: #292b34">&nbsp;您是否确定退出当前账号</span>
                </p>
                <div style="text-align: right;margin-bottom: 8px;">
                    <el-button round size="mini" @click="logoutDialogVisible = false">
                        取消
                    </el-button>
                    <el-button round type="primary" size="mini" @click="logout">
                        确定退出
                    </el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import navbar from "@/components/Navbar";
import settings from "@/settings";
import Cookies from "js-cookie";
import store from "@/store";
export default {
    name: "AppHeader",
    components: {
        navbar
    },
    data() {
        return {
            logoutDialogVisible: false,
            dialogType: "set",
            dialogFormVisible: false,
            form: {
                key: "",
                value: ""
            },
            loginActive: settings.loginActive,
            leftData: ["管理工作台", "理赔工作台", "出台工作台"],
            selectedInColor: Cookies.get("inThemeColor") || "#C6C6C6",
            selectedOutColor: Cookies.get("themeColor") || "",
            checkedColors: [],
            themeList: [
                {
                    name: "灰色（默认）",
                    inColor: "#E8E7E4",
                    outColor: "#C6C6C6"
                },
                {
                    name: "海天蓝色",
                    inColor: "#EBF6FF",
                    outColor: "#A9C5DC"
                },
                {
                    name: "豆沙绿色",
                    inColor: "#E5EFE4",
                    outColor: "#C9E0CF"
                },
                {
                    name: "胭脂红色",
                    inColor: "#FDE6D0",
                    outColor: "#DECAC5"
                },
                {
                    name: "白色",
                    inColor: "#FFFFFF",
                    outColor: "#C1C5D9"
                },
                {
                    name: "杏仁黄色",
                    inColor: "#FFF2E2",
                    outColor: "#E2CCAE"
                }
            ],
            centerDialogVisible: false,
            info: {
                name: "孙大圣",
                position: "部门管理",
                state: true,
                picture: require("@/assets/tangqing.gif")
            },
            newInfo: true,
            workState: [
                { select: true, label: "上班" },
                { select: false, label: "下班" },
                { select: false, label: "小休" },
                { select: false, label: "离线" }
            ],
            test: "",
            activeName: "first"
        };
    },
    computed: {
        manualSideBar() {
            return this.$store.state.app.manualSideBar;
        },
        breadValue() {
            return this.$route.meta.title;
        }
    },
    methods: {
        // 颜色选择器改变事件
        changeTheme(val) {
            this.$store.dispatch("app/setThemeColor", val);
            this.$store.dispatch("app/setTest", val);
        },
        loginOut(msg) {
            console.log(msg);
            this.logoutDialogVisible = true;
        },
        changeState(index) {
            var _this = this;
            // 模拟异步
            setTimeout(function () {
                _this.workState = _this.workState.map((v, i) => {
                    v.select = i === index;
                    return v;
                });
            }, 500);
        },
        selectThemeColor() {
            this.centerDialogVisible = true;
            // console.log(this.centerDialogVisible);
        },
        handleCheckedCitiesChange(val) {
            // console.log(val);
        },
        clickThemeColor(item) {
            // this.$store.dispatch('app/setThemeColor', item.color);
            this.selectedInColor = item.inColor;
            this.selectedOutColor = item.outColor;
        },
        clickChangeColor() {
            this.$store.dispatch("app/setInThemeColor", this.selectedInColor);
            this.$store.dispatch("app/setOutThemeColor", this.selectedOutColor);
            Cookies.set("inThemeColor", this.selectedInColor);
            Cookies.set("outThemeColor", this.selectedOutColor);
            document.getElementsByTagName("body")[0].style.backgroundColor = this.$store.state.app.outThemeColor;
            this.centerDialogVisible = false;
        },
        leftDataIndex(e) {
            // console.log("点击角色切换导航", e);
        },
        logout() {
            this.logoutDialogVisible = false;
            Cookies.remove("Admin-token");
            Cookies.remove("gray_version");
            Cookies.remove("other-token");
            store.dispatch("user/clearUserInfo");
            window.localStorage.removeItem("userInfo");
            window.localStorage.removeItem("token");
            this.$router.push({ path: "/login" });
        },
        isLoginActive() {
            this.loginActive = !this.loginActive;
            settings.loginActive = this.loginActive;
        },
        toggleSideBarManualStatus() {
            this.$store.dispatch("app/toggleManualSideBar", !this.manualSideBar);
        },
        getCookie(type) {
            this.dialogType = type;
            this.dialogFormVisible = true;
        },
        setCookie(type) {
            this.dialogType = type;
            this.dialogFormVisible = true;
        },
        deleteCookie(type) {
            this.dialogType = type;
            this.dialogFormVisible = true;
        },
        operateCookie() {
            switch (this.dialogType) {
                case "get":
                    alert(Cookies.get(this.form.key));
                    this.dialogFormVisible = false;
                    break;
                case "delete":
                    Cookies.remove(this.form.key);
                    this.dialogFormVisible = false;
                    break;
                default:
                    Cookies.set(this.form.key, this.form.value);
                    this.dialogFormVisible = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.el-breadcrumb {
    line-height: 50px;
}
::v-deep .el-breadcrumb__separator {
    margin: 0;
}
::v-deep span.el-input__suffix .el-input__suffix-inner .el-input__icon {
    line-height: 32px;
    padding: 4px 0;
}
.bell-info {
    ::v-deep .el-tabs__header {
        width: 288px;
        margin-left: 21px;
        margin-top: 10px;
    }
    ::v-deep .el-tabs__nav-wrap::after {
        background-color: transparent;
    }
    ::v-deep .el-tabs__item:hover {
        color: #303133;
    }
    ::v-deep .el-tabs__item.is-active {
        color: #303133;
        font-weight: bold;
    }
    ::v-deep .el-tabs__active-bar {
        background-color: #f05b50;
        padding: 0px 36px;
        background-clip: content-box;
        bottom: 5px;
        height: 2px;
    }

    ::v-deep .el-tabs__item {
        padding: 0;
    }
}
.operation-btn {
    height: 100%;
    width: 50%;
    float: left;
    font-family: "Microsoft YaHei";
    font-size: 14px;
    color: #1c61fc;
    text-align: center;
    line-height: 48px;
    border-top: 1px solid #e2e2e2;
    cursor: pointer;
}
.operation-btn-left {
    border-right: 1px solid #e2e2e2;
}
.operation-btn-right {
    border-left: 1px solid #e2e2e2;
}
.navbar-search .el-input__inner {
    color: #292b34;
}
</style>
