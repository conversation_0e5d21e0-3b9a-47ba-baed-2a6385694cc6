<template>
    <div class="list-wrap">
        <span style="position:absolute;left:90px; top:25px; font-size:12px">2条未读</span>
        <div class="list-item">
            <div class="item-title">
                <h2>我是标题</h2>
            </div>
            <div class="item-con">
                我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容
            </div>
        </div>
        <div class="list-item-info">
            <div class="item-info-title">
                <h2>我是标题我是标题我是标题我不能超过15个子</h2>
                <p>11月11日</p>
            </div>
            <div class="item-info-con">
                我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容
            </div>
        </div>
        <div class="list-item-info">
            <div class="item-info-title no-icon">
                <h2>我是标题</h2>
                <p>11月11日</p>
            </div>
            <div class="item-info-con">
                我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容我是内容
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.list-wrap {
    padding: 0 20px;
    .list-item {
        border-bottom: 1px solid #dcdcdc;
        position: relative;
        .item-title {
            margin-top: 16px;
            height: 16px;
            h2 {
                font-size: 16px;
                margin: 0;
            }
        }
        .item-con {
            color: #65677a;
            font-size: 14px;
            padding: 12px 0 16px 0;
        }
    }
    .list-item-info {
        border-bottom: 1px solid #dcdcdc;
        position: relative;
        padding-left: 20px;
        .item-info-title {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            height: 16px;
            &::before {
                width: 8px;
                height: 8px;
                display: block;
                content: "";
                background: red;
                border-radius: 4px;
                position: absolute;
                left: 0;
            }
            &.no-icon::before {
                display: none;
            }
            h2 {
                color: #292b34;
                font-size: 14px;
                margin: 0;
                width: 224px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            p {
                color: #929292;
                font-size: 12px;
                margin: 0;
            }
        }
        .item-info-con {
            color: #65677a;
            font-size: 14px;
            padding: 12px 0 16px 0;
        }
    }
}
</style>
