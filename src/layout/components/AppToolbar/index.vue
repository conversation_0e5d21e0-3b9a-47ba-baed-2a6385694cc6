<template>
    <div id="toolbar_container">
        <toolbar
            ref="templateToolbar"
            :all-tools="toolDataArray"
            :had-tools="hadToolDataArray"
            :top="isTagsView ? '90px' : '56px'"
            :tool-drawer-top="isTagsView ? '90px' : '56px'"
            right="auto"
            @saveHadTools="saveHadTools"
        >
            <tool-container this-option-name="toolForm">
                <toolForm />
            </tool-container>
            <tool-container this-option-name="toolStepts">
                <toolStepts />
            </tool-container>
            <tool-container this-option-name="list">
                <list />
            </tool-container>
            <tool-container this-option-name="conversation">
                <conversation />
            </tool-container>
            <tool-container this-option-name="mesageView">
                <mesageView />
            </tool-container>
            <tool-container this-option-name="test1">
                <test1 />
            </tool-container>
            <tool-container this-option-name="test2">
                <test2 />
            </tool-container>
        </toolbar>
    </div>
</template>
<script>
import settings from "@/settings";
import toolbar from "@/components/Toolbar";
import toolForm from "./toolForm";
import toolStepts from "./toolStepts";
import list from "./list";
import conversation from "./conversation";
import mesageView from "./messageView";
import test1 from "./test1";
import test2 from "./test2";
export default {
    name: "AppToolbar",
    components: {
        toolbar,
        toolForm,
        toolStepts,
        list,
        conversation,
        mesageView,
        test1,
        test2
    },
    data() {
        return {
            // 根据src/settings中的isTagsBar的值确定标签栏的有无，从而影响工具栏的top的样式
            isTagsView: settings.isTagsBar,
            isMenuFirstOpen: true,
            toolDataArray: [
                {
                    index: 1,
                    groupTitle: "常用工具1",
                    children: [
                        { index: 2, icon: "edit", title: "输入型", name: "toolForm" },
                        { index: 3, icon: "form", title: "时间线", name: "toolStepts" },
                        { index: 4, icon: "chart", title: "列表型", name: "list" },
                        { index: 5, icon: "component", title: "对话型", name: "conversation" },
                        { index: 6, icon: "edit", title: "预览型", name: "mesageView" },
                        { index: 7, icon: "email", title: "输入型", name: "test1" },
                        { index: 8, icon: "example", title: "example", name: "test2" },
                        { index: 9, icon: "form", title: "form", name: "" },
                        { index: 37, icon: "form", title: "form", name: "" }
                    ]
                },
                {
                    index: 10,
                    groupTitle: "常用工具2",
                    children: [
                        { index: 11, icon: "404", title: "404", name: "" },
                        { index: 12, icon: "bug", title: "bug", name: "" },
                        { index: 13, icon: "chart", title: "chart", name: "" },
                        { index: 14, icon: "component", title: "com", name: "" },
                        { index: 15, icon: "edit", title: "edit", name: "" }
                    ]
                },
                {
                    index: 27,
                    groupTitle: "常用工具2",
                    children: [{ index: 19, icon: "404", title: "404", name: "" }]
                },
                {
                    index: 28,
                    groupTitle: "常用工具2",
                    children: [
                        { index: 29, icon: "404", title: "404", name: "" },
                        { index: 30, icon: "bug", title: "bug", name: "" },
                        { index: 31, icon: "chart", title: "chart", name: "" },
                        { index: 32, icon: "component", title: "com", name: "" },
                        { index: 33, icon: "edit", title: "edit", name: "" },
                        { index: 34, icon: "email", title: "email", name: "" },
                        { index: 35, icon: "example", title: "example", name: "" },
                        { index: 36, icon: "form", title: "form", name: "" }
                    ]
                }
            ],
            hadToolDataArray: [
                { index: 2, icon: "edit", title: "输入型", name: "toolForm" },
                { index: 3, icon: "form", title: "时间线", name: "toolStepts" },
                { index: 4, icon: "chart", title: "列表型", name: "list" },
                { index: 5, icon: "component", title: "对话型", name: "conversation" },
                { index: 6, icon: "edit", title: "信息预览型", name: "mesageView" },
                { index: 7, icon: "email", title: "输入型", name: "test1" },
                { index: 8, icon: "example", title: "example", name: "test2" }
            ],
            toolbarSpread: false
        };
    },
    methods: {
        saveHadTools(tools) {
            this.hadToolDataArray=[...tools];
            // console.log(tools, ".............tools....................");
        }
    }
};
</script>
<style lang="scss" scoped>
#toolbar_container {
    background: #fafafa;
    box-shadow: inset 1px 0 0 0 #e2e2e2;
}
</style>
