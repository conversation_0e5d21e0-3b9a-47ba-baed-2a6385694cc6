<template>
    <div style="padding: 0 20px;">
        <el-timeline class="picc-steps-vertical">
            <el-timeline-item icon="picc-icon picc-icon-circle-o">
                <ToolbarFlow title="sdfsdfsdf" description="待支付" title-color="#dc402b" />
            </el-timeline-item>
            <el-timeline-item icon="picc-icon picc-icon-success-mini">
                <ToolbarFlow title="sdfsdfsdf" description="sdfsdfsdsdfsdfdsf">
                    <div class="info-wrap">
                        <div class="info-title">
                            <p>张三</p>
                            <p>2020/11/11 11:11:11</p>
                        </div>
                        <div class="info-text">
                            这里是文本这里是文本这里是文本这里是文本这里是文本这里是文本这里是文本这里是文本
                        </div>
                    </div>
                </ToolbarFlow>
            </el-timeline-item>
            <el-timeline-item icon="picc-icon picc-icon-success-mini">
                <ToolbarFlow title="sdfsdfsdf" description="sdfsdfsdsdfsdfdsf">
                    <div class="info-wrap">
                        <div class="info-title">
                            <p>张三</p>
                            <p>2020/11/11 11:11:11</p>
                        </div>
                    </div>
                </ToolbarFlow>
            </el-timeline-item>
        </el-timeline>
    </div>
</template>
<style lang="scss" scoped>
::v-deep .picc-steps-vertical.el-timeline .el-timeline-item .el-timeline-item__content {
    border-bottom: 0 !important;
    // padding: 16px 0;
}
.picc-steps-vertical {
    .info-wrap {
        padding-top: 16px;
        flex-direction: column;
        .info-title {
            width: 100%;
            display: flex;
            flex-direction: row;
            font-size: 12px;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .info-text {
            width: 100%;
            padding: 12px;
            color: #292b34;
            font-size: 12px;
            background: #efefef;
            border-radius: 5px;
        }
    }
}
</style>
