<template>
    <div class="messageview">
        <el-scrollbar class="messageview-scrollbar" :horizon="false">
            <div v-for="(items, index) in dataList" :key="index" class="messageview-container">
                <div class="messageview-container-title">
                    {{ items.title }}
                </div>
                <div v-for="(item, index2) in items.data" :key="index2" class="messageview-container-title-information">
                    <span class="messageview-container-title-information-label">{{ item.label }}</span>
                    <span class="messageview-container-title-information-value">{{ item.value }}</span>
                </div>
            </div>
        </el-scrollbar>
    </div>
</template>

<script>
export default {
    name: "ConversationItem",
    filters: {},
    // props: {
    //     dataList: {
    //         type: Array,
    //         default: () => []
    //     }
    // },
    data() {
        return {
            dataList: [
                {
                    title: "基本数据",
                    data: [
                        {
                            label: "产品名称",
                            value: "金锁家财险"
                        },
                        {
                            label: "产品方案",
                            value: "家安险"
                        },
                        {
                            label: "保险期限方案",
                            value: "2019/03/12 到 2020/03/12"
                        },
                        {
                            label: "操作员",
                            value: "刘芮麟"
                        },
                        {
                            label: "出单机构",
                            value: "11019300 北京分公司营业部"
                        },
                        {
                            label: "签单时间",
                            value: "2019/09/10"
                        }
                    ]
                },
                {
                    title: "保费信息",
                    data: [
                        {
                            label: "投保份数",
                            value: "1份"
                        },
                        {
                            label: "单份保费",
                            value: "￥128.00"
                        },
                        {
                            label: "单份净保费",
                            value: "￥120.78"
                        },
                        {
                            label: "单份税额",
                            value: "￥7.24"
                        },
                        {
                            label: "总保费",
                            value: "￥128.00"
                        }
                    ]
                },
                {
                    title: "客户信息",
                    data: [
                        {
                            label: "关心人类型",
                            value: "个人"
                        },
                        {
                            label: "角色",
                            value: "投保人"
                        },
                        {
                            label: "关系人名称",
                            value: "李四"
                        },
                        {
                            label: "单份税额",
                            value: "￥7.24"
                        },
                        {
                            label: "总保费",
                            value: "￥128.00"
                        }
                    ]
                },
                {
                    title: "基本数据",
                    data: [
                        {
                            label: "产品名称",
                            value: "金锁家财险"
                        },
                        {
                            label: "产品方案",
                            value: "家安险"
                        },
                        {
                            label: "保险期限方案",
                            value: "2019/03/12 到 2020/03/12"
                        },
                        {
                            label: "操作员",
                            value: "刘芮麟"
                        },
                        {
                            label: "出单机构",
                            value: "11019300 北京分公司营业部"
                        },
                        {
                            label: "签单时间",
                            value: "2019/09/10"
                        }
                    ]
                },
                {
                    title: "保费信息",
                    data: [
                        {
                            label: "投保份数",
                            value: "1份"
                        },
                        {
                            label: "单份保费",
                            value: "￥128.00"
                        },
                        {
                            label: "单份净保费",
                            value: "￥120.78"
                        },
                        {
                            label: "单份税额",
                            value: "￥7.24"
                        },
                        {
                            label: "总保费",
                            value: "￥128.00"
                        }
                    ]
                },
                {
                    title: "客户信息",
                    data: [
                        {
                            label: "关心人类型",
                            value: "个人"
                        },
                        {
                            label: "角色",
                            value: "投保人"
                        },
                        {
                            label: "关系人名称",
                            value: "李四"
                        },
                        {
                            label: "单份税额",
                            value: "￥7.24"
                        },
                        {
                            label: "总保费",
                            value: "￥128.00"
                        }
                    ]
                }
            ]
        };
    },
    computed: {}
};
</script>

<style lang="scss" scoped>
.messageview {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 155px);
    width: 100%;
}
::v-deep .messageview-scrollbar .el-scrollbar__wrap {
    margin-bottom: 0px !important;
    overflow-x: hidden;
}
::v-deep .messageview-scrollbar .el-scrollbar__bar {
    margin-right: 6px;
}
.messageview-container {
    display: flex;
    flex-direction: column;
    margin: 16px 20px;
    padding: 16px 24px 8px 24px;
    background: #fafafa;
    border-radius: 5px;
    border: 1px solid #ececec;
}
.messageview-container-title {
    padding-bottom: 16px;
    font-weight: bold;
    font-size: 14px;
    color: #292b34;
    line-height: 20px;
}
.messageview-container-title-information {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    padding-bottom: 12px;
}
.messageview-container-title-information-label {
    font-size: 14px;
    color: #292b34;
}
.messageview-container-title-information-value {
    font-size: 14px;
    color: #65677a;
}
</style>
