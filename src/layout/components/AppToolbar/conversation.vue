<template>
    <div style="height:100%;width:100%;">
        <div style="width:100%;">
            <div style="display: flex;flex-direction:row;justify-content:flex-start;">
                <img width="28px" height="28px" style="margin:12px 12px 12px 20px" :href="require('@/assets/tangqing.gif')" />
                <span style="line-height:52px;margin-right:16px">高级审核&nbsp;&nbsp;&nbsp;&nbsp;范范</span>
                <span style="line-height:52px;color:#1c61fc">更换</span>
            </div>
            <div style="margin:0 20px;padding:16px 20px 16px 16px;background:#F1F2F4;border-radius: 5px;">
                <div style="display: flex;flex-direction:row;justify-content:space-between;height:16px;line-height: 16px;margin-bottom: 12px;">
                    <span style="font-size:14px;font-weight: bold;">货运2019-07-15 火灾，爆炸</span>
                    <div style="font-size:14px;color:red;">
                        待立案
                    </div>
                </div>
                <div style="display: flex;flex-direction:row;justify-content:flex-start;margin-bottom:8px;height: 12px;line-height: 12px;">
                    <span style="font-size:12px;">德邦物流有限公司</span>
                    <div style="font-size:12px;color:red;margin-left:12px;">
                        ★★★
                    </div>
                </div>
                <div style="display: flex;flex-direction:row;justify-content:flex-start;height: 12px;line-height: 12px;">
                    <span style="font-size:12px;">12345678901234567890</span>
                </div>
            </div>
        </div>
        <div style="width:100%;">
            <el-tabs v-model="activeName" class="picc-tabs">
                <el-tab-pane label="未解决" name="first">
                    <online />
                </el-tab-pane>
                <el-tab-pane label="历史问题4" name="second">
                    历史问题&nbsp;&nbsp;4
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script>
import online from "@/components/OnlineConversation";
export default {
    components: {
        online
    },
    data() {
        return {
            activeName: "first"
        };
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
::v-deep .el-tabs__header {
    padding-left: 12px;
    border-bottom: 0.5px solid #e2e2e2;
}

</style>
