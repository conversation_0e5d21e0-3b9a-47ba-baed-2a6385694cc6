<template>
    <div class="form-wrap">
        <div class="search">
            <div class="search-info">
                <el-input v-model="input" placeholder="请输入内容" />
                <el-button type="text">
                    查询
                </el-button>
            </div>
            <el-checkbox v-model="checkValue">
                本人
            </el-checkbox>
            <span @click="retractOrOpen($event)">收起</span>
        </div>
        <el-form ref="formWrap" label-width="100px">
            <el-form-item label="报案号" prop="clusterId">
                <el-input v-model="temp.clusterId" size="small" clearable />
            </el-form-item>
            <el-form-item label="保单号" prop="clusterName">
                <el-input v-model="temp.clusterName" size="small" clearable />
            </el-form-item>
            <el-form-item label="立案号" prop="namespaceName">
                <el-input v-model="temp.namespaceName" size="small" clearable />
            </el-form-item>
            <el-form-item label="险类代码" prop="fileConfigName">
                <el-input v-model="temp.configName" size="small" clearable />
            </el-form-item>
            <el-form-item label="险别代码" prop="remark">
                <el-input v-model="temp.remark" size="small" clearable />
            </el-form-item>
            <el-form-item label="支付对象" prop="region">
                <el-input v-model="temp.region" size="small" clearable />
            </el-form-item>
            <el-form-item label="领款人" prop="applicationId">
                <el-input v-model="temp.applicationId" size="small" clearable />
            </el-form-item>
            <el-button style="margin:24px auto 0; display:block" type="primary" round>
                查询
            </el-button>
        </el-form>
    </div>
</template>
<script>
export default {
    data() {
        return {
            input: "",
            checkValue: "",
            temp: {}
        };
    },
    methods: {
        retractOrOpen(e) {
            const formDom = this.$refs.formWrap;
            formDom.$el.style.display = formDom.$el.style.display == 'none' ? 'block' : 'none';
            e.target.innerText = e.target.innerText == '展开' ? '收起' : '展开';
        }
    }
};
</script>
<style lang="scss" scoped>
.form-wrap {
    padding: 0 20px;
    .search {
        display: flex;
        align-items: center;
        margin: 16px 0;
        width: 100%;
        .search-info {
            flex: 1;
            position: relative;
            .el-input {
                // display: block;
                ::v-deep .el-input__inner {
                    padding: 0 0 0 12px;
                }
            }
            .el-button--text {
                font-size: 12px;
                padding: 8px 12px 8px 8px;
                position: absolute;
                top: 0;
                right: 0;
            }
        }
        span {
            color: #1c61fc;
            font-size: 14px;
            display: block;
            width: 50px;
            text-align: right;
            cursor: pointer;
        }
        .el-checkbox {
            padding-left: 12px;
            ::v-deep .el-checkbox__label {
                padding-left: 8px;
            }
        }
    }
    .el-form-item {
        padding: 5px 8px;
    }
}
</style>
