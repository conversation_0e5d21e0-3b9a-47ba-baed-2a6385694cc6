<template>
    <div class="table-wrap">
        <div class="search">
            <div class="search-info">
                <el-input placeholder="请输入内容" :model="input" />
                <el-button type="text">
                    查询
                </el-button>
            </div>
            <el-checkbox v-model="checkValue">
                本人
            </el-checkbox>
            <span>更多</span>
        </div>
        <div class="table">
            <el-table :data="tableData" height="100%">
                <el-table-column label="赔案名称/计算书号">
                    <template slot-scope="scope">
                        <p class="title">
                            {{ scope.row.first.name }}
                        </p>
                        <p>{{ scope.row.first.no[0]["no1"] }}</p>
                        <p class="ellipsis">
                            {{ scope.row.first.no[1]["no1"] }}
                            <el-tooltip class="item" effect="dark" placement="right">
                                <div v-for="(item, index) in scope.row.first.no" slot="content" :key="index" class="tip-text">
                                    <p>{{ item.name }}:{{ item.no1 }}</p>
                                </div>
                                <span>...</span>
                            </el-tooltip>
                        </p>
                    </template>
                </el-table-column>
                <el-table-column label="领款人/可抵扣金额">
                    <template slot-scope="scope">
                        <p class="title">
                            {{ scope.row.second.name }}
                        </p>
                        <p>{{ scope.row.second.sale }}</p>
                    </template>
                </el-table-column>
                <el-table-column type="selection" width="55" align="center" />
            </el-table>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            input: "",
            checkValue: "",
            tableData: [
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                },
                {
                    first: {
                        name: "货运火灾",
                        no: [
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" },
                            { name: "计算书号", no1: "2345667781292" },
                            { name: "报案号", no1: "2222222" }
                        ]
                    },
                    second: { name: "张三", sale: "20000" }
                }
            ],
            tipData: [{ name: "计算书号", no1: "2345667781292" }, { name: "报案号", no2: "2222222" }]
        };
    }
};
</script>
<style lang="scss" scoped>
.table-wrap {
    // display: flex;
    // flex-direction: column;
    padding: 0 20px;
    .search {
        display: flex;
        align-items: center;
        margin: 16px 0;
        .search-info {
            flex: 1;
            position: relative;
            .el-input {
                // display: block;
                ::v-deep .el-input__inner {
                    padding: 0 0 0 12px;
                }
            }
            .el-button--text {
                font-size: 12px;
                padding: 8px 12px 8px 8px;
                position: absolute;
                top: 0;
                right: 0;
            }
        }
        span {
            color: #1c61fc;
            font-size: 14px;
            display: block;
            width: 60px;
            text-align: right;
            cursor: pointer;
        }
        .el-checkbox {
            padding-left: 12px;
            ::v-deep .el-checkbox__label {
                padding-left: 8px;
            }
        }
    }
    .el-form-item {
        padding: 5px 8px;
    }
    .table {
        padding-bottom: 20px;
        // flex: 1;
        position: absolute;
        left: 20px;
        right: 20px;
        top: 140px;
        bottom: 0;
        ::v-deep .el-table {
            th {
                background: #f5f5f5;
                padding: 7px 0;
                .cell {
                    font-size: 12px;
                }
            }
            tr {
                border-bottom-style: dashed;
                td {
                    border-bottom-style: dashed;
                    .cell {
                        p {
                            margin: 0 10px;
                            color: #65677a;
                            font-size: 12px;
                            &.title {
                                color: rgb(41, 43, 52);
                                font-size: 14px;
                            }
                            &.ellipsis {
                                span {
                                    color: #1c61fc;
                                    float: right;
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
.el-tooltip__popper,
.tip-text {
    // margin: 5px 0;
    line-height: 1 !important;
}

.tip-text > p {
    margin: 5px 0;
    font-size: 12px;
}
</style>
