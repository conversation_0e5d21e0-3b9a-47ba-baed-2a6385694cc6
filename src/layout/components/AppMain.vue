<template>
    <section :class="mainBgColor ? 'app-main bg-grey' : 'app-main default'">
        <!-- <transition name="fade-transform" mode="out-in"> -->
        <keep-alive :include="cachedViews">
            <router-view :key="key" />
        </keep-alive>
        <!-- </transition> -->
        <!--
            qiankunjs--基座--子应用展示容器---开始
        -->
        <!-- <div v-if="qiankunflag" id="container"></div> -->
        <div id="container"></div>
        <!--
            qiankunjs--基座--子应用展示容器---结束
        -->
    </section>
</template>

<script>
export default {
    name: "AppMain",
    data() {
        return {
            mainBgColor: false,
            qiankunflag:process.env.VUE_APP_USE_TYPE === 'MainApp'
        };
    },
    computed: {
        cachedViews() {
            return this.$store.state.tagsView.cachedViews;
        },
        key() {
            return this.$route.fullPath;
        }
    },
    //
    watch: {
        $route: function(to, from) {
            if (to.meta.bgClass) {
                this.mainBgColor = "grey";
            } else {
                this.mainBgColor = null;
            }
        }
    },
    mounted() {
        if (this.$route.meta.bgClass) {
            this.mainBgColor = "grey";
        }
    }
};
</script>

<style lang="scss" scoped>
.app-main {
    /* 50= navbar  50  */
    min-height: calc(100vh - 92px);
    width: 100%;
    position: relative;
    overflow: visible;
}

.fixed-header + .app-main {
    padding-top: 50px;
    height: 100vh;
    overflow: auto;
}

.hasTagsView {
    .app-main {
        /* 84 = navbar + tags-view = 50 + 34 */
        min-height: calc(100vh - 84px);
    }

    .fixed-header + .app-main {
        padding-top: 84px;
    }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
    .fixed-header {
        padding-right: 15px;
    }
}
</style>
