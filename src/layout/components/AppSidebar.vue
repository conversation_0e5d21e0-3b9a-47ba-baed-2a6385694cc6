<template>
    <sidebar title="PDF-C前端开发框架" :is-spread="computedSideBarSpread" :routes="permission_routes" />
    <!-- :logo="Logo" -->
</template>
<script>
// import Logo from '@/assets/logo_x.png';
import {mapGetters} from 'vuex';
import Sidebar from '@/components/Sidebar';

export default {
    name: 'AppSidebar',
    components: {
        Sidebar
    },
    computed: {
        ...mapGetters(['sideBarSpread', 'permission_routes', 'manualSideBar', 'manual_sideBarSpread', 'manual_mouseInSideBar']),
        computedSideBarSpread() {
            if (this.manualSideBar) {
                return this.manual_sideBarSpread || this.manual_mouseInSideBar;
            } else {
                return this.sideBarSpread;
            }
        }
        // Logo: () => Logo
    }
};
</script>
