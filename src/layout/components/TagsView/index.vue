<template>
    <div id="tags-view-container" class="tags-view-container">
        <scroll-pane ref="scrollPane" class="tags-view-wrapper" @scroll="handleScroll">
            <router-link
                v-for="tag in visitedViews"
                ref="tag"
                :key="tag.path"
                :class="{ active: isActive(tag), affix: isAffix(tag) }"
                :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
                tag="span"
                class="tags-view-item"
                @click.middle.native="!isAffix(tag) ? closeSelectedTag(tag) : ''"
                @contextmenu.prevent.native="openMenu(tag, $event)"
            >
                <span class="tags-view-title">
                    {{ tag.title }}
                </span>
                <span v-if="!isAffix(tag)" class="tag-close-item el-icon-close" @click.prevent.stop="closeSelectedTag(tag)"></span>
            </router-link>
        </scroll-pane>
        <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
            <li @click="refreshSelectedTag(selectedTag)">
                Refresh
            </li>
            <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">
                Close
            </li>
            <li @click="closeOthersTags">
                Close Others
            </li>
            <li @click="closeAllTags(selectedTag)">
                Close All
            </li>
        </ul>
        <!-- 标签过多时显示的省略按钮  start -->
        <div v-if="isShowListAllTags" class="list-all-tags">
            <el-popover ref="tagsPopover" placement="bottom" trigger="click">
                <el-scrollbar>
                    <div style="height: 296px; width: 100%; position: relative">
                        <!-- 循环数据展示全部标签  start -->
                        <router-link
                            v-for="tag in visitedViews"
                            ref="tag"
                            :key="tag.path"
                            :class="isActive(tag) ? 'active' : ''"
                            :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
                            tag="span"
                            class="tags-view-item-all"
                            @click.native="clickListTags($event)"
                            @contextmenu.prevent.native="openMenu(tag, $event)"
                        >
                            <span class="tags-view-title">
                                {{ tag.title }}
                            </span>
                            <!--                            <span v-if="isActive(tag)" class="el-icon-close tags-view-item-all-close" @click.prevent.stop="closeSelectedTag(tag)"></span>-->
                        </router-link>
                        <!-- 循环数据展示全部标签  end -->
                    </div>
                </el-scrollbar>
                <button slot="reference" class="list-all-btn">
                    <i class="el-icon-more"></i>
                </button>
            </el-popover>
        </div>
        <!-- 标签过多时显示的省略按钮  end -->
    </div>
</template>

<script>
import ScrollPane from "./ScrollPane";
import path from "path";

export default {
    components: { ScrollPane },
    data() {
        return {
            isShowListAllTags: false,
            visible: false,
            top: 0,
            left: 0,
            selectedTag: {},
            affixTags: []
        };
    },
    computed: {
        visitedViews() {
            return this.$store.state.tagsView.visitedViews;
        },
        routes() {
            return this.$store.state.permission.routes;
        }
    },
    watch: {
        $route() {
            this.addTags();
            this.moveToCurrentTag();
        },
        visible(value) {
            if (value) {
                document.body.addEventListener("click", this.closeMenu);
            } else {
                document.body.removeEventListener("click", this.closeMenu);
            }
        }
    },
    mounted() {
        this.initTags();
        this.addTags();
    },
    methods: {
        isListAllTags() {
            const tagsContainer = document.getElementById("tags-view-container");
            const containerWidth = tagsContainer.getElementsByClassName("el-scrollbar__view")[0].offsetWidth;
            const tagsItem = tagsContainer.getElementsByClassName("tags-view-item");
            var itemWidth = 0;
            var flag = false;
            const isShowBorder = containerWidth / 150;
            if (this.visitedViews.length > isShowBorder) {
                for (var i = 0; i < tagsItem.length; i++) {
                    itemWidth = itemWidth + tagsItem[i].offsetWidth + 4;
                    // console.log(tagsItem[i].offsetWidth);
                    if (itemWidth > containerWidth - 15) {
                        // console.log("itemWidth" + itemWidth + "..........." + "containerWidth" + containerWidth);
                        flag = true;
                    } else {
                        // console.log("itemWidth" + itemWidth + "..........." + "containerWidth" + containerWidth);
                        flag = false;
                    }
                }
            }

            return flag;
        },
        isActive(route) {
            return route.path === this.$route.path;
        },
        isAffix(tag) {
            return tag.meta && tag.meta.affix;
        },
        filterAffixTags(routes, basePath = "/") {
            let tags = [];
            routes.forEach((route) => {
                if (route.meta && route.meta.affix) {
                    const tagPath = path.resolve(basePath, route.path);
                    tags.push({
                        fullPath: tagPath,
                        path: tagPath,
                        name: route.name,
                        meta: { ...route.meta }
                    });
                }
                if (route.children) {
                    const tempTags = this.filterAffixTags(route.children, route.path);
                    if (tempTags.length >= 1) {
                        tags = [...tags, ...tempTags];
                    }
                }
            });
            return tags;
        },
        initTags() {
            const affixTags = (this.affixTags = this.filterAffixTags(this.routes));
            for (const tag of affixTags) {
                // Must have tag name
                if (tag.name) {
                    this.$store.dispatch("tagsView/addVisitedView", tag);
                }
            }
        },
        addTags() {
            const { name } = this.$route;
            if (name) {
                this.$store.dispatch("tagsView/addView", this.$route);
            }
            this.isShowListAllTags = this.isListAllTags();
            return false;
        },
        moveToCurrentTag() {
            const tags = this.$refs.tag;
            this.$nextTick(() => {
                for (const tag of tags) {
                    if (tag.to.path === this.$route.path) {
                        this.$refs.scrollPane.moveToTarget(tag);
                        // when query is different then update
                        if (tag.to.fullPath !== this.$route.fullPath) {
                            this.$store.dispatch("tagsView/updateVisitedView", this.$route);
                        }
                        break;
                    }
                }
            });
        },
        refreshSelectedTag(view) {
            this.$store.dispatch("tagsView/delCachedView", view).then(() => {
                const { fullPath } = view;
                this.$nextTick(() => {
                    this.$router.replace({
                        path: "/redirect" + fullPath
                    });
                });
            });
            this.isShowListAllTags = this.isListAllTags();
        },
        closeSelectedTag(view) {
            this.$store.dispatch("tagsView/delView", view).then(({ visitedViews }) => {
                if (this.isActive(view)) {
                    this.toLastView(visitedViews, view);
                }
            });
            this.isShowListAllTags = this.isListAllTags();
        },
        closeOthersTags() {
            this.$router.push(this.selectedTag);
            this.$store.dispatch("tagsView/delOthersViews", this.selectedTag).then(() => {
                this.moveToCurrentTag();
            });
            this.isShowListAllTags = this.isListAllTags();
        },
        closeAllTags(view) {
            this.$store.dispatch("tagsView/delAllViews").then(({ visitedViews }) => {
                if (this.affixTags.some((tag) => tag.path === view.path)) {
                    return;
                }
                this.toLastView(visitedViews, view);
                this.$forceUpdate();
            });
            this.isShowListAllTags = this.isListAllTags();
        },
        toLastView(visitedViews, view) {
            const latestView = visitedViews.slice(-1)[0];
            if (latestView) {
                this.$router.push(latestView.fullPath);
            } else {
                // now the default is to redirect to the home page if there is no tags-view,
                // you can adjust it according to your needs.
                if (view.name === "Dashboard") {
                    // to reload home page
                    this.$router.replace({ path: "/redirect" + view.fullPath });
                } else {
                    this.$router.push("/");
                }
            }
        },
        openMenu(tag, e) {
            const menuMinWidth = 105;
            const offsetLeft = this.$el.getBoundingClientRect().left; // container margin left
            const offsetWidth = this.$el.offsetWidth; // container width
            const maxLeft = offsetWidth - menuMinWidth; // left boundary
            const left = e.clientX - offsetLeft + 15; // 15: margin right

            if (left > maxLeft) {
                this.left = maxLeft;
            } else {
                this.left = left;
            }

            this.top = e.clientY;
            this.visible = true;
            this.selectedTag = tag;
        },
        closeMenu() {
            this.visible = false;
            this.isShowListAllTags = this.isListAllTags();
        },
        handleScroll() {
            this.closeMenu();
        },
        clickListTags() {
            this.$refs.tagsPopover.doClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.list-all-btn {
    height: 100%;
    width: 100%;
    padding: 0;
    border: none;
    min-width: 0;
    background: #fafafa;
    cursor: pointer;
    font-size: 20px;
    &:hover {
        background: #f2f3f5;
        color: #2a47a5;
    }
}
.list-all-tags {
    width: 54px;
    height: 36px;
    background: #fafafa;
    position: absolute;
    bottom: 0;
    right: 0;
    -webkit-box-shadow: -6px 0 8px rgba(41, 43, 52, 0.24);
    box-shadow: -6px 0 8px rgba(41, 43, 52, 0.24);
}
.tags-view-item-all {
    padding: 0 24px;
    display: block;
    position: relative;
    cursor: pointer;
    line-height: 40px;
    color: #292b34;
    background: #ffffff;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    &.active {
        font-weight: normal;
    }
    &:hover {
        background: rgb(233, 238, 253);
    }
}
.tags-view-item-all-close {
    position: absolute;
    top: 10px;
    right: 10px;
}
.tags-view-item-all-close:hover {
    background-color: #eeeeee;
}
::v-deep .el-scrollbar__wrap {
    margin-bottom: 0px !important;
}
.tags-view-container {
    // height: 100%;
    height: 36px;
    width: 100%;
    // background: rgba(101, 103, 122, 0.2);
    // background: #cdcdcd;
    background: #FFFFFF;
    // border: 1px solid #f1f2f4;
    position: relative;
    //消除tabs栏的右侧阴影，在两侧留白的时候可以看出
    // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 0px 0 rgba(0, 0, 0, 0.04);
    .tags-view-wrapper {
        height: 100%;
        background: rgba(241, 242, 244, 0.5);
        .tags-view-item {
            padding: 0 24px;
            margin: 8px 2px 0 2px;
            display: inline-block;
            position: relative;
            cursor: pointer;
            height: 28px;
            line-height: 28px;
            border: 1px solid #edeef0;
            border-bottom: 0;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            color: #292b34;
            background: #edeef0;
            // background: rgba(241, 242, 244, 0.5);
            font-size: 12px;
            &:first-of-type {
                margin-left: 14px;
            }
            &:last-of-type {
                margin-right: 14px;
            }
            &.affix:not(.active):hover {
                padding: 0 24px;
            }
            &:hover {
                // padding: 0 12px;
                // background: linear-gradient(59deg, rgba(66, 82, 224, 0.1), rgba(28, 97, 252, 0.1));
                // border-color: #eceefc;
                // color: #2a47a5;
                padding: 0 12px;
                color: #2a47a5;
                border-bottom: none;
                border: 1px solid transparent;
                border-radius: 4px;
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
                background-clip: padding-box, border-box;
                background-origin: padding-box, border-box;
                background-image: linear-gradient(59deg, rgba(66, 82, 224, 0.1), rgba(28, 97, 252, 0.1));
                .tag-close-item {
                    display: inherit;
                }
            }
            &.active {
                font-weight: normal;
                padding: 0 12px;
                background-color: #ffffff;
                color: #1c61fc;
                border-color: #dddddd;
                .tag-close-item {
                    display: inherit;
                }
            }
            .tag-close-item {
                display: none;
            }
        }
    }
    .contextmenu {
        margin: 0;
        background: #fff;
        z-index: 3000;
        position: absolute;
        list-style-type: none;
        padding: 5px 0;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #333;
        box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
        li {
            margin: 0;
            padding: 7px 16px;
            cursor: pointer;
            &:hover {
                background: #eee;
            }
        }
    }
}
</style>

<style lang="scss">
.all-tag-views-popover {
    padding-left: 0;
    padding-right: 0;
}
//reset element css of el-icon-close
.tags-view-wrapper {
    .tags-view-item {
        .tags-view-title {
            display: inline-block;
            vertical-align: top;
            max-width: 100px;
            text-align: center;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .el-icon-close {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
            transform-origin: 100% 50%;
            margin-left: 8px;
            color: rgb(90, 102, 105);
            font-size: 16px;
            line-height: 16px;
            vertical-align: text-bottom;
            &:before {
                transform: scale(0.6);
                display: inline-block;
            }
            &:hover {
                background-color: #dcdcdc;
            }
        }
    }
}
// .tags-view-container .el-scrollbar__wrap {
//     margin-bottom: 0px !important;
// }
// .scroll-container .el-scrollbar__wrap{
//     height: 50px !important;
// }
// .el-scrollbar__wrap {
//     margin-bottom: 0px !important;
// }
/* .el-scrollbar__wrap{
    overflow-x:auto
} */
</style>
