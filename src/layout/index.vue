<template>
    <div class="app-wrapper">
        <div v-if="!isqiankun" class="el-aside sidebar-container" @mouseenter="spreadSidebar" @mouseleave="shrinkSidebar">
            <app-sidebar />
        </div>
        <div
            :style="(isTagsView ? '' : 'padding-top: 56px;')+(isqiankun? 'padding:0px !important;':'')"
            :class="isHomePath ? (toolbarOpened ? 'layout-right toolbar-is-active' : 'layout-right toolbar-is-inactive') : 'layout-right'"
        >
            <div v-if="!isqiankun" class="main-up">
                <app-header />
                <tags-view v-if="isTagsView" />
            </div>

            <div class="main-down" :class="extendsFlag ? 'extends-flag' : ''">
                <app-main />
                <div :style="isTagsView ? '' : 'padding-top: 56px;'" class="toolbar-container">
                    <app-toolbar />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ResizeMixin from "./mixin/ResizeHandler";
import Logo from "@/assets/PICC.png";
import { mapGetters } from "vuex";
import { AppMain } from "./components";
import AppHeader from "./components/AppHeader";
import AppToolbar from "./components/AppToolbar";
import AppSidebar from "./components/AppSidebar";
import TagsView from "./components/TagsView/index";
import settings from "@/settings";

export default {
    name: "Layout",
    components: {
        AppMain,
        AppHeader,
        AppToolbar,
        AppSidebar,
        TagsView
    },
    mixins: [ResizeMixin],
    data() {
        return {
            // 根据src/settings中的isTagsBar的值确定标签栏的有无，从而影响展示区域的padding-top的样式
            isTagsView: settings.isTagsBar,
            Logo: Logo,
            isqiankun:window.__POWERED_BY_QIANKUN__ ? true : false
        };
    },
    computed: {
        ...mapGetters(['currentPath', 'sideBarSpread', 'manualSideBar','mouseInSideBar','manual_sideBarSpread']),
        toolbarOpened() {
            if(this.manualSideBar) return false;
            return this.$store.state.app.toolbarOpened;
        },
        extendsFlag() {
            if(this.manualSideBar) return false;
            return !this.isHomePath && this.toolbarOpened;
        },
        isHomePath() {
            if(this.manualSideBar) return this.manual_sideBarSpread;
            return this.$route.path === settings.homePath;
        },
        pathAndToolbar() {
            return {isHomePath: this.isHomePath, toolbarOpened: this.toolbarOpened};
        }
    },
    watch: {
        pathAndToolbar: {
            immediate: true,
            handler(val) {
                if(this.manualSideBar) return;
                const {isHomePath, toolbarOpened} = val;
                if (isHomePath) {
                    if (toolbarOpened) {
                        this.$store.dispatch('app/setSideBarSpread', false);
                    } else {
                        this.$store.dispatch('app/setSideBarSpread', true);
                    }
                } else {
                    this.$store.dispatch('app/setSideBarSpread', this.mouseInSideBar);
                }
            }
        }
    },
    mounted() {
        // 解决popper显示的时候会被固定的头覆盖掉
        // 所以在滚动事件中动态给头部header加大z-index,除遮罩外
        window.addEventListener("scroll", () => {
            if (document.body.lastElementChild.classList.contains("el-popper")) {
                const lastChild = document.body.lastElementChild;
                //
                if (!lastChild.classList.contains("picc-nav-dropdown-menu") && !lastChild.classList.contains("picc-popover-message")) {
                    document.querySelector(".main-up").style.zIndex = parseInt(lastChild.style.zIndex) + 1;
                }
            } else {
                document.querySelector(".main-up").style.zIndex = 2000;
            }
        });
    },
    methods: {
        spreadSidebar(){
            if(this.manualSideBar) return;
            this.$store.dispatch('app/setMouseInSideBar',true);
            this.$store.dispatch('app/setSideBarSpread',true);
        },
        shrinkSidebar(){
            if(this.manualSideBar) return;
            this.$store.dispatch('app/setMouseInSideBar',false);
            if (!this.isHomePath) {
                this.$store.dispatch('app/setSideBarSpread', false);
            } else {
                this.$store.dispatch('app/setSideBarSpread', !this.toolbarOpened);
            }
        },
        handleClickOutside() {
            this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
        },
        heightScreen() {
            return window.innerHeight - 90 + "px";
        },
        heightFullScreen() {
            return window.innerHeight + "px";
        }
    }
};
</script>

<style lang="scss">
// .main-up{
//     // background: pink;
//     height: auto;
//     position: fixed;
//     top:0;
//     right:0;
//     left:102px;
//     z-index:10
// }
.extends-flag {
    transition: padding-right 0.2s ease-in;
    padding-right: 360px !important;
}
.main-down {
    // background: gold;
    transition: padding-right 0.2s ease-in;
    width: 100%;
    height: auto;
    padding-top: 90px;
    padding-right: 56px;
}
</style>
<style lang="scss">
#containerBody::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
/*正常情况下滑块的样式*/

#containerBody::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 10px;
    -webkit-box-shadow: inset 1px 1px 0 #dddddd;
}
/*鼠标悬浮在该类指向的控件上时滑块的样式*/

#containerBody:hover::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 10px;
    -webkit-box-shadow: inset 1px 1px 0 #dddddd;
}
/*鼠标悬浮在滑块上时滑块的样式*/

#containerBody::-webkit-scrollbar-thumb:hover {
    background-color: #dddddd;
    -webkit-box-shadow: inset 1px 1px 0 #dddddd;
}
/*正常时候的主干部分*/

#containerBody::-webkit-scrollbar-track {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px #dddddd;
    background-color: white;
}
/*鼠标悬浮在滚动条上的主干部分*/

#containerBody::-webkit-scrollbar-track:hover {
    -webkit-box-shadow: inset 0 0 6px #dddddd;
    background-color: #ffffff;
}
</style>
