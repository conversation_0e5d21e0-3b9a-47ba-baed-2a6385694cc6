const dateReg1 = /^\d{4}-\d{1,2}-\d{1,2}$/;
const dateReg2 = /^\d{8}$/;
const dateReg3 = /^\d{4}\/\d{1,2}\/\d{1,2}$/;

export const dateFormat = {
    data() {
        return {
            dateFormat: "yyyy/MM/dd"
        };
    },
    mounted () {
        //
        const dateInputs = document.querySelectorAll('[data-mode="dateFormat"] input');
        new Set(dateInputs).forEach(item => {
            item.addEventListener('input', e => {
                const val = e.target.value;
                if (dateReg1.test(val)) {
                    this.dateFormat = 'yyyy-MM-dd';
                } else if (dateReg2.test(val)) {
                    this.dateFormat = 'yyyyMMdd';
                } else if (dateReg3.test(val)) {
                    this.dateFormat = 'yyyy/MM/dd';
                } else {
                    this.dateFormat = 'yyyy/MM/dd';
                }
            });
            item.addEventListener('blur', e => {
                // console.log(e);
                const val = e.target.value;
                if (dateReg1.test(val) || dateReg2.test(val) || dateReg3.test(val)) {
                    this.dateFormat = 'yyyy/MM/dd';
                }
            });
        });
    },
    methods: {
        dateFormatSet(str) {
            if (dateReg1.test(str) || dateReg2.test(str) || dateReg3.test(str)) {
                this.dateFormat = 'yyyy/MM/dd';
            }
        }
    }
}
;
