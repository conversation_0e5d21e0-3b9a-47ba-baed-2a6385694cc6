/*
 * for sidebar component start
*/

.picc-sidebar {
    min-width: 142px;
    .picc-sidebar-logo span {
        display: block;
    }
    &.is-shrink {
        padding: 0;
        box-sizing: unset;
        .picc-sidebar-item {
            a {
                span {
                    display: none;
                }
            }
        }
    }
    // 展开状态
    &:not(.is-shrink) {
        //
        .picc-sidebar-item {
            a {
                span {
                    // padding-left: 10px;
                    display: inline-block;
                }
            }
        }
    }
    .pointer-events-none{
        pointer-events: none;
    }
}

// TODO
#toolSelectList {
    right: 0 !important;
}

#containerBody {
    background: #fff;
    &.theme-grey {
        background: #e6e6e6;
    }
    .el-main {
        padding: 0;
    }
    .app-main {
        padding: 20px;
        &.default {
            background: #fff;
        }
        &.bg-grey {
            background: #e6e6e6;
        }
    }
}

// picc dialog
.picc-dialog {
    ::v-deep .el-dialog__footer {
        text-align: center;
    }
}

// picc result
.picc-result {
    .picc-result-body {
        // padding: 0 60px;
        // font-size: 0;
        .picc-result-list_title {
            margin-right: 24px;
            font-size: 14px;
            line-height: 14px;
            display: inline-block;
            & + span {
                font-size: 14px;
            }
        }
        .picc-result-msg {
            font-size: 12px;
            font-weight: normal;
        }
    }
    .picc-result-box {
        padding-bottom: 36px;
    }
}

// global
.mt28 {
    margin-top: 28px;
}

.mt20 {
    margin-top: 20px;
}

.mt40 {
    margin-top: 40px;
}

.mt16 {
    margin-top: 16px;
}

.mt32 {
    margin-top: 32px;
}

// tooltip multi
.picc-tooltip-multi {
    padding: 20px;
    min-width: 207px;
    .picc-tooltip-multi-list {
        max-height: 192px;
        display: inline-block;
        &:last-child {
            border-left: 1px solid #65677a;
            margin-left: 20px;
            padding-left: 20px;
        }
        // flex-wrap: wrap;
        // flex-direction: column;
        .picc-tooltip-multi-list_item {
            display: table-row;
            &:last-child {
                dt,
                dd {
                    padding-bottom: 0;
                }
            }
            dt,
            dd {
                display: table-cell;
                padding-bottom: 16px;
            }
            dt {
                padding-right: 16px;
                color: #909199;
            }
        }
    }
}
/*
 * TagsView scrollbar scss-----------start
*/
.scroll-container {
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    width: 100%;
    .el-scrollbar__bar {
        bottom: 0px;
    }
    .el-scrollbar__wrap {
        height: 50px;
    }
    .is-vertical {
        display: none;
    }
}
/*
 * TagsView scrollbar scss-----------end
*/

/*
 * responsive mode
*/

.main-up {
    // background: pink;
    height: auto;
    position: fixed;
    top: 0;
    right: 0;
    left: 70px;
    z-index: 1998;
}

.layout-right {
    // background:yellow;
    // width:calc(100%-189);
    height: 100%;
    box-sizing: border-box;
    width: 100%;
    padding: 92px 56px 0 70px;
    &.toolbar-is-inactive {
        padding: 92px 56px 0 142px;
        .main-up {
            left: 142px;
        }
    }
}

// TODO
.main-down {
    padding-top: 0;
    padding-right: 0;
}
/**
*；没有两侧留白和媒体查询适配
*/
@media screen and (max-width: 1919px) {
    #app {
        // width: calc(100vw - (100vw - 100%));
        // padding: 0 calc((100% - (1600px - (100vw - 100%))) / 2);
        width: 100%;
        margin: 0 0;
    }
    #toolSelectList {
        right: 0 !important;
        background-color: #f1f2f4;
    }
    #toolDrawerId {
        margin-right: 56px !important;
    }
    #toolSelectList {
        height: 100%;
    }
    .main-up {
        left: 70px;
        right: 0;
        transition: left 0.1s ease-in;
    }
    .picc-sidebar {
        &.is-shrink{
            .picc-sidebar-item {
                & > div {
                    opacity: 1;
                    left: 70px !important;
                }
            }
        }
        .picc-sidebar-item {
            //&:hover {
                & > div {
                    opacity: 1;
                    left: 142px !important;
                }
            //}
        }
    }
    .layout-right {
        &.sidebar-expanded {
            &:not(.is-home) {
                .main-up {
                    transition: left 0.1s ease-in;
                    left: 142px;
                }
                .main-down {
                    transition: padding 0.1s ease-in;
                    padding-left: calc(142px - 70px);
                }
            }
        }
    }
    .layout-right.toolbar-is-inactive .main-up {
        left: 142px;
    }
    // .layout-right.toolbar-is-inactive {
    //     padding: 92px 56px 0 184px;
    // }
}
@media screen and (min-width: 1919px) {
    .main-up {
        left: 80px;
        right: 0;
    }
    .layout-right {
        padding: 92px 56px 0 80px;
    }
    #toolSelectList {
        right: 0 !important;
        background-color: #f1f2f4;
    }
    #toolDrawerId {
        margin-right: 56px !important;
    }
    #toolSelectList {
        height: 100%;
    }
    .picc-sidebar {
        &.is-shrink{
            .picc-sidebar-item {
                & > div {
                    opacity: 1;
                    left: 80px !important;
                }
            }
        }
        .picc-sidebar-item {
            //&:hover {
                & > div {
                    opacity: 1;
                    left: 184px  !important;
                }
            //}
        }
    }
    .layout-right {
        &.sidebar-expanded {
            &:not(.is-home) {
                .main-up {
                    left: 184px;
                }
                .main-down {
                    padding-left: calc(184px - 80px);
                }
            }
        }
    }
    .layout-right.toolbar-is-inactive .main-up {
        left: 184px;
    }
    .layout-right.toolbar-is-inactive {
        padding: 92px 56px 0 184px;
    }
}
/**
*；两侧留白的媒体查询适配
*/
// @media screen and (max-width:1617px) {
//     #toolSelectList {
//         background-color: #F1F2F4;
//     }
//     #toolDrawer {
//         margin-right: 56px !important;
//     }
//     #toolSelectList {
//         height: 100%;
//     }
//     .picc-sidebar {
//         .picc-sidebar-item {
//             &:hover {
//                 &>div {
//                     left: 142px
//                 }
//             }
//         }
//     }
//     .layout-right {
//         &.sidebar-expanded {
//             &:not(.is-home) {
//                 .main-up {
//                     transition: left .1s ease-in;
//                     left: 142px
//                 }
//                 .main-down {
//                     transition: padding .1s ease-in;
//                     padding-left: 72px;
//                 }
//             }
//         }
//     }
// }

// // web template responsive mode
// @media screen and (min-width:1617px) {
//     #app {
//         // width: calc(100vw - (100vw - 100%));
//         // padding: 0 calc((100% - (1600px - (100vw - 100%))) / 2);
//         width: 1600px;
//         margin: 0 calc((100vw - 1600px) / 2);
//     }
//     #toolSelectList {
//         right: calc((100vw - 1600px) / 2 - (100vw - 100%)) !important;
//         background-color: #F1F2F4;
//     }
//     #toolDrawer {
//         margin-right: calc((100vw - 1600px) / 2 - (100vw - 100%) + 56px) !important;
//     }
//     #toolSelectList {
//         height: 100%;
//     }
//     .main-up {
//         left: calc(70px + ((100% - (1600px - (100vw - 100%))) / 2));
//         right: calc((100vw - 1600px) / 2 - (100vw - 100%));
//         transition: left .1s ease-in;
//     }
//     .picc-sidebar {
//         .picc-sidebar-item {
//             &:hover {
//                 &>div {
//                     left: calc( ((100% - (1600px - (100vw - 100%))) / 2) + 142px)
//                 }
//             }
//         }
//     }
//     .layout-right {
//         &.sidebar-expanded {
//             &:not(.is-home) {
//                 .main-up {
//                     transition: left .1s ease-in;
//                     left: calc(((100% - (1600px - (100vw - 100%))) / 2) + 142px)
//                 }
//                 .main-down {
//                     transition: padding .1s ease-in;
//                     padding-left: calc(142px - 70px);
//                 }
//             }
//         }
//     }
//     .layout-right.toolbar-is-inactive .main-up {
//         left: calc(142px + ((100vw - 1600px) / 2));
//     }
//     // .layout-right.toolbar-is-inactive {
//     //     padding: 92px 56px 0 184px;
//     // }
// }

// @media screen and (min-width:1920px) {
//     .main-up {
//         left: calc(80px + ((100% - (1600px - (100vw - 100%))) / 2));
//         right: calc((100vw - 1600px) / 2 - (100vw - 100%));
//     }
//     .layout-right {
//         padding: 92px 56px 0 80px;
//     }
//     #toolSelectList {
//         right: calc((100vw - 1600px) / 2 - (100vw - 100%)) !important;
//         background-color: #F1F2F4;
//     }
//     #toolDrawer {
//         margin-right: calc((100vw - 1600px) / 2 - (100vw - 100%) + 56px) !important;
//     }
//     #toolSelectList {
//         height: 100%;
//     }
//     .picc-sidebar {
//         .picc-sidebar-item {
//             &:hover {
//                 &>div {
//                     left: calc( ((100% - (1600px - (100vw - 100%))) / 2) + 184px)
//                 }
//             }
//         }
//     }
//     .layout-right {
//         &.sidebar-expanded {
//             &:not(.is-home) {
//                 .main-up {
//                     left: calc(((100% - (1600px - (100vw - 100%))) / 2) + 184px)
//                 }
//                 .main-down {
//                     padding-left: calc(184px - 80px);
//                 }
//             }
//         }
//     }
//     .layout-right.toolbar-is-inactive .main-up {
//         left: calc(184px + ((100vw - 1600px) / 2));
//     }
//     .layout-right.toolbar-is-inactive {
//         padding: 92px 56px 0 184px;
//     }
// }

.picc-steps-simple .is-success .el-step__icon.is-icon {
    color: white;
    background: #1ac88e;
    border-radius: 8px;
}

.picc-steps-simple .is-error .el-step__icon.is-icon {
    color: white;
    background: #dc402b;
    border-radius: 8px;
}
.picc-steps-vertical  .el-step.is-vertical .el-step__line {
    width: 2px;
    top: 3px;
    bottom: -3px;
    left: 11px;
}
.picc-steps-vertical .is-vertical .el-step__icon {
    position: absolute;
    left: 4px;
    top: 3px;
    width: 16px !important;
    height: 16px !important;
    font-size: 12px;
    background: none;
    border: 1px solid;
    border-color: #aaa;
    background: #f5f5f5;
}
.picc-steps-vertical .is-success .el-step__icon {
    width: 16px !important;
    height: 16px !important;
    color: white;
    background: #1ac88e;
    border-radius: 8px;
    border-color: #1ac88e;
}

// 根据用户体验处要求修改输入框不可用时的鼠标指针图标，图片位置：src/assets/InputDisabled.png
[class*='is-disabled'] * {
    cursor: url(../assets/InputDisabled.png)  2 2, pointer !important;
}
// .picc-steps-vertical.el-timeline .el-timeline-item__node.el-timeline-item__node--normal.el-timeline-item__node-- {}
@import "responsive.scss";
