/*
 sidebar
*/

.sidebar-container {
    .el-scrollbar .el-scrollbar__wrap {
        overflow-x: hidden;
    }
    .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
        white-space: nowrap;
        display: inline-block;
        height: 100%;
    }
    height: 100%;
    position: fixed;
    background-color: #f1f2f4;
    // border-right: 1px solid #E2E2E2;
    // z-index: 9999;
    z-index: 1999;
    .picc-sidebar-sub--item {
        width: 224px;
    }
    .picc-sidebar-sub {
        height: 100%;
        display: inline-block;
        vertical-align: top;
    }
    .picc-sidebar-item,
    .picc-sidebar-item .picc-sidebar-sub--item {
        .router-link-active,
        .router-link-active .svg-icon {
            color: #e7390e;
        }
    }
    .picc-sidebar-item {
        & > a {
            &.router-link-active {
                font-weight: 700;
            }
        }
    }
}

.picc-sidebar {
    .picc-sidebar-item {
        padding: 0;
        a {
            display: block;
            // text-align: center;
            & > svg {
                margin-left: 27.5px;
                font-size: 15px;
            }
            span {
                width: auto;
            }
        }
    }
}

@media screen and (min-width: 1919px) {
    .picc-sidebar {
        .picc-sidebar-item {
            a {
                padding: 0 5px;
            }
        }
        &.is-shrink {
            .picc-sidebar-logo {
                width: 80px;
            }
        }
        &:not(.is-shrink) {
            width: 184px;
        }
    }
}
