import { register<PERSON><PERSON><PERSON><PERSON><PERSON>, initGlobalState } from "qiankun";
export const registerList = [
    // {
    //     name: "paas-manager-test-web",
    //     entry: "http://************:9528",
    //     container: "#container",
    //     activeRule: "/paasPortal/test"
    // },
    // {
    //     name: "paas-manager-demo-web2",
    //     entry: "http://************:9529",
    //     container: "#container",
    //     activeRule: "/paasPortal/demo"
    // }
    // {
    //     name: "paas-manager-test-web",
    //     entry: "http://localhost:9528",
    //     container: "#container",
    //     activeRule: "/paasPortal/test"
    // },
    // {
    //     name: "paas-manager-demo-web2",
    //     entry: "http://localhost:9529",
    //     container: "#container",
    //     activeRule: "/paasPortal/demo"
    // }
    // {
    //     name: "paas-manager-test-web",
    //     entry: "http://************:9527/sub1/",
    //     container: "#container",
    //     activeRule: "/paasPortal/test"
    // },
    // {
    //     name: "paas-manager-demo-web2",
    //     entry: "http://************:9527/sub2/",
    //     container: "#container",
    //     activeRule: "/paasPortal/demo"
    // }
    {
        name: "paas-manager-test-web",
        entry: "http://************:9527/sub1/",
        // entry: "http://localhost:9528/sub1/",
        container: "#container",
        activeRule: "/paasPortal/test"
    },
    {
        name: "paas-manager-demo-web2",
        entry: "http://************:9527/sub2/",
        // entry: "http://localhost:9528/sub2/",
        container: "#container",
        activeRule: "/paasPortal/demo"
    },
    {
        name: "doc-demo-web2",
        // entry: "http://**************/",
        // entry: "http://**************/open/",
        entry: "http://**************/docs/viewweb/reader/788f2c4aecfb4b698796aa3edfba?_w_filecode=f943458f18cb6cccccd7e54b07cd4136&_w_scene_id=4fa9f2d416f27387e7936ac6130f3c1a&_w_third_appid=AK20240531JLPFRR&_w_third_file_id=788f2c4aecfb4b698796aa3edfba&_w_demo_token=5c712c7747e2463bb8b31494ec0c",
        // entry: "http://localhost:9528/sub2/",
        container: "#container",
        activeRule: "/paasPortal/docs"
    }
];

export function registerMicroAppsData() {
    registerMicroApps(registerList);
}