module.exports = {
    // login active
    // TODO: 是否激活登录页
    /**
     * @type {Boolean} true | false;
     * @description show login page or not;
     */
    loginActive: false,
    // TODO: 可以按照开发进度删除代码
    loginInfo: {
        user: '123456456',
        token: Math.random(),
        userId: '100000'
    },
    // 指定某个路由为首页，默认为 '/'
    homePath: '/dashboard',
    // TODO: 按照所属项目修改项目的名称作为页面标题
    title: 'PICC',
    /**
     * @type {boolean} true | false
     * @description Whether show the settings right-panel
     */
    errorLog: 'production',
    /**
     * @type {boolean} true | false
     * @description Whether show the tags-view in layout
     */
    isTagsBar: true,
    /**
     * @description 手动控制左侧菜单
     * @type {boolean}
     */
    manualSideBar: false
};
