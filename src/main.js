/*
 * PICC web template 入口文件
 * { picc } PICC 公共组件
 * { ./icons } 全局svg图标
 * { filters } 全局vue自定义过滤
 */
import 'whatwg-fetch';
import Vue from "vue";
import Element from "element-ui";
import App from "./App";
import "./registerServiceWorker";
import store from "./store";
import { routerFun, destoryRouterFun } from "./router";
import "./icons"; // icon
// import "./permission"; // permission control
import * as filters from "./filters"; // global filters
// picc components
// import "web-plugin/common/theme/index.css";
import picc from "web-plugin";
import "web-plugin/common/picc.css";
import "@/styles/index.scss";
// i18n languages
import i18n from "@/lang";
// 引入vue-quill-editor富文本编辑器
import VueQuillEditor from "vue-quill-editor";

import "quill/dist/quill.core.css"; // import styles
import "quill/dist/quill.snow.css"; // for snow theme
import "quill/dist/quill.bubble.css"; // for bubble theme
import * as echarts from "echarts";

//乾坤微应用相关
import "./public-path";
import { registerMicroAppsData } from "@/MicroConfig";
//XSS安全配置
// import xss from '@/utils/xss.js';
import verifition from "@picc/verifition";
Vue.use(verifition);
import xss from 'xss';
Vue.prototype.$xss = xss;

registerMicroAppsData();
Vue.prototype.$echarts = echarts; // eslint-disable-line no-undef
Vue.use(VueQuillEditor /* { default global options } */); // eslint-disable-line no-undef
Vue.use(Element);
Vue.use(picc);

Object.keys(filters).forEach((key) => {
    Vue.filter(key, filters[key]); // eslint-disable-line no-undef
});
Vue.config.productionTip = false; // eslint-disable-line no-undef
/* eslint-disable */
let instance = null;
let router = routerFun()
if (process.env.VUE_APP_USE_TYPE !== "MicroApp") {
    /**
* qiankunjs--基座--创建vue实例---开始
*/
    new Vue({
        router,
        store,
        i18n,
        render: (h) => h(App)
    }).$mount("#app");
    /**
    * qiankunjs--基座--创建vue实例---结束
    */
} else {
    /**
     * qiankunjs--子应用--开始
     */


    // 独立运行时
    if (!window.__POWERED_BY_QIANKUN__) {
        render();
    }

    /**
    * qiankunjs--子应用--结束
    */
}

function render(props = {}) {
    const { container } = props;
    instance = new Vue({
        router,
        store,
        render: (h) => h(App),
    }).$mount(container ? container.querySelector('#app') : '#app');
}
export async function bootstrap() {
    console.log("[vue] vue app bootstraped");
}
export async function mount(props) {
    console.log("[vue] props from main framework", props);
    render(props);
}
export async function unmount() {
    instance.$destroy();
    instance.$el.innerHTML = "";
    instance = null;
    // router = null;
    destoryRouterFun()
}
