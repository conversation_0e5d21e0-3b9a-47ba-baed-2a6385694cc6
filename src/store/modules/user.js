// import { getStorage } from '@/utils/storage.js';
import * as mock from '@/api/mock';
import settings from '@/settings';
const state = {
    // TODO: 按照要求定义指定数据属性
    userInfo: null
};
const mutations = {
    SET_USERINFO: (state, data) => {
        state.userInfo = data;
    }
};
// TODO: 可以按照要求将指定的数据存储到vuex
const actions = {
    setUserInfo({ state, commit }, data) {
        return new Promise((resolve, reject) => {
            // TODO
            commit('SET_USERINFO', data);
        });
    },
    getUserInfo({ state, commit }) {
        return new Promise((resolve, reject) => {
            /**
             * settings.loginActive;
             * @description There's login page;
             */
            if (settings.loginActive) {
                // TODO
                /**
                 * @description api calling for userinfo to set the userinfo or set the data to local;
                 */
                mock.getUserInfo().then(data => {
                    commit('SET_USERINFO', data);
                    resolve(data);
                });
            /**
             * @description There's no login page, that means using general platform
             */
            } else {
                commit('SET_USERINFO', settings.loginInfo);
                resolve(settings.loginInfo);
            }
        });
    },
    // TODO
    /**
     * @description when logout, it should be clear data in vuex
     */
    clearUserInfo({ state, commit }, data) {
        return new Promise((resolve, reject) => {
            // TODO
            commit('SET_USERINFO', {});
        });
    },
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
