import { constantRoutes,asyncRoutes } from "@/router";
import { dealRouter } from "@/router/dealRouter";
// import { MessageBox } from 'element-ui';
// import custom from '@/router/modules/custom';

import { getRoutes } from "@/api/mock";
// import { getStorage } from '@/utils/storage';
/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
    if (route.meta && route.meta.roles) {
        return roles.some((role) => route.meta.roles.includes(role));
    } else {
        return true;
    }
}

/**
 * 对前端路由进行权限过滤
 * @param allArray asyncRoutes 前端记录的路由信息
 * @param permissionArray  后端接口返回的数据处理后的路由
 * @returns 当前用户有权限的路由数据
 * 
 */
export function filterAsyncRoutes(allArray, permissionArray) {
    //过滤当前数组获取数组并集
    let res = allArray.filter(item => permissionArray.find(bItem => {
        return item.path == bItem.path;
    }));
    //遍历并集数组对children数组进行并集操作
    res.map((item)=>{
        let tmp = permissionArray.find(bItem => {
            return item.path == bItem.path;
        });
        if (item.children&&item.children.length!==0){
            filterAsyncRoutes(item.children,tmp.children);
        }
    });
    return res;
}
const state = {
    routes: [],
    addRoutes: []
};

const mutations = {
    SET_ROUTES: (state, routes) => {
        state.routes = routes;
    }
};

const actions = {
    generateRoutes({ commit, dispatch }) {
        return new Promise((resolve) => {
            getRoutes().then((data) => {
                const backRouters = dealRouter(data);
                const result = filterAsyncRoutes(asyncRoutes,backRouters);
                console.log(result,'.....................///////////...............');
                commit("SET_ROUTES", result);
                resolve(result);
            });
        });
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
