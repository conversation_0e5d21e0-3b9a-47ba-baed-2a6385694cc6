import Cookies from 'js-cookie';
import { getLanguage } from '@/lang/index';
import {manualSideBar} from '../../settings';

const state = {
    manualSideBar: manualSideBar,
    manualSideBarStatus: {
        sideBarSpread: true,
        mouseInSideBar: false
    },
    sidebar: {
        opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
        withoutAnimation: false
    },
    device: 'desktop',
    language: getLanguage(),
    size: Cookies.get('size') || '',
    inThemeColor: Cookies.get('inThemeColor') || '#E8E7E4',
    outThemeColor: Cookies.get('outThemeColor') || '#C6C6C6',
    sideBarSpread: true,
    currentPath: '',
    toolbarOpened: false,
    mouseInSideBar: false,
};

const mutations = {
    TOGGLE_SIDEBAR: state => {
        state.sidebar.opened = !state.sidebar.opened;
        state.sidebar.withoutAnimation = false;
        if (state.sidebar.opened) {
            Cookies.set('sidebarStatus', 1);
        } else {
            Cookies.set('sidebarStatus', 0);
        }
    },
    TOGGLE_MANUAL_SIDEBAR: (state, status) => {
        state.manualSideBar = status;
    },
    CLOSE_SIDEBAR: (state, withoutAnimation) => {
        Cookies.set('sidebarStatus', 0);
        state.sidebar.opened = false;
        state.sidebar.withoutAnimation = withoutAnimation;
    },
    TOGGLE_DEVICE: (state, device) => {
        state.device = device;
    },
    SET_LANGUAGE: (state, language) => {
        state.language = language;
        Cookies.set('language', language);
    },
    SET_SIZE: (state, size) => {
        state.size = size;
        Cookies.set('size', size);
    },
    SET_INTHEMECOLOR: (state, inThemeColor) => {
        state.inThemeColor = inThemeColor;
    },
    SET_OUTTHEMECOLOR: (state, outThemeColor) => {
        state.outThemeColor = outThemeColor;
    },
    SET_SIDEBARSPREAD: (state, sideBarSpread) => {
        state.sideBarSpread = sideBarSpread;
    },
    SET_CURRENTPATH: (state, path) => {
        state.currentPath = path;
    },
    TOOLBAR_OPENED: (state) => {
        state.toolbarOpened = true;
    },
    TOOLBAR_CLOSED: (state) => {
        state.toolbarOpened = false;
    },
    SET_MOUSE_IN_SIDEBAR:(state, mouseInSideBar)=>{
        state.mouseInSideBar = mouseInSideBar;
    },
    SET_MANUAL_SIDEBAR_STATUS:(state, status)=>{
        state.manualSideBarStatus = status;
    }
};

const actions = {
    toggleSideBar({ commit }) {
        commit('TOGGLE_SIDEBAR');
    },
    toggleManualSideBar({ commit }, status){
        commit('TOGGLE_MANUAL_SIDEBAR', status);
    },
    closeSideBar({ commit }, { withoutAnimation }) {
        commit('CLOSE_SIDEBAR', withoutAnimation);
    },
    toggleDevice({ commit }, device) {
        commit('TOGGLE_DEVICE', device);
    },
    setLanguage({ commit }, language) {
        commit('SET_LANGUAGE', language);
    },
    setSize({ commit }, size) {
        commit('SET_SIZE', size);
    },
    setInThemeColor({ commit }, inThemeColor) {
        commit('SET_INTHEMECOLOR', inThemeColor);
    },
    setOutThemeColor({ commit }, outThemeColor) {
        commit('SET_OUTTHEMECOLOR', outThemeColor);
    },
    setSideBarSpread({ commit }, sideBarSpread) {
        commit('SET_SIDEBARSPREAD', sideBarSpread);
    },
    setCurrentPath({ commit }, path) {
        commit('SET_CURRENTPATH', path);
    },
    setMouseInSideBar({ commit }, mouseInSideBar) {
        commit('SET_MOUSE_IN_SIDEBAR', mouseInSideBar);
    },
    setManualSideBarStatus({ commit }, status) {
        commit('SET_MANUAL_SIDEBAR_STATUS', status);
    },
    setToolBarStatus({commit}, status) {
        if (status) {
            commit('TOOLBAR_OPENED');
        } else {
            commit('TOOLBAR_CLOSED');
        }
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
