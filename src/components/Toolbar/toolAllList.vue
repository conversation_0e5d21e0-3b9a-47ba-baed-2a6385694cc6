<template>
    <div class="toolAllList">
        <el-button
            type="text" class="tool-setting" :style="drag ? 'color:#909199' : 'color:#1c61fc'"
            @click="clickSetting()"
        >
            自定义设置
        </el-button>
        <div v-if="drag" class="tool-tip">
            鼠标拖拽移动功能可调整显示顺序
        </div>
        <el-scrollbar id="scroll_bar">
            <div class="tool_options_container">
                <draggable
                    v-model="allDataArray" chosen-class="tool-option-chosen" :disabled="!drag" :force-fallback="false"
                    group="people" animation="1000" @start="onStart" @end="onEnd"
                >
                    <transition-group>
                        <div v-for="(item, index) in allDataArray" :key="index" class="tool_option_container">
                            <div v-if="item.index !== null" id="toolListOption" @click="operateTools(item)">
                                <svg-icon class="tool_logo" :icon-class="item.icon" />
                                <p class="tool_title">
                                    {{ item.title }}
                                </p>
                                <svg-icon v-if="drag" class="tool_drag" icon-class="tool-drag" />
                            </div>
                        </div>
                    </transition-group>
                </draggable>
            </div>
        </el-scrollbar>
        <div v-if="drag" class="toolListBottom">
            <el-button class="complete" type="primary" round @click="saveButton()">
                完&nbsp;&nbsp;&nbsp;&nbsp;成
            </el-button>
        </div>
    </div>
</template>
<script>
import draggable from 'vuedraggable';
export default {
    name: "ToolAllList",
    components: {
        draggable,
    },
    props: {
        allData: {
            type: Array,
            default: () => []
        },
    },
    data() {
        return {
            drag: false,
            allDataArray: this.allData,
            originalArray: this.allData,
        };
    },
    methods: {
        clickSetting() {
            this.drag = !this.drag;
        },
        operateTools(item) {

        },
        saveButton() {
            this.originalArray = this.allDataArray;
            this.drag = false;
        },

        onStart() {
            this.originalArray = this.allDataArray;
        },

        onEnd() {
            
        },
        closeDrawer() {
            this.drag = false;
            this.allDataArray = this.originalArray;
        }
    }
};
</script>
<style lang="scss" scoped>
.toolAllList {
    width: 100%;
}

::v-deep .el-scrollbar__wrap {
    overflow-x: scroll;
    overflow-y: auto;
}

.tool-setting {
    height: 16px !important;
    font-size: 12px;
    font-weight: 400;
    position: absolute;
    top: 25px;
    right: 20px;
}

.tool-tip {
    width: 100%;
    height: 36px;
    background-color: rgba(255, 228, 224, 0.80);
    padding: 10px 20px;
    line-height: 16px;
    font-size: 12px;
    color: #292b34;
}

.tool_options_container {
    border-top: 1px dashed rgba(41, 43, 52, 0.4);
    width: 320px;
    margin: 16px 20px;
    // border-left: 1px dashed black;
}

.tool_option_container {
    width: 80px;
    height: 80px;
    text-align: center;
    border-bottom: 1px dashed rgba(41, 43, 52, 0.4);
    border-right: 1px dashed rgba(41, 43, 52, 0.4);
    vertical-align: top;
    display: inline-block;
}

.tool-option-chosen {
    background-color: #fff;
    opacity: 1;
    border: 1px dashed rgba(41, 43, 52, 0.4);
    box-shadow: 0px 0px 8px 0px rgba(41,43,52,0.3);
}

.tool_option_container:hover {
    cursor: pointer;

    & .tool_logo {
        fill: #2f377f !important;
        stroke: #2f377f !important;
    }

    & .tool_title {
        color: #1c61fc;
    }
}

#toolListOption {
    width: 56px;
    height: 72px;
    position: relative;
    margin: 4px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.tool_option_container:nth-child(4n + 1) {
    border-left: 1px dashed rgba(41, 43, 52, 0.4);
}

.lastflag {
    vertical-align: top;
}

.tool_logo {
    width: 18px !important;
    height: 18px !important;
    margin: 17px 19px 9px 19px;
    fill: #1854db !important;
    stroke: #1854db !important;
}

.tool_title {
    margin: 0px;
    // font-family: MicrosoftYaHeiUI;
    font-size: 10px;
    color: #65677a;
    letter-spacing: 0;
    text-align: center;
    line-height: 12px;
    // transform:scale(0.9)
}
.tool_drag {
    width: 20px !important;
}

.toolListBottom {
    width: 100%;
    height: 120px;
    position: absolute;
    left: 0px;
    bottom: 0px;
    text-align: center;
    line-height: 120px;
    overflow-y: auto;
    background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 28%);
}

.complete {
    width: 216px;
}
</style>