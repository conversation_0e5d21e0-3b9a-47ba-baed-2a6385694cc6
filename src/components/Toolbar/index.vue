<!--
    ====================
    Toolbar(工具栏) 组件
    ====================
import ToolAllList from "./toolAllList.vue";
    TODO: 组件有待优化；
    功能：    首页右侧工具栏组件，工具栏功能的展示和编辑以及个性化工具栏内容的展示和使用；
    实现原因：每个系统中存在一些小的实用的工具，比如保费计算器，保单预览和详细信息展示等等，这些工具
            需要经常使用，所以设计布局的右侧增加工具栏，以便可以更加快捷的使用这些工具；
    实现方式： 1. 接受两个两个json数据，一个是用来记录全部存在的工具信息，另一个是用来记录当前用户选择的工具的信息
              2. 根据上述两个json进行血循环渲染出右侧菜单栏和菜单栏的增加删除操作页面并实现功能，最多可以选择七个
              3. 配合使用toolcantain组件实现组件插槽内容自定义和工具栏的内容点击切换功能；
              4. 规范的接送数据中：
                    index: 为当前工具的唯一标识,
                    icon: 当前工具的图标,
                    title: 当前组件的名称,
                    name: 当前组件需要自定义跳转的文件的名称

 -->
<template>
    <div class="picc-toolbar" style="width: 56px; background: rgb(241, 242, 244)">
        <div id="toolSelectList" :style="'top:' + top + ';right:' + right + ';'">
            <div
                v-for="(item, index) in hadToolsArray"
                :key="index"
                :class="item.name"
                class="toolbarIconcontainer customTools"
                @click="clickToolOption(item.title, item.name, item.type, $event)"
            >
                <svg-icon :icon-class="item.icon" class="toolbarIcon" />
                <p class="had_tools_title">
                    {{ item.title }}
                </p>
            </div>
            <div class="toolbarIconcontainer toolList" @click="clickToolOption('工具栏列表', 'toolList', '0', $event)">
                <svg-icon icon-class="Tools-add-nor" class="toolbarIcon" />
                <p class="operate_tools_title">
                    操作
                </p>
            </div>
            <div class="toolbarIconcontainer toolAllList" @click="clickToolOption('全部功能', 'toolAllList', '0', $event)">
                <svg-icon :icon-class="tool_all_open ? 'tool-close':'tool-open'" class="toolbarIcon" />
                <p class="operate_tools_title">
                    {{ tool_all_open ? "收起":"展开" }} 
                </p>
            </div>
        </div>

        <el-drawer
            id="toolDrawerId"
            ref="tooldrawer"
            :title="title"
            :modal="isModal"
            :visible.sync="drawer"
            :direction="direction"
            :before-close="handleClose"
            size="360px"
            class="tool_drawer"
            :style="'margin-top:' + toolDrawerTop + '!important'"
            :wrapper-closable="true"
            :modal-append-to-body="false"
            :show-close="selectOption != 'toolAllList'"
            @opened="openedCallback"
            @closed="closedCallback"
        >
            <!-- <el-drawer
            id="toolDrawer"
            ref="tooldrawer"
            :visible.sync="drawer"
            :direction="direction"
            size="360px"
            class="tool_drawer"
            @opened="openedCallback"
            @closed="closedCallback"
        > -->
            <slot></slot>

            <div class="drawer_content">
                <toolList v-if="selectOption === 'toolList'" :all-data="allToolsArray" :had-data="hadToolsArray" @toolBarSave="toolBarSave" />
                <tool-all-list v-if="selectOption === 'toolAllList'" ref="toolAllList" :all-data="toolAllListArray" />
            </div>
        </el-drawer>
    </div>
</template>
<script>
import toolList from "./toolList";
import ToolAllList from "./toolAllList";
export default {
    name: "Toolbar",
    components: {
        toolList,
        ToolAllList
    },
    props: {
        allTools: {
            type: Array,
            default: () => []
        },
        hadTools: {
            type: Array,
            default: () => []
        },
        top: {
            type: String,
            default: () => "90px"
        },
        right: {
            type: String,
            default: () => ""
        },
        openTypePush: {
            type: Boolean,
            default: false
        },
        toolDrawerTop: {
            type: String,
            default: () => "56px"
        }
    },
    data() {
        return {
            allToolsArray: this.allTools,
            isModal: false,
            drawer: false,
            direction: "rtl",
            appendToBody: true,
            selectOption: "",
            title: "",
            toolBarDom: null,
            hadToolsArray: this.hadTools,
            toolAllListArray:[],
            tool_all_open: false,
            tool_all_name: "展开",
        };
    },
    watch: {
        drawer(newVal) {
            // 用户体验处设计规范v1.3优化点，打开工具栏，页面内容从挤压改为不动
            // this.$store.dispatch("app/setToolBarStatus", newVal);
        }
        // 监听路由变化每次变化都会关闭路由和抽屉后面的占位块
        // $route() {
        //     this.$refs.tooldrawer.closeDrawer();
        //     document.getElementsByClassName("toolPosition")[0].style.width = "56px";
        // }
    },
    created() {
        for (let i = 0; i < this.allToolsArray.length; i++) {
            for (let j = 0; j < this.allToolsArray[i].children.length; j++) {
                const flag = this.dealDataIsHad(this.allToolsArray[i].children[j], this.hadToolsArray);

                if (flag) {
                    this.allToolsArray[i].children[j].type = 1;
                } else {
                    if (this.hadToolsArray.length === 7) {
                        this.allToolsArray[i].children[j].type = 2;
                    } else {
                        this.allToolsArray[i].children[j].type = 0;
                    }
                }
                this.toolAllListArray.push(this.allToolsArray[i].children[j]);
            }
        }
    },
    methods: {
        handleClose(done) {
            done();
        },
        /**
         * 判断当前的工具是否在已存在的工具栏中
         */
        dealDataIsHad(item, dataArray) {
            let flag = false;
            if (dataArray.length !== 0) {
                for (let i = 0; i < dataArray.length; i++) {
                    // for (let j = 0; j < dataArray[i].children.length; j++) {
                    if (dataArray[i].index === item.index) {
                        flag = true;
                    }
                    // }
                }
            }
            return flag;
        },
        //点击侧面工具栏
        clickToolOption(title, name, type, e) {
            if (name === 'toolAllList') {
                this.tool_all_open = !this.tool_all_open;
            }
            var currClassList = document.getElementsByClassName(name)[0].classList;
            if (currClassList.value.includes('active')) {
                document.getElementsByClassName(name)[0].classList.remove("active");
                this.closeToolbarContain();
                return;
            }
            var toolsArray = document.getElementsByClassName("toolbarIconcontainer");
            for (let i = 0; i < toolsArray.length; i++) {
                toolsArray[i].classList.remove("active");
            }
            document.getElementsByClassName(name)[0].classList.add("active");
            this.selectOption = name;
            this.title = title;
            // if(document.getElementsByClassName(name)[0].classList)
            // this.drawer = !this.drawer;
            this.drawer = true;
            // if (this.drawer === true) {
            // if (title === "工具栏列表") {
            //     this.$refs.tooldrawer.closeDrawer();
            // }
            // } else {
            // if (!type || type === "0"|| type === "1") {
            // this.drawer = true;
            // } else {
            //     const item = {
            //         title: title,
            //         name: name,
            //         type: type
            //     };
            //     this.$emit("notOpenDrawer", item, true);
            // }
            // }
        },

        toolBarSave(hadDataArray) {
            this.$refs.tooldrawer.closeDrawer();
            this.closedCallback();
            // for(let i=0;i<hadDataArray.length;i++){
            //     delete hadDataArray[i].type;
            // }
            this.hadToolsArray = [...hadDataArray];
            this.$emit("saveHadTools", this.hadToolsArray);
        },
        closeToolbarContain() {
            this.$refs.tooldrawer.closeDrawer();
        },
        //抽屉打开回调
        openedCallback() {
            // document.getElementsByClassName("tool_drawer")[0].style.zIndex = "2001";
        },
        //抽屉关闭回调
        closedCallback() {
            var toolsArray = document.getElementsByClassName("toolbarIconcontainer");
            for (let i = 0; i < toolsArray.length; i++) {
                toolsArray[i].classList.remove("active");
            }
            if (this.selectOption === 'toolAllList') {
                this.tool_all_open = false;
                this.$refs.toolAllList.closeDrawer();
            }
        }
    }
};
</script>
<style lang="scss">
#toolBar {
}
#el-drawer__title span {
    outline: none;
}
#toolSelectList {
    float: right;
    position: fixed;
    height: 100%;
    z-index: 6;
    box-shadow: inset 1px 0px 0px 0px rgba(226,226,226,1);
}
.toolbarIconcontainer {
    width: 56px;
    height: 72px;
    text-align: center;
    cursor: pointer;
    & .toolbarIcon {
        margin: 16px auto 8px;
        width: 20px !important;
        height: 20px !important;
    }
    & .had_tools_title {
        margin: 0;
        font-size: 12px;
        // color: #65677a;
        letter-spacing: 0;
        text-align: center;
        line-height: 12px;
        display: inline-block;
        width: 48px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    & .operate_tools_title {
        margin: 0;
        font-size: 10px;
        color: #292b34;
        letter-spacing: 0;
        text-align: center;
        line-height: 12px;
        display: inline-block;
        width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
// .toolbarIconcontainer:hover{
//     // background-color: #292B34;
//     // opacity: 0.4;
//     background: red;
//     & .toolbarIcon{
//     }
// }
.customTools:hover {
    & .had_tools_title {
        // font-family: microsof;
        font-size: 12px;
        color: #1c61fc;
        letter-spacing: 0;
        text-align: center;
        line-height: 12px;
    }
    & .toolbarIcon {
        fill: #2f377f !important;
        stroke: #2f377f !important;
    }
}
// .operateTools:hover{
//   background: rgba(28,97,252,0.08);
//   & .operate_tools_title{
//     font-weight: bold;
//     font-size: 10px;
//     color: #292B34;
//     letter-spacing: 0;
//     text-align: center;
//     line-height: 12px;
//   }
// }
.toolbarIcon {
    fill: #1854db !important;
    stroke: #1854db !important;
}
#toolDrawerId {
    display: flex;
    flex-direction: row-reverse;
    // margin-top: 93px !important;
    margin-right: 72px;
    & > .el-drawer__body {
        height: 100% !important;
        overflow-y: auto;
    }
}
.drawerEdit {
    width: 360px !important;
    left: auto !important;
}
//el-dialog__wrapper tool_drawer
// #toolDrawerId .el-drawer__container {
    // width: 360px !important;
// }
.tool_drawer .el-drawer__header {
    margin-bottom: 20px;
    font-weight: bold;
    font-size: 14px;
    color: #292b34;
    letter-spacing: 0;
    line-height: 16px;
}
.drawer_content {
    height: auto !important;
    transition: opacity 0.28;
    padding: 0 !important;
}
.toolPosition {
    width: 56px;
    height: 0;
    // transition:width 0.1s;
}
.active {
    background: rgba(28, 97, 252, 0.08);
    font-weight: bold;
    color: #292b34;
    & .operate_tools_title {
        font-weight: bold;
        font-size: 10px;
        color: #292b34;
        letter-spacing: 0;
        text-align: center;
        line-height: 12px;
    }
}
.el-drawer__close-btn {
    width: 32px;
}
.tool_drawer .el-drawer__body::-webkit-scrollbar{
    width: 8px;
    height: 8px;
}
.tool_drawer .el-drawer__body::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 10px;
    -webkit-box-shadow: inset 1px 1px 0 #dddddd;
}
.tool_drawer .el-drawer__body::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: white;
}
/**
 *媒体查询样式调整
*/
@media screen and (max-width: 1617px) {
    #toolSelectList {
        background-color: #f1f2f4;
    }
    #toolDrawerId {
        margin-right: 56px !important;
    }
}
@media screen and (min-width: 1617px) {
    #toolSelectList {
        right: calc((100vw - 1600px) / 2 - (100vw - 100%)) !important;
        background-color: #f1f2f4;
    }
    #toolDrawerId {
        margin-right: calc((100vw - 1600px) / 2 - (100vw - 100%) + 56px) !important;
    }
}

@media screen and (min-width: 1919px) {
    #toolSelectList {
        right: calc((100vw - 1600px) / 2 - (100vw - 100%)) !important;
        background-color: #f1f2f4;
    }
    #toolDrawerId {
        margin-right: calc((100vw - 1600px) / 2 - (100vw - 100%) + 56px) !important;
    }
}
</style>
