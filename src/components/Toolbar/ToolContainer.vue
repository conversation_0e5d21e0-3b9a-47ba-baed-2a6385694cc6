
<!--
    ====================
    toolContain 组件
    ====================
    TODO: 组件有待优化；
    功能：    配合toolbar组件使用，实现工具栏的切换；
    实现原因：再工具栏中使用插槽实现内容的自定义并且可以通过点击工具栏来进行切换；
    实现方式： 1. 内部定义一个插槽，可以以内嵌 任何页面模板
              2. 使用v-if对由父组件的传过来的selectOption的值进行v-if判断显示还是隐藏
 -->
<template>
    <div v-if="$parent.$parent.selectOption === thisOptionName" class="drawer_content">
        <slot></slot>
    </div>
</template>
<script>
export default {
    name: 'ToolContainer',
    props: {
        thisOptionName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {};
    },
    methods: {
        getSelectOptionName() {
            return this.$parent.selectOption;
        }
    }
};
</script>
<style lang="scss">
.drawer_content{
    // height: 100%;
    transition: opacity 0.28;
}
</style>
