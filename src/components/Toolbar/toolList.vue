<template>
    <div id="toolList">
        <el-scrollbar id="scroll_bar">
            <!-- <div id="main_tools"> -->
            <div v-for="(items, index) in allData" :key="index" class="tool_group">
                <p class="toolGroupTitle">
                    {{ items.groupTitle }}
                </p>
                <div class="tool_options_container">
                    <div v-for="(item, subIndex) in items.children" :key="subIndex" class="tool_option_container">
                        <!-- <toolOption :option-data="item" /> -->

                        <div v-if="item.index !== null" id="toolListOption" @click="operateTools(item)">
                            <!-- <img v-if="item.type===0" src="./image/png/Add tool_Add.png" alt="" class="operation_img">
              <img v-if="item.type===1" src="./image/png/Add tool_Delete.png" alt="" class="operation_img">
              <img v-if="item.type===2" src="./image/png/Add tool_Add_disable.png" alt="" class="operation_img"> -->
                            <i v-if="item.type === 0" class="operation_img picc-icon picc-icon-add-o-sm-green"></i>
                            <i v-if="item.type === 1" class="operation_img picc-icon picc-icon-remove-sm"></i>
                            <i v-if="item.type === 2" class="operation_img picc-icon picc-icon-add-o-sm-green disabled"></i>
                            <div>
                                <!-- <img class="tool_logo" src="@/assets/Tools-OCR-hover.png"> -->
                                <svg-icon class="tool_logo" :icon-class="item.icon" />
                                <p class="tool_title">
                                    {{ item.title }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- </div> -->
        </el-scrollbar>
        <div class="toolListBottom">
            <el-button class="complete" type="primary" round @click="saveButton">
                完&nbsp;&nbsp;&nbsp;&nbsp;成
            </el-button>
        </div>
    </div>
</template>
<script>
// import toolOption from './toolOption'
export default {
    components: {
        // toolOption
    },
    props: {
        allData: {
            type: Array,
            default: () => []
        },
        hadData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            allDataArray:[...this.allData],
            hadDataArray:[...this.hadData]
        };
    },
    created() {
        for (let i = 0; i < this.allData.length; i++) {
            const length = this.allData[i].children.length % 4;
            if (length !== 0) {
                const addOptionNum = 4 - length;
                for (let j = 0; j < addOptionNum; j++) {
                    const option = { index: null, icon: "", title: "", url: "", type: "3" };
                    this.allDataArray[i].children.push(option);
                }
            }
        }
        this.$forceUpdate();
        this.$nextTick(function() {
            const drawerTool = document.getElementById("toolDrawerId").getElementsByClassName("el-drawer__container")[0].offsetHeight;
            const drawerToolheader = document.getElementById("toolDrawerId").getElementsByClassName("el-drawer__header")[0].offsetHeight;
            document.getElementById("scroll_bar").style.height = drawerTool - drawerToolheader - 121 + "px";
        });
    },
    methods: {
        operateTools(item) {
            if (item.type === 0 || item.type === 1) {
                if (item.type === 0) {
                    this.hadDataArray.push(item);
                    this.dealDataType(item, 1, "add");
                    if (this.hadDataArray.length >= 7) {
                        this.dealDataToDisable("add", 2);
                    }
                } else {
                    this.dealDataType(item, 0, "delete");
                    for (let i = 0; i < this.hadDataArray.length; i++) {
                        if (item.index === this.hadDataArray[i].index) {
                            this.hadDataArray.splice(i, 1);
                            this.$forceUpdate();
                        }
                    }
                    if (this.hadDataArray.length < 7) {
                        this.dealDataToDisable("add", 0);
                    }
                }
            }
        },
        /**
         * 修改全部的工具栏中的type值
         */
        dealDataType(item, type) {
            for (let i = 0; i < this.allData.length; i++) {
                for (let j = 0; j < this.allData[i].children.length; j++) {
                    if (this.allDataArray[i].children[j].index === item.index) {
                        this.allDataArray[i].children[j].type = type;
                    }
                }
            }
            this.$forceUpdate();
        },
        /**
         * 对于工具栏到达七个时的特殊处理
         */
        dealDataToDisable(operate, type) {
            for (let i = 0; i < this.allData.length; i++) {
                for (let j = 0; j < this.allData[i].children.length; j++) {
                    let flag = false;
                    for (let a = 0; a < this.hadDataArray.length; a++) {
                        if (this.hadDataArray[a].index === this.allData[i].children[j].index) {
                            flag = true;
                        }
                    }
                    if (!flag) {
                        this.allDataArray[i].children[j].type = type;
                    }
                }
            }
        },
        saveButton() {
            this.$emit("toolBarSave",this.hadDataArray);
        }
    }
};
</script>
<style lang="scss">
#toolList {
    width: 330px;
    margin: 0 auto;
}
#toolList .el-scrollbar__wrap {
    overflow-x: hidden;
    overflow-y: auto;
}
.tool_group {
    width: 100%;
    margin-bottom: 8px;
    & .tool_option_container {
        width: 80px;
        height: 80px;
        padding: 0 auto;
        text-align: center;
        border-bottom: 1px dashed rgba(41, 43, 52, 0.4);
        border-right: 1px dashed rgba(41, 43, 52, 0.4);
        vertical-align: top;
        display: inline-block;
    }
}
.tool_options_container {
    border-top: 1px dashed rgba(41, 43, 52, 0.4);
    width: 320px;
    // border-left: 1px dashed black;
}
.tool_logo {
    fill: #1854db !important;
    stroke: #1854db !important;
}
.tool_option_container:hover {
    cursor: pointer;
    & .tool_logo {
        fill: #2f377f !important;
        stroke: #2f377f !important;
    }
    & .tool_title {
        color: #1c61fc;
    }
}
#main_tools {
    overflow-y: auto;
    width: 350px;
}
.toolGroupTitle {
    // font-family: MicrosoftYaHeiUI-Bold;
    font-size: 12px;
    color: #292b34;
    letter-spacing: 0;
    line-height: 12px;
    font-weight: bold;
    line-height: 28px;
    margin: 0px;
    display: block;
    width: 321px;
}

#toolListOption {
    width: 56px;
    height: 72px;
    position: relative;
    margin: 4px 12px;
    display: inline-block;
}
.tool_option_container:nth-child(4n + 1) {
    border-left: 1px dashed rgba(41, 43, 52, 0.4);
}
.lastflag {
    vertical-align: top;
}
.tool_logo {
    width: 18px;
    height: 18px;
    margin: 21px 19px 9px 19px;
}
.tool_title {
    margin: 0px;
    // font-family: MicrosoftYaHeiUI;
    font-size: 10px;
    color: #65677a;
    letter-spacing: 0;
    text-align: center;
    line-height: 12px;
    // transform:scale(0.9)
}
.operation_img {
    position: absolute;
    top: 0px;
    right: -5px;
}
.toolListBottom {
    width: 100%;
    height: 120px;
    position: absolute;
    left: 0px;
    bottom: 0px;
    text-align: center;
    line-height: 120px;
    overflow-y: auto;
    background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 28%);
}
.complete {
    width: 216px;
}
</style>
