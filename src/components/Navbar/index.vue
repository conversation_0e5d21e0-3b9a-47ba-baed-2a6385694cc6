<template>
    <div class="picc-navbar" :class="{ 'fixed-nav': isFixed }">
        <div class="info">
            <slot name="crumb"></slot>
        </div>
        <div class="right-menu">
            <div class="navbar-search">
                <slot name="search-input"></slot>
            </div>
            <div>
                <theme-select @theme-change="themeChange" />
            </div>
            <div class="navbar-operation">
                <span v-show="newInfo" class="new-info-notice"></span>
                <el-popover
                    ref="new-info-pop" placement="bottom" width="372" popper-class="picc-popover-message"
                    trigger="hover"
                >
                    <div class="picc-popover-content">
                        <slot name="bell-message"></slot>
                    </div>
                    <div class="picc-popover-operation">
                        <slot name="bell-operation"></slot>
                    </div>
                    <i slot="reference" class="picc-icon picc-icon-bell"></i>
                </el-popover>
            </div>
            <div class="navbar-brief-info">
                <img v-popover:changeworkState :src="personInfo.picture" class="user-avatar" />
                <span :class="['work-state', { working: state }]"></span>
                <el-popover
                    ref="changeworkState" v-model="popoverShow" popper-class="work-state-popover"
                    placement="right-start" trigger="click" :visible-arrow="false" :offset="60"
                >
                    <div class="work-state-pop">
                        <ul>
                            <li
                                v-for="(value, idx) in workStateInner" :key="idx" :class="{ selected: value.select }"
                                @click="selectState(idx)"
                            >
                                <span
                                    :class="['state-select', { 'selected picc-icon picc-icon-success-square': value.select }]"
                                ></span>
                                <span :class="['state-dot', { selected: value.select }]"></span>
                                <span class="state-info">{{ value.label }}</span>
                            </li>
                        </ul>
                    </div>
                </el-popover>
                <span class="navbar-brief-info-department">{{ personInfo.position }}</span>
                <span class="navbar-brief-info-name">{{ personInfo.name }}</span>
            </div>
            <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
                <div class="avatar-wrapper">
                    <i class="el-icon-caret-bottom"></i>
                </div>
                <el-dropdown-menu slot="dropdown" class="picc-nav-dropdown-menu">
                    <slot name="dropdown-menu-list"></slot>
                    <el-dropdown-item divided>
                        <span v-popover:loginOut style="display: block" @click="loginOutFn">退出账号</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
    </div>
</template>

<script>
// import { mapGetters } from "vuex";

export default {
    name: "Navbar",
    props: {
        // 左侧校色切换导航数据
        leftData: {
            type: Array,
            default: () => {
                return [];
            }
        },
        personInfo: {
            type: Object,
            default: () => {
                return {
                    name: "孙大圣",
                    position: "部门经理",
                    newInfo: false,
                    picture: require("@/assets/tangqing.gif")
                };
            }
        },
        newInfo: {
            type: Boolean,
            default: false
        },
        isFixed: {
            type: Boolean,
            default: false
        },
        workState: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            willSelect: false,
            popoverShow: false,
            loginOut: false,
            workStateInner: this.workState,
            index: 0
        };
    },
    computed: {
        // ...mapGetters(["sideBarSpread", "manualSideBar"]),
        state: function () {
            let i = this.workStateInner.findIndex((c) => {
                return c.select == true;
            });
            return i === 0 ? true : false;
        }
    },
    watch: {},
    methods: {
        selectState(index) {
            this.popoverShow = false;
            this.$emit("changeStateBack", index);
        },
        loginOutFn() {
            // this.loginOut = false;
            this.$emit("loginOutBack", "点击了确定退出");
        },
        // 点击左侧角色切换导航
        changeTab(item, idx) {
            this.index = idx;
            this.$emit("leftDataIndex", this.leftData[idx]);
        },
        themeChange(data) {
            if (data === "eye-care") {
                document.getElementById("currentStyle").setAttribute("href", "/index.eye-care.css");
            } else if (data === 'eye-youth') {
                document.getElementById("currentStyle").setAttribute("href", "/index.eye-youth.css");
            } else {
                document.getElementById("currentStyle").setAttribute("href", "/index.css");
            }
        }
    }
};
</script>

