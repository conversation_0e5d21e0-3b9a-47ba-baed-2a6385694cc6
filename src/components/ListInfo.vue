<template>
    <div
        class="picc-list-info"
        :class="size === 'large' ? 'picc-list-info-large' : ''"
    >
        <div class="list-info-header">
            <div
                class="header-text"
                :class="size != 'large' ? 'header-text-border' : ''"
            >
                {{ title }}
            </div>
            <slot name="header-button"></slot>
        </div>
        <div class="list-info-content">
            <slot name="list-info-content"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: "Index",
    props: {
        title: {
            type: String,
            default: ""
        },
        size: {
            type: String,
            default: "small"
        }
    }
};
</script>

<style scoped lang="scss">
.picc-list-info {
    width: 288px;
    background-color: #fafafa;
    box-shadow: inset 1px -1px 0 0 #e2e2e2;
    &.picc-list-info-large {
        width: 416px;
        background: #ffffff;
        border-radius: 4px;

        .list-info-header {
            .header-text {
                margin: 18px 0 0 24px;
                font-weight: bold;
            }
        }
        .list-info-content {
            height: 208px;
            padding: 10px 24px 20px 24px;

            .item {
                line-height: 16px;
                margin-bottom: 16px;
            }
        }
    }

    .list-info-header {
        height: 52px;
        overflow: hidden;
        text-align: left;

        .header-text {
            float: left;
            margin: 24px 0 0 24px;
            font-family: MicrosoftYaHeiUI;
            font-size: 14px;
            color: #292b34;
            line-height: 16px;
        }
        .header-text-border:after {
            content: "";
            display: inline-block;
            height: 12px;
            border-left: 1px solid #dddddd;
            margin-left: 8px;
        }
    }

    .list-info-content {
        height: 224px;
        padding: 16px 40px 28px 24px;

        .item {
            overflow: hidden;
            margin-bottom: 12px;
            line-height: 20px;
            &:last-child {
                margin-bottom: 0;
            }
            &.warn {
                color: #dc402b;
            }

            .label {
                float: left;
                font-size: 14px;
                color: #292b34;
            }

            .number {
                float: right;
                font-size: 20px;
            }

            .value {
                float: right;
                font-size: 14px;
                color: #65677a;
            }
        }
    }
}
</style>
