<template>
    <div class="warranty-list">
        <ul :class="expand ? 'shrink' : 'expand'">
            <li v-for="(item, index) in data" :key="index">
                {{ item }}
            </li>
        </ul>
        <div v-if="data.length > 10" class="warranty-list-expand" @click="expand = !expand">
            <span> {{ expand ? "展开全部" : "收起" }} <i :class="expand ? 'picc-icon picc-icon-dropdown' : 'picc-icon picc-icon-up'"></i> </span>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            expand: this.data.length > 10
        };
    }
};
</script>
<style lang="scss">
.warranty-list {
    width: 67%;
    font-size: 0;
    display: inline-block;
    vertical-align: top;
    ul {
        padding: 0;
        margin: 0;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    .warranty-list-expand {
        font-size: 12px;
    }
    .shrink {
        height: 156px;
        overflow: hidden;
    }
    .expand {
        height: auto;
        overflow: auto;
    }
    .warranty-list-expand {
        margin-bottom: 20px;
        margin-top: -6px;
        span {
            line-height: 20px;
            height: 20px;
            display: inline-block;
        }
        .picc-icon {
            vertical-align: top;
        }
        .picc-icon-up {
            // background-image: url(picc-sprite.png);
            display: inline-block;
            background-position: -180px -452px;
            transform: rotate(180deg);
            transition: transform 0.1s ease-in;
            width: 20px;
            height: 20px;
        }
        .picc-icon-dropdown {
            transition: transform 0.1s ease-in;
        }
    }
}
</style>
