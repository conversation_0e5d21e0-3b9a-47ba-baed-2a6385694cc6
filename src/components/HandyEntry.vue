<!--
    ===============
    结果页提示 组件
    ===============
    功能：    结果页提示
    实现方式： 1. 一共有三种状态 success | warning | error；
              2. 一共设置有两个slot，因为结果页主体部分和message的距离不一致，所以
                 采用slot来灵活搭配；
              3. 主体部分的slot名称为body, 底部按钮部分的slot名称为footer
              4. @type为结果页的类型 success | warning | error；
              5. @message为结果页图标下方要提示的内容区域；
 -->
<template id="">
    <div class="picc-handy-entry">
        <div class="picc-handy-entry_title">
            <h2>便捷入口 {{ entries.message }}</h2>
        </div>
        <div class="picc-handy-entry_content">
            <div v-for="(item, index) in entries" :key="index" class="picc-handy-entry_item">
                <div :class="item.icon"></div>
                <div class="picc-hand-entry_item_title">
                    {{ item.title }}
                </div>
            </div>
            <!--  -->
            <div class="picc-handy-entry_item picc-he-item-add">
                <div class="picc-hand-entry_item_test_icon"></div>
                <div class="picc-hand-entry_item_title">
                    添加
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'HandyEntry',
    props: {
        entries: {
            type: Array,
            default: function() {
                return [
                    {
                        // icon: 'picc-hand-entry_item_test_icon',
                        // title: '测试入口',
                        // link: '/#/'
                    }
                ];
            }
        },
        title: {
            type: String,
            default: '便捷入口'
        },
        entryAdd: {
            type: Boolean,
            default: true
        }
    },
    mounted () {
        // console.log(this.$props.entries);
    }
};
</script>
<style lang="scss">

    .picc-handy-entry {
        width: 280px;
        height: 280px;
        // color: $--PICC-operation-color-blue;
        border-left: 1px solid #e2e2e2;
        border-bottom: 1px solid #e2e2e2;
        background:  #fafafa;
        //
        .picc-handy-entry_title {
            height: 52px;
            h2 {
                margin: 0;
                font-size: 14px;
                font-weight: normal;
                padding: 20px 0 10px 12px;
                text-align: left;
            }
        }
        .picc-handy-entry_content {
            display: flex;
            flex-flow: wrap;
        }
        .picc-handy-entry_item {
            width: 68px;
            height: 68px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .picc-hand-entry_item_test_icon {
            background: url(../assets/PICC.png) no-repeat;
            background-size: contain;
            width: 28px;
            height: 28px;
        }
        .picc-hand-entry_item_title {
            color: #292b34;
            line-height: 12px;
            font-size: 10px;
            padding-top: 4px;
        }
    }
    //
</style>
