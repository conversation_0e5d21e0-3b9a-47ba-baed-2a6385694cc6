<template>
    <el-dropdown trigger="click" class="international" @command="handleSetLanguage">
        <div><svg-icon class-name="international-icon" icon-class="language" />&nbsp;语言</div>
        <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :disabled="language === 'zh'" command="zh">
                中文简体
            </el-dropdown-item>
            <el-dropdown-item :disabled="language === 'en'" command="en">
                English
            </el-dropdown-item>
            <el-dropdown-item :disabled="language === 'zhTW'" command="zhTW">
                繁体
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script>
export default {
    computed: {
        language() {
            return this.$store.getters.language;
        }
    },
    methods: {
        handleSetLanguage(lang) {
            this.$i18n.locale = lang;
            this.$store.dispatch("app/setLanguage", lang);
            var message = lang === "zh" ? "切换中文简体成功" : lang === "zhTW"? "切换中文繁体成功" : "Switch Language Success";
            this.$message({
                message: message,
                type: "success"
            });
        }
    }
};
</script>
