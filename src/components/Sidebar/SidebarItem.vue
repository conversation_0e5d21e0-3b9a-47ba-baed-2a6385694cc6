<template>
    <div
        ref="sidebar-items"
        class="picc-sidebar-item"
        :class="{ 'picc-sidebar-item-active': sidebarSubItemsShow }"
        @mouseover="handleMouseEnter"
        @mouseleave="handleMouseLeave"
    >
        <router-link v-if="route.children && route.alwaysShow" tag="a" :to="path">
            <svg-icon class="pointer-events-none" :icon-class="meta.icon" />
            <span class="pointer-events-none"> {{ meta.title }}</span>
        </router-link>
        <!--如果不想有二级菜单的时候一级菜单可点击，就用下面这个-->
        <!-- <a-->
        <!--    v-if="route.children && route.alwaysShow"-->
        <!--    data-no-action="true"-->
        <!--    :class="{'router-link-exact-active router-link-active': $route.path.startsWith(path)}"-->
        <!-- >-->
        <!--     <svg-icon class="pointer-events-none" :icon-class="meta.icon" />-->
        <!--     <span class="pointer-events-none"> {{ meta.title }}</span>-->
        <!--</a>-->

        <router-link v-else-if="route.children && route.children.length === 1" :to="route.path + '/' + route.children[0].path">
            <svg-icon class="pointer-events-none" :icon-class="meta.icon" />
            <span class="pointer-events-none">{{ route.children[0].meta.title }}</span>
        </router-link>

        <router-link v-else-if="!route.hidden" tag="a" :to="route.path">
            <svg-icon class="pointer-events-none" :icon-class="meta.icon" />
            <span class="pointer-events-none">{{ meta.title }} </span>
        </router-link>
        <el-scrollbar v-if="route.children && route.alwaysShow" v-show="sidebarSubItemsShow" ref="scrollBar" style="max-width: 864px">
            <div ref="sidebarSubContainer" style="height: 100%">
                <div v-if="route.alwaysShow || (route.children.length > 1 && !route.hidden)" style="height: 100%">
                    <div v-for="(item, idx) in newRoutesSort" :key="idx" class="picc-sidebar-sub">
                        <div v-for="(subItem, subIndex) in item" v-show="!subItem.hidden" :key="subIndex" class="picc-sidebar-sub--item">
                            <!-- {{ subItem }} -->
                            <div v-if="!subItem.hidden" class="">
                                <div v-if="subItem.children" :class="subItem.children ? 'has-sub-title' : ''">
                                    <h2 :title="subItem.meta.title">
                                        {{ subItem.meta.title }}
                                    </h2>
                                    <div v-for="(child, childIndex) in subItem.children" :key="childIndex" class="">
                                        <router-link
                                            v-if="!child.hidden"
                                            :to="(path == '/' ? '' : path) + '/' + subItem.path + '/' + child.path"
                                            :title="child.meta.title"
                                        >
                                            {{ child.meta.title }}
                                        </router-link>
                                    </div>
                                </div>
                                <div v-else class="">
                                    <router-link :to="(path == '/' ? '' : path) + '/' + subItem.path" :title="subItem.meta.title">
                                        {{ subItem.meta.title }}
                                    </router-link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-scrollbar>
    </div>
</template>
<script>
import { debounce } from "@/utils";

export default {
    name: "SidebarItem",
    props: {
        // 是否展示子菜单
        sidebarSubItemsShow: {
            type: Boolean,
            default: false,
            required: false
        },
        // 当前菜单序号
        index: {
            type: Number,
            default: -9999,
            required: false
        },
        // 下一个展示菜单的序号
        nextIndex: {
            type: Number,
            default: -9999,
            required: false
        },
        path: {
            type: String,
            required: true,
            default: function () {
                return "";
            }
        },
        meta: {
            type: Object,
            required: true
        },
        route: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            sidebarSubItemsCloseTimeout: null,
            newRoutesSort: []
        };
    },
    mounted() {
        // 增加功能，点击二级导航自动关闭侧边导航
        if(this.$refs['sidebar-items']){
            this.$refs['sidebar-items'].addEventListener('click', this.sidebarItemClickHandler);
        }
        window.addEventListener("resize", this.computeRouteGroup);
        if(this.$refs.sidebarSubContainer) {
            // 火狐浏览器
            this.$refs.sidebarSubContainer.addEventListener("DOMMouseScroll", this.handleScrollBarScroll);
            this.$refs.sidebarSubContainer.addEventListener("mousewheel", this.handleScrollBarScroll);
        }
        this.computeRouteGroup();
    },
    beforeDestroy() {
        if(this.$refs['sidebar-items']){
            this.$refs['sidebar-items'].removeEventListener('click', this.sidebarItemClickHandler);
        }
        if(this.$refs.sidebarSubContainer) {
            this.$refs.sidebarSubContainer.removeEventListener("DOMMouseScroll", this.handleScrollBarScroll);
            this.$refs.sidebarSubContainer.removeEventListener("mousewheel", this.handleScrollBarScroll);
        }
        window.removeEventListener("resize", this.computeRouteGroup);
    },
    methods: {
        handleMouseEnter() {
            if (this.sidebarSubItemsShow) {
                if (this.nextIndex !== this.index) {
                    this.$emit("enter", this.index);
                }
                return;
            }
            if (this.sidebarSubItemsCloseTimeout) {
                clearTimeout(this.sidebarSubItemsCloseTimeout);
                this.sidebarSubItemsCloseTimeout = null;
            }
            this.sidebarSubItemsPrepareClose = false;
            this.$emit("enter", this.index);
        },
        handleMouseLeave(e) {
            // 有些浏览器滚轮滚动时会触发mouseleave事件，这个还跟电脑性能有关系，设置个50ms应该差不多，不行再调大点
            if (e.toElement != null && e.toElement !== e.fromElement) {
                this.$emit("leave", this.index);
            } else if (e.toElement == null) {
                if (this.sidebarSubItemsCloseTimeout) {
                    clearTimeout(this.sidebarSubItemsCloseTimeout);
                }
                this.sidebarSubItemsCloseTimeout = setTimeout(() => {
                    this.$emit("leave", this.index);
                }, 50);
            }
        },
        handleScrollBarScroll(e) {
            e.stopPropagation();
            e.preventDefault();
            this.handleScrollBarScrollAction(e);
        },
        // 节流处理
        handleScrollBarScrollAction: debounce(
            function (e) {
                const wrap = this.$refs.scrollBar.wrap;
                const WIDTH = 288;
                const animation = (curr, offset, time) => {
                    requestAnimationFrame(() => {
                        wrap.scrollLeft += offset;
                        this.$refs.scrollBar.handleScroll();
                        if (curr < time) animation(curr + 1, offset, time);
                    });
                };
                const delta = e.wheelDelta || -e.detail;
                if (delta < -1) {
                    // 向下滚
                    const maxScrollLeft = (this.newRoutesSort.length - 3) * WIDTH;
                    if (wrap.scrollLeft < maxScrollLeft) {
                        animation(0, 32, 8);
                    }
                } else {
                    // 向上滚
                    if (wrap.scrollLeft > 0) {
                        animation(0, -32, 8);
                    }
                }
            },
            100,
            true
        ),
        sidebarItemClickHandler(e) {
            if (!e.target.dataset.noAction && e.target.nodeName === 'A') {
                this.$emit("close", this.index);
            }
        },
        computeRouteGroup() {
            // 每个连接的高度时32px
            const validHeight = Math.max(document.body.clientHeight - 200, 300);
            //
            if (this.route.children) {
                //
                let height = 0;
                const newRoutesSort = [[]];

                for (const item of this.route.children) {
                    //
                    if (item.children) {
                        let subHeights = 64;
                        //
                        if (item.children.length) {
                            new Set(item.children).forEach(() => {
                                subHeights += 32;
                            });
                        }
                        height += subHeights;
                        //
                        if (validHeight - height < 0) {
                            //
                            newRoutesSort.push([item]);
                            subHeights = 0;
                        } else {
                            newRoutesSort[newRoutesSort.length - 1].push(item);
                        }
                    } else {
                        height += 32;
                        //
                        if (validHeight - height < 0) {
                            newRoutesSort.push([item]);
                            height = 0;
                        } else {
                            newRoutesSort[newRoutesSort.length - 1].push(item);
                        }
                    }
                }
                this.newRoutesSort = newRoutesSort.filter((i) => i.length);
            }
        }
    }
};
</script>
