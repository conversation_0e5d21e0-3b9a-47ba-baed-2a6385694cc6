<!--
    ====================
    菜单 组件
    ====================
    TODO: 组件有待优化；
    功能：    对当前的登录的用户有权限的菜单进行展示，并实现点击切换页面的功能，实现二级菜单过长的自动换列；
    实现原因：当前用户对业务功能的需要和操作的便利；
    实现方式： 1. 通过vuex中的permission_routes进行数据数据的获取，通过对数据的循环展示渲染菜单栏
              2. 将也二级和三级菜单展示出来然后通过对当前页面的高度的计算和对二级菜单和三级菜单的累加进行
                  比较实现根据长度动态换列
              3. 鼠标滑过一级菜单时一级菜单展开滑过以及菜单按钮时代开二级菜单，点击二级菜单进行路由的跳转，
                  且要求二级菜单数据过长需要动态换列

 -->
/* eslint-disable */
<template>
    <div :class="sidebarClassName" @mouseenter="inSideBar = true" @mouseleave="handleMouseLeaveSideBar">
        <div class="picc-sidebar-logo">
            <svg-icon :icon-class="spreadIcon" :style="isSpread ? 'width:112px;height:34px' : 'width:34px;height:34px'" />
            <span>{{ title }}</span>
        </div>
        <el-scrollbar>
            <div ref="wrapper" class="picc-sidebar-item-wrapper picc-sidebar-custom-active">
                <template v-for="(item, index) in routes">
                    <sidebar-item
                        v-if="!item.hidden"
                        :key="index"
                        :hidden="item.hidden"
                        :path="item.path"
                        :meta="item.meta"
                        :route="item"
                        :index="index"
                        :next-index="prepareShowIndex"
                        :sidebar-sub-items-show="showSideBarSubItemsIndex === index"
                        @enter="handleSidebarItemEnter"
                        @leave="handleSidebarItemLeave"
                        @close="handleSidebarItemClose"
                    />
                </template>
            </div>
        </el-scrollbar>
        <div v-if="manualSideBar" class="sidebar-handler" :class="{ 'is-spread': isSpread }" @click="toggleSideBar">
            <svg-icon :icon-class="manual_sideBarSpread ? 'menu-close' : 'menu-open'" />
        </div>
    </div>
</template>
<script>
import SidebarItem from "./SidebarItem.vue";
import { addClass, debounce, removeClass } from "@/utils";
import { mapGetters } from "vuex";

export default {
    name: "Sidebar",
    components: {
        SidebarItem
    },
    props: {
        title: {
            type: String,
            default: ""
        },
        isSpread: {
            type: Boolean,
            default: false
        },
        routes: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            // 标记鼠标是否在菜单栏内
            inSideBar: false,
            // 准备切换的菜单的序号
            prepareShowIndex: -1,
            // 菜单切换计时器
            timer: null,
            // 当前显示子菜单的序号
            showSideBarSubItemsIndex: -1
        };
    },
    computed: {
        ...mapGetters(["sideBarSpread", "manualSideBar", "manual_sideBarSpread", "manual_mouseInSideBar"]),
        sidebarClassName() {
            if (this.isSpread) {
                return "picc-sidebar";
            } else {
                return "picc-sidebar is-shrink";
            }
        },
        spreadIcon() {
            if (!this.isSpread) {
                return "onlyword";
            } else {
                return "havePicc";
            }
        }
    },
    watch: {
        showSideBarSubItemsIndex: {
            immediate: true,
            handler(val) {
                if (val === -1) {
                    this.resetActive();
                }
            }
        },
        $route() {
            this.resetActive();
        },
        inSideBar(val) {
            this.$store.dispatch("app/setMouseInSideBar", val);
            if (this.manualSideBar) {
                this.$store.dispatch("app/setManualSideBarStatus", {
                    sideBarSpread: this.manual_sideBarSpread,
                    mouseInSideBar: val
                });
            }
        }
    },
    methods: {
        toggleSideBar() {
            this.$store.dispatch("app/setManualSideBarStatus", {
                sideBarSpread: !this.manual_sideBarSpread,
                mouseInSideBar: !this.manual_sideBarSpread
            });
        },
        // 鼠标移入一级菜单，设置定时器，定时器执行时，移除通过dom操作添加的active，设置需要显示的菜单序号，展示菜单
        // （定时器执行后展示，根据体验情况调整，时间短了，斜着进入菜单时，可能会切到别的一级菜单，因为鼠标经过了别的一级菜单）
        handleSidebarItemEnter(index) {
            this.prepareShowIndex = index;
            this.resetTimer();
            this.timer = setTimeout(() => {
                const dom = this.$refs.wrapper.querySelector(".picc-sidebar-item.picc-sidebar-item-active.__auto__");
                if (dom) {
                    removeClass(dom, "picc-sidebar-item-active __auto__");
                }
                this.showSideBarSubItemsIndex = this.prepareShowIndex;
                this.prepareShowIndex = -1;
            }, 200);
        },
        // 鼠标离开一级菜单时，如果鼠标还在菜单范围，则不关闭当前显示的菜单
        handleSidebarItemLeave(index) {
            // 经过其他菜单移动到没有菜单的地方，经过的最后一个菜单
            if (this.prepareShowIndex === index) {
                this.resetTimer();
                this.prepareShowIndex = -1;
            }
            if (!this.inSideBar && this.prepareShowIndex === -1) {
                this.showSideBarSubItemsIndex = -1;
            }
        },
        // 选中某个菜单时，或者移除子菜单范围，关闭子菜单
        handleSidebarItemClose(index) {
            this.resetTimer();
            this.showSideBarSubItemsIndex = -1;
        },
        // 鼠标移开菜单栏
        handleMouseLeaveSideBar() {
            // 如果有计时器，先清掉
            this.resetTimer();
            this.inSideBar = false;
            this.showSideBarSubItemsIndex = -1;
        },
        resetTimer() {
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }
        },
        // 自动添加当前页面菜单的高亮状态
        resetActive() {
            this.$nextTick(() => {
                const autoActiveDom = this.$refs.wrapper.querySelector(".picc-sidebar-item.picc-sidebar-item-active.__auto__");
                if (autoActiveDom) {
                    removeClass(autoActiveDom, "picc-sidebar-item-active __auto__");
                }
                const activeRouteDom = this.$refs.wrapper.querySelector(".picc-sidebar-item > .router-link-active");
                if (activeRouteDom) {
                    addClass(activeRouteDom.parentNode, "picc-sidebar-item-active __auto__");
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.picc-sidebar {
    height: 100%;
}

.el-scrollbar {
    max-height: calc(100% - 200px);
    height: 100%;

    ::v-deep {
        .el-scrollbar__view {
            width: 100%;
        }
    }
}

.picc-sidebar-item {
    transition: all 0.2s;
}

.sidebar-handler {
    cursor: pointer;
    position: absolute;
    bottom: 24px;
    right: calc(50% - 9px);
    font-size: 18px;

    &.is-spread {
        right: 24px;
    }
}
</style>
