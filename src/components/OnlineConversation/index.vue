<template>
    <div class="conversation">
        <div class="conversation-container">
            <div class="conversation-message-list">
                <template v-for="(item, index) in listData">
                    <conversation-item :key="index" :data="item" />
                </template>
            </div>
            <div class="conversation-input">
                <div class="conversation-input-container">
                    <el-input
                        v-model="textArea" type="textarea"
                        class="picc-textarea-puretext conversation-input-container-inputarea"
                        resize="none" :autosize="{ minRows: 1, maxRows: 7 }" placeholder="在此输入你的回答"
                    />
                    <!-- <textarea class="conversation-input-container-inputarea" type="text" placeholder="在此输入你的回答"></textarea> -->
                    <button class="conversation-input-container-btn">
                        发送
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import conversationItem from "./conversationItem";
export default {
    name: "Conversation",
    components: {
        conversationItem
    },
    data() {
        return {
            listData: [
                {
                    persionType: "me",
                    imageUrl: "./././././",
                    publishDate: "2020-10-24 10:30:25",
                    message:
                        "啊手动阀手动test test test test test test test test test阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀"
                },
                {
                    persionType: "other",
                    imageUrl: "./././././",
                    publishDate: "2020-10-23 10:30:25",
                    message:
                        "啊手动阀手动test test test test test test test test test阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀"
                },
                {
                    persionType: "other",
                    imageUrl: "./././././",
                    publishDate: "2020-10-22 10:30:25",
                    message:
                        "啊手动阀手动test test test test test test test test test阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀"
                },
                {
                    persionType: "other",
                    imageUrl: "./././././",
                    publishDate: "2020-10-21 10:30:25",
                    message:
                        "啊手动阀手动test test test test test test test test test阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀"
                },
                {
                    persionType: "other",
                    imageUrl: "./././././",
                    publishDate: "2020-10-21 10:30:25",
                    message:
                        "啊手动阀手动test test test test test test test test test阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀"
                }
            ],
            textArea:""
        };
    },
    computed: {}
};
</script>

<style lang="scss" scoped>
.conversation {
    width: 100%;
    // height: 536px;
}

.conversation-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.conversation-message-list {
    display: flex;
    flex-direction: column-reverse;
    height: 100%;
    overflow-y: auto;
    margin-bottom: 172px;
}

.conversation-input {
    position: fixed;
    bottom: 0;
    width: 360px;
    flex-grow: 0;
    max-height: 172px;
    min-height: 52px;
    background-color: white;
    border-top: 0.5px solid #e2e2e2;
}

.conversation-input-container {
    display: flex;
    flex-direction: row;
    margin: 12px 20px;
    align-items: flex-end;
}

.conversation-input-container-inputarea {
    width: 260px;
    min-height: 20px;
    max-height: 140px;
    margin-bottom: 4px;
    // overflow-y: scroll;
    // overflow-x: hidden;
}
::v-deep .conversation-input-container-inputarea .el-textarea__inner {
    border: 0;
    overflow: auto;
    padding: 0 4px 0 0;
}

.conversation-input-container-btn {
    width: 50px;
    height: 28px;
    line-height: 28px;
    border: none;
    background: #99acf0;
    border-radius: 14px;
    padding: 0 10px;
    font-size: 12px;
    color: #ffffff;
    margin-left: 10px;
    cursor: pointer;

    &:hover {
        background: #4a6cf7;
    }
}
</style>
