<template>
    <div>
        <div v-if="data.persionType === 'other'" class="conversation-item">
            <img class="conversation-item-idlogo" :src="data.imageUrl" alt="" width="24px" height="24px" />
            <div class="conversation-item-message">
                <div class="conversation-item-message-datecontainer">
                    <span class="conversation-item-message-datecontainer-date">{{ data.publishDate | dealDateToChinese }}</span>
                </div>
                <div class="conversation-item-message-information">
                    {{ data.message }}
                </div>
            </div>
        </div>
        <div v-if="data.persionType === 'me'" class="conversation-item-reverse">
            <img class="conversation-item-idlogo-reverse" :src="data.imageUrl" alt="" width="24px" height="24px" />
            <div class="conversation-item-message">
                <div class="conversation-item-message-datecontainer-reverse">
                    <span class="conversation-item-message-datecontainer-date">{{ data.publishDate | dealDateToChinese }}</span>
                </div>
                <div class="conversation-item-message-information">
                    {{ data.message }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "ConversationItem",
    filters: {
        dealDateToChinese(dateStr) {
            var dateArr = dateStr.split("-");
            var year = parseInt(dateArr[0]);
            var month = dateArr[1].indexOf("0") === 0 ? parseInt(dateArr[1].substring(1)) : parseInt(dateArr[1]); // 处理月份为04这样的情况
            var day = parseInt(dateArr[2]);
            var time = dateStr.split(" ")[1];

            const date = new Date();
            const nowYear = date.getFullYear();
            const nowMonth = date.getMonth();
            const nowDay = date.getDate();
            if (nowYear === parseInt(year) && nowMonth === parseInt(month) && nowDay === parseInt(day)) {
                return "今天 " + time;
            }
            if (nowYear === parseInt(year) && nowMonth === parseInt(month) && nowDay - parseInt(day) === 1) {
                return "昨天 " + time;
            }
            if (nowYear === parseInt(year) && nowMonth === parseInt(month) && nowDay - parseInt(day) === 2) {
                return "前天 " + time;
            }
            return dateStr;
        }
    },
    props: {
        data: {
            type: Object,
            default: () => {
                return {
                    imageUrl: "./././././",
                    publishDate: "2020-02-20 10.30.25",
                    message:
                        "啊手动阀手动test test test test test test test test test阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀啊手动阀手动阀手动阀"
                };
            }
        }
    },
    data() {
        return {
            a: "111111"
        };
    },
    computed: {}
};
</script>

<style lang="scss" scoped>
.conversation-item {
    display: flex;
    flex-direction: row;
    margin: 16px 20px 0 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #dddddd;
}
.conversation-item-idlogo {
    margin: 0 8px 0 0;
}
.conversation-item-idlogo-reverse {
    margin: 0 0 0 8px;
}
.conversation-item-message {
    height: auto;
    width: 100%;
    margin: 0 0 0 0;
}
.conversation-item-message-datecontainer {
    display: flex;
    flex-direction: row;
}
.conversation-item-message-datecontainer-date {
    font-size: 12px;
    color: #afafb4;
}
.conversation-item-message-information {
    margin: 12px 0 0 0;
    white-space: normal;
    width: 100%;
}
.conversation-item-reverse {
    display: flex;
    flex-direction: row-reverse;
    margin: 16px 20px 0 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #dddddd;
}
.conversation-item-message-datecontainer-reverse {
    display: flex;
    flex-direction: row-reverse;
}
</style>
