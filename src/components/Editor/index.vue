
<template>
    <div>
        <!-- Two-way Data-Binding -->
        <quill-editor
            ref="myQuillEditor"
            v-model="contentData"
            :options="editorOption"
            @blur="onEditorBlur($event)"
            @focus="onEditorFocus($event)"
            @ready="onEditorReady($event)"
            @change="onEditorChange($event)"
        />
    </div>
</template>

<script>
// You can also register Quill modules in the component
// import Quill from 'quill';
// import someModule from '../yourModulePath/someQuillModule.js';
// Quill.register('modules/someModule', someModule);

export default {
    props: {
        content: {
            type: String,
            default: ''
        },
        editorOption: {
            type: Object,
            default: () => {
                return {
                // Some Quill options...
                    modules: {
                        toolbar: [
                            ['bold', 'italic', 'underline', 'strike'], // toggled buttons
                            ['blockquote', 'code-block'],

                            [{ header: 1 }, { header: 2 }], // custom button values
                            [{ list: 'ordered' }, { list: 'bullet' }],
                            [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
                            [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
                            [{ direction: 'rtl' }], // text direction

                            [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
                            [{ header: [1, 2, 3, 4, 5, 6, false] }],

                            [{ color: [] }, { background: [] }], // dropdown with defaults from theme
                            [{ font: [] }],
                            [{ align: [] }],

                            ['clean']
                        ]
                    }
                };
            }
        }
    },
    data () {
        return {
            contentData: this.content
            // content: '<h2>I am Example</h2>',
            // editorOption: {
            //     // Some Quill options...
            //     modules: {
            //         toolbar: [
            //             ['bold', 'italic', 'underline', 'strike'], // toggled buttons
            //             ['blockquote', 'code-block'],

            //             [{ header: 1 }, { header: 2 }], // custom button values
            //             [{ list: 'ordered' }, { list: 'bullet' }],
            //             [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
            //             [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
            //             [{ direction: 'rtl' }], // text direction

            //             [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
            //             [{ header: [1, 2, 3, 4, 5, 6, false] }],

            //             [{ color: [] }, { background: [] }], // dropdown with defaults from theme
            //             [{ font: [] }],
            //             [{ align: [] }],

            //             ['clean']
            //         ]
            //     }
            // }
        };
    },
    computed: {
        editor() {
            return this.$refs.myQuillEditor.quill;
        }
    },
    mounted() {
        // console.log('this is current quill instance object', this.editor);
    },
    methods: {
        onEditorBlur(quill) {
            // console.log('editor blur!', quill);
        },
        onEditorFocus(quill) {
            // console.log('editor focus!', quill);
        },
        onEditorReady(quill) {
            // console.log('editor ready!', quill);
        },
        onEditorChange({ quill, html, text }) {
            // console.log('editor change!', quill, html, text);
            this.contentData = html;
        }
    }
};
</script>
