<template>
    <div class="dialog-container">
        <el-dialog
            v-if="sonRefresh"
            :show-close="false"
            title="提示"
            :visible.sync="getIsVisible"
            width="40%"
            custom-class="dialog-custom"
            @close="closeDialog"
        >
            <div slot="title" class="dialog-title-slot">
                <i class="picc-icon picc-icon-error dialog-title-img"></i>
                <span style="color: #292b34">{{ promptTitle }}</span>
            </div>
            <span class="errcode" style="color: #292b34">{{ promptContent }}</span>
            <p style="color: #292b34">
                {{ promptCode }}
            </p>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="closeDialog">我知道了</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "NewSubmitfailed",
    props: {
        dialogShow: {
            type: Boolean,
            default: false
        },
        promptTitle: {
            type: String,
            default: "提交失败"
        },
        promptCode: {
            type: String,
            default: "错误代码：02T00：00：00.000+8:00"
        },
        promptContent: {
            type: String,
            default: "很抱歉由于数据传输中断导致了此次提交失败，请稍后重试"
        }
    },
    data() {
        return {
            getIsVisible: this.dialogShow,
            sonRefresh: true
        };
    },
    watch: {
        dialogShow(val) {
            this.getIsVisible = val;
        }
    },
    methods: {
        closeDialog() {
            this.$emit("dialogShowChange", false);
        }
    }
};
</script>

<style lang="scss" scoped>
.dialog-container {
    ::v-deep .el-dialog__wrapper {
        color: #292b34 !important;
        .el-dialog {
            max-width: 643px;
            width: 50% !important;
            max-height: 374px;
            height: 374px;
            .el-dialog__header {
                padding: 62px 24px 0px 24px;
                font-size: 16px;
                font-weight: bold;

                box-shadow: inset 0 0px 0 0 #e2e2e2;
                .dialog-title-slot {
                    text-align: center;
                    .dialog-title-img {
                        margin: 0 auto;
                        text-align: center;
                        display: block;
                        height: 55px;
                    }
                    > span {
                        display: block;
                        width: 100%;
                        margin-top: 24px;
                    }
                }
            }

            .el-dialog__body {
                padding: 48px 0 0 0;
                font-family: microsoft yahei;
                line-height: 16px;
                font-size: 14px;
                text-align: center;
            }
            p {
                margin: 16px 0 0 0;
            }
            .el-dialog__footer {
                width: 100%;
                padding: 44px 0 44px 0;
                text-align: center;
                .dialog-footer {
                    font-family: microsoft yahei;
                    .el-button {
                        padding: 0;
                        line-height: 16px;
                        border-radius: 20px;
                        margin-bottom: 0;
                        font-size: 14px;
                    }

                    .el-button--primary {
                        width: 96px;
                        background-color: #3556eb;
                    }
                }
            }
        }
    }
}
</style>
