import Vue from 'vue';
/**
 * 时间选择器 - 多种手动输入格式转化为时间组件的value-format的格式
 * 解释：
 * 使用自定义指令获取当前节点的全部信息，同时可以拿到绑定的对象信息，
 * 将该页面的this传入对v-model绑定的值进行赋值即可触发时间选择器的展
 * 示格式化功能将输入的时间格式化成对应的时间。
 */
Vue.directive('testDateFormat', {
    inserted: function (el, binding, vnode) {
        const { value: _obj } = binding;
        const { context: that, data } = vnode;
        const { expression: key } = data.model;
        if (that && that._isVue) {
            const $this = el.getElementsByTagName('input')[0];
            $this.addEventListener('change', () => {
                let value = $this.value;
                // 中文日期
                if (!value.match(/\d{1,}/g)) {
                    const chinaNum = ['〇', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
                    const wList = [...value].map(w => {
                        const idx = chinaNum.findIndex(p => p == w);
                        return idx == -1 ? w : idx;
                    });
                    value = wList.join('');
                }
                value = value.replace(/^(\d{4})\D*(\d{1,2})\D*(\d{1,2})\D*/, '$1-$2-$3'); // 格式化输入格式
                const time = value && value.constructor == String ? new Date(value) : value;  // 转换时间格式
                let keys = key.split('.');//将传入的v-model对象以点为分隔符拆成数组便于后面赋值
                // 自定义赋值
                if (_obj) {
                    const { keys: keyList } = _obj;
                    keys = keyList;
                }
                //给v-model绑定的值进行赋值前面已经将对象用点进行分割成数组，这里循环对象进行赋值
                let targetVModel = that;
                for (let i = 0; i < keys.length - 1; i++) {
                    targetVModel = targetVModel[keys[i]];
                }
                targetVModel[keys[keys.length - 1]] = dealDateToValueFormat(time, vnode.componentOptions.propsData.valueFormat);
            });
        }
    }
});
//根据value-format的格式处理输入的时间格式并返回
function dealDateToValueFormat(timeVal, format) {
    let year = timeVal.getFullYear();
    let month = ((timeVal.getMonth() + 1) < 10 ? "0" : "") + (timeVal.getMonth() + 1);
    let day = (timeVal.getDate() < 10 ? "0" : "") + timeVal.getDate();
    if (format != undefined && format.indexOf("-") != -1) {
        return year + "-" + month + "-" + day;
    } else if (format != undefined && format.indexOf("/") != -1) {
        return year + "/" + month + "/" + day;
    } else if (format != undefined && format.indexOf("/") == -1 && format.indexOf("/") == -1) {
        return year + month + day;
    } else {
        return year + "-" + month + "-" + day;
    }
}