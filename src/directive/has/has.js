import store from '@/store';

export default {
    bind: function(el, binding, vnode) {
    // 获取按钮权限
        const roles = vnode.context.$route.meta.roles;
        const roleslist = store.getters && store.getters.roles;
        const has = roles.includes(roleslist);
        if (!has) {
            vnode.context.$nextTick(() => {
                el.parentNode && el.parentNode.removeChild(el);
            });
        }
    }
};
