export default {
    bind: function(el, binding, vnode) {
    // 获取按钮权限
        console.log("bind",'.......................',binding);
        imageRequest(binding.value,el);
    },
    // inserted: function(el, binding, vnode) {
    //     // 获取按钮权限
    //     console.log("inserted",'.......................',binding);
    // },
    // update: function(el, binding, vnode) {
    //     // 获取按钮权限
    //     console.log("update");
    // },
    // componentUpdate: function(el, binding, vnode) {
    //     // 获取按钮权限
    //     console.log("componentUpdate");
    // },
    // unbind: function(el, binding, vnode) {
    //     // 获取按钮权限
    //     console.log("unbind");
    // },
};
/**
     * 图片资源请求方法，以blob形式将返回值赋值给img标签的src属性
     * @param {*} url the image url 
     * @param {*} ele each dom element
     */
function imageRequest(url, ele) {
    const xhr = new XMLHttpRequest();
    xhr.open('get', url, true);
    // if (this.headers.length) {
    //     this.headers.forEach(item => {
    //         console.log(item);
    //         xhr.setRequestHeader(`${item.header}`, item.content);
    //     });
    // }
    xhr.responseType = 'blob';
    xhr.onreadystatechange = function(e) {
        if (xhr.status === 200 && xhr.readyState === XMLHttpRequest.DONE) {
            ele.src = URL.createObjectURL(xhr.response);
            ele.onload = function() {
                URL.revokeObjectURL(ele.src);
            };
        }
    };
    xhr.send();
}
