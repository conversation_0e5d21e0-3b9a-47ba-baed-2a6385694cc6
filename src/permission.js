// import router from "./router";
import store from "./store";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import getPageTitle from "@/utils/get-page-title";
import settings from "@/settings";
export default function routerPermission(router) {

    NProgress.configure({ showSpinner: false }); // NProgress Configuration
    const whiteList = ["/login", "/auth-redirect", "/retrievePassword", "/register", "/404"]; // no redirect whitelist

    let isLogin = false;

    router.beforeEach(async (to, from, next) => {
        // xss处理路由中的参数---------------------------start
        /* 跳转参数判断，url包含可以脚本时拒绝跳转 */
        const illegal = [
            /<(no)?script[^>]*>.*?<\/(no)?script>/gim,
            /eval\((.*?)\)/gim,
            /expression\((.*?)\)/gim,
            /(javascript:|vbscript:|view-source:)+/gim,
            /<("[^"]*"|'[^']*'|[^'">])*>/gim,
            /(window\.location|window\.|\.location|document\.cookie|document\.|alert\(.*?\)|window\.open\()+/gim,
            /<+\s*\w*\s*(oncontrolselect|oncopy|oncut|ondataavailable|ondatasetchanged|ondatasetcomplete|ondblclick|ondeactivate|ondrag|ondragend|ondragenter|ondragleave|ondragover|ondragstart|ondrop|οnerrοr=|onerroupdate|onfilterchange|onfinish|onfocus|onfocusin|onfocusout|onhelp|onkeydown|onkeypress|onkeyup|onlayoutcomplete|onload|onlosecapture|onmousedown|onmouseenter|onmouseleave|onmousemove|onmousout|onmouseover|onmouseup|onmousewheel|onmove|onmoveend|onmovestart|onabort|onactivate|onafterprint|onafterupdate|onbefore|onbeforeactivate|onbeforecopy|onbeforecut|onbeforedeactivate|onbeforeeditocus|onbeforepaste|onbeforeprint|onbeforeunload|onbeforeupdate|onblur|onbounce|oncellchange|onchange|onclick|oncontextmenu|onpaste|onpropertychange|onreadystatechange|onreset|onresize|onresizend|onresizestart|onrowenter|onrowexit|onrowsdelete|onrowsinserted|onscroll|onselect|onselectionchange|onselectstart|onstart|onstop|onsubmit|onunload)+\s*=+/gim
        ];
        const query = Object.keys(to.query);
        let strQuery = '';
        for (let key of query) {
            strQuery += to.query[key];
            const param = to.query[key];
            console.log(typeof (to.query));
            for (let reg of illegal) {
                if (reg.test(param.toLowerCase())) {
                    alert('请求参数不合法！');
                    next({ path: to.path, query: {} });
                    return;
                }
            }
        }
        //没有传对象直接传一个字符串
        for (let reg of illegal) {
            if (reg.test(strQuery)) {
                alert('请求参数不合法！');
                next({ path: to.path, query: {} });
                return;
            }
        }
        // xss处理路由中的参数---------------------------end
        // 为了实现宽屏时的两侧六百时的留白处颜色变化
        document.getElementsByTagName("body")[0].style.backgroundColor = store.state.app.outThemeColor;
        /**
         * 之前是获取的to.meta.title, 因为meta.title是渲染菜单项的名称，无法使用英文，
         * 所以暂时用name替换
         */
        document.title = getPageTitle(to.name);
        NProgress.start();
        if (isLogin) {
            await store.dispatch("app/setCurrentPath", to.path);
            next();
            NProgress.done();
        } else {
            // TODO: 可以随机按照不同存在vuex中的数据来验证登录
            const userInfo = await store.dispatch("user/getUserInfo");
            /**
             * @description condition of first landing the system: userInfo shoulde be invalid;
             *              condition of refresh the page after login: userInfo should be valid;
             *              condition of after login: userInfo should be set in vuex;
             */
            if (userInfo) {
                const accessRoutes = await store.dispatch("permission/generateRoutes");
                console.log(accessRoutes, ".........................");
                router.addRoutes([...accessRoutes]);
                // assign {isLogin} as true for duplicate routes
                isLogin = true;
                next({ ...to, replace: true });
                //
                if (to.path === "/login") {
                    next("/");
                } else {
                    next({ ...to, replace: true });
                }
                //
                NProgress.done();
                //
            } else {
                /**
                 * settings.loginActive
                 * @type {Boolean} true | false
                 */
                if (settings.loginActive) {
                    if (whiteList.indexOf(to.path) !== -1) {
                        next();
                    } else {
                        next(`/login?redirect=${to.path}`);
                    }
                    NProgress.done();
                } else {
                    //
                    isLogin = true;
                    // TODO: 如果免登陆状态下，非法进入页面跳转到的页面，可以按照要求改
                    next("/dashboard");
                    NProgress.done();
                }
            }
        }
    });

}
