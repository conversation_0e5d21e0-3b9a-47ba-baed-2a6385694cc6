import Layout from "@/layout";
import SubMenuContainer from "@/layout/components/SubMenuContainer.vue";
// import { componentUrlMap, pathMap, nameMap, iconsMap } from '@/router/routerMap'
const keywordRoutes = "epaySystemManagerNewRoutes";
export const setSessionRoutes = (routesObj) => {
    sessionStorage.setItem(keywordRoutes, JSON.stringify(routesObj || []));
};

export const getSessionRoutes = () => {
    return JSON.parse(sessionStorage.getItem(keywordRoutes));
};

export const dealRouter = (josnObj) => {
    var str = [];
    for (var i = 0; i < josnObj.length; i++) {
        var obj = {};
        const urlName = josnObj[i].routePath;
        obj.path = urlName;

        const componentUrl = josnObj[i].filePath;

        if (componentUrl === "/layout") {
            obj.component = Layout;
        } else if(componentUrl === "/SubMenuContainer"){
            obj.component = SubMenuContainer;
        }else {
            if(componentUrl){
                let str ="@/views" + componentUrl;
                // const componentReal = () => import("@/views" + componentUrl);
                const componentReal = "@/views" + componentUrl;
                obj.component = componentReal;
            }
        }

        obj.name = josnObj[i].name;
        obj.meta = {};
        obj.meta.bgClass = "default" && josnObj[i].bg;
        obj.meta.title = josnObj[i].menuName;
        obj.meta.icon = josnObj[i].image;
        obj.hidden = josnObj[i].hidden;
        obj.alwaysShow = josnObj[i].alwaysShow;
        if (josnObj[i].childMenu && josnObj[i].childMenu.length > 0) {
            if(obj.alwaysShow == null) obj.alwaysShow = josnObj[i].childMenu.length > 1;
            obj.children = dealRouter(josnObj[i].childMenu);
        } else if (josnObj[i].childMenus && josnObj[i].childMenus.length > 0) {
            if(obj.alwaysShow == null) obj.alwaysShow = josnObj[i].childMenus.length > 1;
            obj.children = dealRouter(josnObj[i].childMenus);
        }
        str.push(obj);
    }
    return str;
};

export const dealData = (data) => {
    const sortData = sortDatasByMenulevel(data, 0);
    const realData = rebuildData(sortData);
    ascArray(realData);
    return realData[0].children;
};
// 根据menulevel对数据进行分组
function sortDatasByMenulevel(data) {
    const sortData = [];
    for (let i = 0; i < data.length; i++) {
        if (!sortData[data[i].menuLevel - 1]) {
            sortData[data[i].menuLevel - 1] = [];
        }
        const obj = {};
        obj.id = data[i].id;
        obj.upperId = data[i].upperId;
        obj.component = "";
        obj.name = data[i].taskCode;
        obj.path = data[i].actionURL;
        obj.svrId = data[i].svrId;
        obj.displayNo = data[i].displayNo;
        obj.menuType = data[i].menuType;

        if (data[i].menuType === "2") {
            obj.hidden = true;
        } else {
            obj.hidden = false;
        }
        obj.menuLevel = data[i].menuLevel;
        obj.meta = {};
        obj.meta.title = data[i].menuCName;
        obj.meta.icon = data[i].imagePath;
        obj.meta.affix = true;
        if (data[i].displayNo !== 0 || data[i].menuLevel !== 4) {
            sortData[data[i].menuLevel - 1].push(obj);
        }
    }
    return sortData;
}
// 根据分好组的数据倒序遍历并构建父子关系
function rebuildData(data) {
    for (let i = data.length - 1; i > 0; i--) {
        for (let j = 0; j < data[i].length; j++) {
            if (i >= 1) {
                for (let a = 0; a < data[i - 1].length; a++) {
                    if (data[i][j].upperId === data[i - 1][a].id) {
                        if (!data[i - 1][a].children) {
                            data[i - 1][a].children = [];
                        }
                        data[i - 1][a].children.push(data[i][j]);
                    }
                }
            }
        }
    }
    return data[0];
}
// 同级数组数据排序需要使用的方法
function compare(obj1, obj2) {
    var val1 = obj1.displayNo;
    var val2 = obj2.displayNo;
    if (val1 < val2) {
        return -1;
    } else if (val1 > val2) {
        return 1;
    } else {
        return 0;
    }
}
// 对存在父子关系的数据的同级数据根据displayNo进行排序
function ascArray(arr) {
    arr.sort(compare);
    for (let i = 0; i < arr.length; i++) {
        if (arr[i].children && arr[i].children.length !== 0) {
            ascArray(arr[i].children);
        }
    }
}

