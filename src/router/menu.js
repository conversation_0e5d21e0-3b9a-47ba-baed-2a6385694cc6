const menuData = {
    status: 0,
    statusText: "Success",
    data: {
        YHZX: [
            {
                id: 339,
                upperId: 0,
                svrId: 1,
                menuCName: "用户中心",
                taskCode: "YHZX",
                menuType: "0",
                menuLevel: 1,
                menuTName: null,
                menuEName: null,
                actionURL: null,
                displayNo: 97,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 340,
                upperId: 339,
                svrId: 1,
                menuCName: "用户管理",
                taskCode: "users",
                menuType: "1",
                menuLevel: 2,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/users",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 343,
                upperId: 339,
                svrId: 1,
                menuCName: "资源管理",
                taskCode: "ziyuan",
                menuType: "1",
                menuLevel: 2,
                menuTName: null,
                menuEName: null,
                actionURL: "/menu",
                displayNo: 2,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000001560,
                upperId: 339,
                svrId: 1,
                menuCName: "用户分类管理",
                taskCode: "handlerManage",
                menuType: "1",
                menuLevel: 2,
                menuTName: "",
                menuEName: "handlerManage",
                actionURL: "/handlerManage",
                displayNo: 2,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 347,
                upperId: 339,
                svrId: 1,
                menuCName: "规则管理",
                taskCode: "menutx",
                menuType: "1",
                menuLevel: 2,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/menutx",
                displayNo: 9,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 623,
                upperId: 339,
                svrId: 1,
                menuCName: "批量导入",
                taskCode: "batchinsert",
                menuType: "1",
                menuLevel: 2,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/pldr",
                displayNo: 11,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 348,
                upperId: 339,
                svrId: 1,
                menuCName: "授权管理",
                taskCode: "auth",
                menuType: "1",
                menuLevel: 2,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/authorization",
                displayNo: 13,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 698,
                upperId: 339,
                svrId: 1,
                menuCName: "日志管理",
                taskCode: "userlog",
                menuType: "1",
                menuLevel: 2,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/userlog",
                displayNo: 18,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 341,
                upperId: 340,
                svrId: 1,
                menuCName: "用户信息管理",
                taskCode: "userinfo",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/userList",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 344,
                upperId: 343,
                svrId: 1,
                menuCName: "应用管理",
                taskCode: "utiisvr",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/systemService",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 624,
                upperId: 623,
                svrId: 1,
                menuCName: "菜单批量导入",
                taskCode: "menubatchinsert",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/menudr",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000041,
                upperId: 698,
                svrId: 1,
                menuCName: "登录日志管理",
                taskCode: "log",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/log",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000044,
                upperId: 348,
                svrId: 1,
                menuCName: "用户批量授权",
                taskCode: "PLAuthorization",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/PLAuthorization",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000045,
                upperId: 347,
                svrId: 1,
                menuCName: "互斥规则管理",
                taskCode: "RuleManagement",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/RuleManagement",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000286,
                upperId: 698,
                svrId: 1,
                menuCName: "离职离岗日志管理",
                taskCode: "liZhi",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/liZhi",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000001561,
                upperId: 2000000000001560,
                svrId: 1,
                menuCName: "验车人信息维护",
                taskCode: "handlerTrace",
                menuType: "1",
                menuLevel: 3,
                menuTName: "",
                menuEName: "handlerTrace",
                actionURL: "/general/user/handlerTrace",
                displayNo: 1,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 349,
                upperId: 348,
                svrId: 1,
                menuCName: "管理员信息配置",
                taskCode: "manager",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/Administrator",
                displayNo: 2,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 345,
                upperId: 343,
                svrId: 1,
                menuCName: "菜单管理",
                taskCode: "smcmemu",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/menu_1",
                displayNo: 3,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 626,
                upperId: 623,
                svrId: 1,
                menuCName: "用户授权批量导入",
                taskCode: "userauthinsert",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/userdr",
                displayNo: 3,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000046,
                upperId: 347,
                svrId: 1,
                menuCName: "依赖规则管理",
                taskCode: "information2",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/yilai",
                displayNo: 3,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 342,
                upperId: 340,
                svrId: 1,
                menuCName: "用户账户管理",
                taskCode: "useraccount",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/accounts",
                displayNo: 4,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 350,
                upperId: 348,
                svrId: 1,
                menuCName: "用户授权",
                taskCode: "userauth",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/userAuthorization",
                displayNo: 4,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 625,
                upperId: 623,
                svrId: 1,
                menuCName: "角色批量导入",
                taskCode: "gradebatchinsert",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/roledr",
                displayNo: 4,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000042,
                upperId: 698,
                svrId: 1,
                menuCName: "Api请求日志管理",
                taskCode: "apiLog",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/apiLog",
                displayNo: 4,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000319,
                upperId: 343,
                svrId: 1,
                menuCName: "角色定义及配置",
                taskCode: "roleinfo",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/RoleDefinition",
                displayNo: 4,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 628,
                upperId: 348,
                svrId: 1,
                menuCName: "用户绑定送修码",
                taskCode: "userresource",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/repairCode",
                displayNo: 5,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000001562,
                upperId: 2000000000001560,
                svrId: 1,
                menuCName: "验车人信息审核",
                taskCode: "handlerVerify",
                menuType: "1",
                menuLevel: 3,
                menuTName: "",
                menuEName: "handlerVerify",
                actionURL: "/general/user/handlerVerify",
                displayNo: 5,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 2000000000001565,
                upperId: 343,
                svrId: 1,
                menuCName: "标签管理",
                taskCode: "label",
                menuType: "1",
                menuLevel: 3,
                menuTName: "",
                menuEName: "",
                actionURL: "/general/user/label",
                displayNo: 5,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 2000000000001567,
                upperId: 698,
                svrId: 1,
                menuCName: "邮件短信日志管理",
                taskCode: "EMSendLog",
                menuType: "1",
                menuLevel: 3,
                menuTName: "",
                menuEName: "",
                actionURL: "/general/user/EMSendLog",
                displayNo: 5,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 627,
                upperId: 343,
                svrId: 1,
                menuCName: "版本上线计划",
                taskCode: "menutesting",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/versionSet",
                displayNo: 6,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 340,
                svrId: 1,
                menuCName: "财险HR用户查询",
                taskCode: "caixian",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/caixian",
                displayNo: 6,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000048,
                upperId: 340,
                svrId: 1,
                menuCName: "集团HR用户查询",
                taskCode: "jituan",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/jituan",
                displayNo: 6,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000043,
                upperId: 698,
                svrId: 1,
                menuCName: "定时日志管理",
                taskCode: "timeLog",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/settimeLog",
                displayNo: 7,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 340,
                svrId: 1,
                menuCName: "管理员强制下线",
                taskCode: "guanliyuan",
                menuType: "1",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "/general/user/guanliyuan",
                displayNo: 8,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000001563,
                upperId: 2000000000001560,
                svrId: 1,
                menuCName: "短期工号超时统计",
                taskCode: "shortCodeOvertime",
                menuType: "1",
                menuLevel: 3,
                menuTName: "",
                menuEName: "shortCodeOvertime",
                actionURL: "/general/user/shortCodeOvertime",
                displayNo: 9,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 2000000000001566,
                upperId: 343,
                svrId: 1,
                menuCName: "区域管理",
                taskCode: "area",
                menuType: "1",
                menuLevel: 3,
                menuTName: "",
                menuEName: "",
                actionURL: "/general/user/area",
                displayNo: 9,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 2000000000001564,
                upperId: 2000000000001560,
                svrId: 1,
                menuCName: "分类信息维护",
                taskCode: "userClassification",
                menuType: "1",
                menuLevel: 3,
                menuTName: "",
                menuEName: "userClassification",
                actionURL: "/general/user/userClassification",
                displayNo: 15,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 6,
                upperId: 2000000000000319,
                svrId: 1,
                menuCName: "修改",
                taskCode: "roleinfo_update",
                menuType: "2",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "roleinfo_update",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 7,
                upperId: 2000000000000319,
                svrId: 1,
                menuCName: "新增",
                taskCode: "roleinfo_add",
                menuType: "2",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "roleinfo_add",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 8,
                upperId: 2000000000000319,
                svrId: 1,
                menuCName: "启用/注销",
                taskCode: "roleinfo_start_Cancel",
                menuType: "2",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "roleinfo_start_Cancel",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 9,
                upperId: 347,
                svrId: 1,
                menuCName: "新增",
                taskCode: "addMutexRule",
                menuType: "2",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "addMutexRule",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 10,
                upperId: 347,
                svrId: 1,
                menuCName: "修改",
                taskCode: "updateMutexRule",
                menuType: "2",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "updateMutexRule",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 11,
                upperId: 347,
                svrId: 1,
                menuCName: "启用/注销",
                taskCode: "changeMutexRule",
                menuType: "2",
                menuLevel: 3,
                menuTName: null,
                menuEName: null,
                actionURL: "changeMutexRule",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 15,
                upperId: 349,
                svrId: 1,
                menuCName: "授权",
                taskCode: "admin_auth",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "Adminorization",
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 1,
                upperId: 344,
                svrId: 1,
                menuCName: "新增",
                taskCode: "svr_add",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "svr_add",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2,
                upperId: 344,
                svrId: 1,
                menuCName: "修改",
                taskCode: "svr_update",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "svr_update",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 3,
                upperId: 344,
                svrId: 1,
                menuCName: "启用/注销",
                taskCode: "svr_change",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "svr_change",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 4,
                upperId: 345,
                svrId: 1,
                menuCName: "新增",
                taskCode: "addMenu",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "addMenu",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 5,
                upperId: 345,
                svrId: 1,
                menuCName: "修改",
                taskCode: "updateMenu",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "updateMenu",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 12,
                upperId: 349,
                svrId: 1,
                menuCName: "新增",
                taskCode: "userinfo_add_save",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "userinfo_add_save",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 13,
                upperId: 349,
                svrId: 1,
                menuCName: "修改",
                taskCode: "userinfo_update_save",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "userinfo_update_save",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 14,
                upperId: 349,
                svrId: 1,
                menuCName: "启用/注销",
                taskCode: "userinfo_start_Cancel",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "userinfo_start_Cancel",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000288,
                upperId: 341,
                svrId: 1,
                menuCName: "解除绑定",
                taskCode: "user_unbind",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "user_unbind",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000290,
                upperId: 341,
                svrId: 1,
                menuCName: "机构调整",
                taskCode: "comcode_change",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "comcode_change",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 342,
                svrId: 1,
                menuCName: "修改",
                taskCode: "account_update",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "account_update",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: ****************,
                svrId: 1,
                menuCName: "查看详情",
                taskCode: "hr_info_detail",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "hr_info_detail",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 341,
                svrId: 1,
                menuCName: "启用/注销",
                taskCode: "user_change",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "user_change",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000298,
                upperId: 2000000000000048,
                svrId: 1,
                menuCName: "查看详情",
                taskCode: "group_hr_info_detail",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "group_hr_info_detail",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000300,
                upperId: 341,
                svrId: 1,
                menuCName: "查看设备",
                taskCode: "user_device",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "user_device",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000302,
                upperId: 341,
                svrId: 1,
                menuCName: "设备解绑",
                taskCode: "device_unbind",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "device_unbind",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000304,
                upperId: 342,
                svrId: 1,
                menuCName: "修改密码",
                taskCode: "password_reset",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "password_reset",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 342,
                svrId: 1,
                menuCName: "启用/注销",
                taskCode: "account_change",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "account_change",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: ****************,
                svrId: 1,
                menuCName: "下线",
                taskCode: "offline_user",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "offline_user",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 341,
                svrId: 1,
                menuCName: "修改",
                taskCode: "user_update",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "user_update",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 342,
                svrId: 1,
                menuCName: "新增",
                taskCode: "account_add",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "account_add",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 341,
                svrId: 1,
                menuCName: "新增",
                taskCode: "user_add",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "user_add",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: ****************,
                upperId: 341,
                svrId: 1,
                menuCName: "查看照片",
                taskCode: "user_image",
                menuType: "2",
                menuLevel: 4,
                menuTName: null,
                menuEName: null,
                actionURL: "user_image",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000001033,
                upperId: 349,
                svrId: 1,
                menuCName: "查询",
                taskCode: "manager_find",
                menuType: "2",
                menuLevel: 5,
                menuTName: null,
                menuEName: null,
                actionURL: "manager_find",
                displayNo: 0,
                imagePath: null,
                validStatus: "1"
            }
        ],
        TYQT: [
            {
                id: 2000000000000085,
                upperId: 0,
                svrId: 2000000000000017,
                menuCName: "通用前台",
                taskCode: "TYQT",
                menuType: "0",
                menuLevel: 1,
                menuTName: null,
                menuEName: null,
                actionURL: null,
                displayNo: 1,
                imagePath: null,
                validStatus: "1"
            },
            {
                id: 2000000000000086,
                upperId: 2000000000000085,
                svrId: 2000000000000017,
                menuCName: "非车批改",
                taskCode: "prpinspg",
                menuType: "1",
                menuLevel: 2,
                menuTName: "",
                menuEName: "",
                actionURL: "/portalOldPrpinsUrlPrefix/prpins/index.jsp",
                displayNo: 3,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 2000000000000087,
                upperId: 2000000000000085,
                svrId: 2000000000000017,
                menuCName: "车险批改",
                taskCode: "prpallpg",
                menuType: "1",
                menuLevel: 2,
                menuTName: "",
                menuEName: "",
                actionURL: "/portalOldCarUrlPrefix/prpall/index.jsp",
                displayNo: 5,
                imagePath: "",
                validStatus: "1"
            },
            {
                id: 2000000000000088,
                upperId: 2000000000000085,
                svrId: 2000000000000017,
                menuCName: "工作台",
                taskCode: "tyqtDashboard",
                menuType: "1",
                menuLevel: 2,
                menuTName: "",
                menuEName: "",
                actionURL: "/general/dashboard",
                displayNo: 7,
                imagePath: "",
                validStatus: "1"
            }
        ]
    }
};
export default menuData;
