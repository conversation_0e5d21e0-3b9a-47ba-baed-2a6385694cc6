/* Layout */
import Layout from "@/layout";
import SubMenuContainer from "@/layout/components/SubMenuContainer.vue";
let intranetRouter = [
    {
        path: "/demo",
        component: Layout,
        name: "demo",
        meta: {
            title: "示例111",
            icon: "education"
        }, hidden: false, alwaysShow: true,
        children: [{
            path: "labels",
            component: () => import("@/views/demo/labels"),
            name: "Labels",
            meta: {
                bgClass: "grey",
                title: "标签",
                icon: ""
            }, hidden: false, alwaysShow: true
        },
        {
            path: "tabs",
            component: () => import("@/views/demo/tabs"),
            name: "Tabs",
            meta: {
                title: "tab切换",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "anchor",
            component: () => import("@/views/demo/anchor"),
            name: "Anchor",
            meta: {
                title: "锚点定位器",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "bottombar",
            component: () => import("@/views/demo/bottombar"),
            name: "bottombar",
            meta: {
                title: "底部导航栏",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "handentry",
            component: () => import("@/views/demo/handentry"),
            name: "Handentry",
            meta: {
                title: "快捷入口",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "todolist",
            component: () => import("@/views/demo/todolist"),
            name: "Todolist",
            meta: {
                title: "任务列表",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "calendar",
            component: () => import("@/views/demo/calendar"),
            name: "CalendarDemo",
            meta: {
                title: "日历",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "upload",
            component: () => import("@/views/demo/upload"),
            name: "Upload",
            meta: {
                title: "上传",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "list",
            component: () => import("@/views/demo/list"),
            name: "List",
            meta: {
                bgClass: "grey",
                title: "列表",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "titlebar",
            component: () => import("@/views/demo/titlebar"),
            name: "Titlebar",
            meta: {
                title: "通屏标题栏",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "internalcard",
            component: () => import("@/views/demo/internalcard"),
            name: "Internalcard",
            meta: {
                title: "内部卡片标题",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "button",
            component: () => import("@/views/demo/button"),
            name: "Button",
            meta: {
                bgClass: "grey",
                title: "按钮组",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "card",
            component: () => import("@/views/demo/card"),
            name: "CardDemo",
            meta: {
                title: "卡片",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "form",
            component: () => import("@/views/demo/form/index"),
            name: "Form",
            meta: {
                title: "表单",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "message",
            component: () => import("@/views/demo/message"),
            name: "Message",
            meta: {
                title: "系统消息",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "feedback",
            component: () => import("@/views/demo/feedback"),
            name: "Feedback",
            meta: {
                title: "页面操作反馈",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "loading",
            component: () => import("@/views/demo/loading"),
            name: "Loading",
            meta: {
                title: "加载",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "alert",
            component: () => import("@/views/demo/alert"),
            name: "Alert",
            meta: {
                title: "对话框",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "modal",
            component: () => import("@/views/demo/modal"),
            name: "Modal",
            meta: {
                title: "模态框",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "result",
            component: () => import("@/views/demo/result"),
            name: "ResultDemo",
            meta: {
                title: "结果页",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "steps",
            component: () => import("@/views/demo/steps"),
            name: "Steps",
            meta: {
                title: "步骤导航",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "payment",
            component: () => import("@/views/demo/payment"),
            name: "Payment",
            meta: {
                title: "支付",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "table",
            component: () => import("@/views/demo/table"),
            name: "Table",
            meta: {
                title: "表格",
                icon: ""
            },
            hidden: false,
            alwaysShow: true
        }, {
            path: "tooltip",
            component: () => import("@/views/demo/tooltip"),
            name: "Tooltip",
            meta: {
                title: "气泡提示",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "preview",
            component: () => import("@/views/demo/preview"),
            name: "Preview",
            meta: {
                title: "图片预览",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "lang",
            component: () => import("@/views/demo/i18n"),
            name: "Language",
            meta: {
                title: "国际化",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "editor",
            component: () => import("@/views/demo/editor"),
            name: "Editor",
            meta: {
                title: "富文本编辑器",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "searchModel",
            component: () => import("@/views/demo/searchModel"),
            name: "SearchModel",
            meta: {
                title: "搜索弹窗",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "submitfailed",
            component: () => import("@/views/demo/Submitfailed"),
            name: "Submitfailed",
            meta: {
                title: "报错框",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "pagination",
            component: () => import("@/views/demo/pagination"),
            name: "Pagination",
            meta: {
                title: "分页",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "watermark",
            component: () => import("@/views/demo/watermark"),
            name: "watermark",
            meta: {
                title: "水印",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "printTest",
            component: () => import("@/views/demo/print"),
            name: "PrintTest",
            meta: {
                title: "打印测试",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "testUtilsFunction",
            component: () => import("@/views/demo/testUtilsFunction"),
            name: "TestUtilsFunction",
            meta: {
                title: "公共方法测试",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "textarea",
            component: () => import("@/views/demo/textarea"),
            name: "Textarea",
            meta: {
                title: "多行文本输入框",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "pageTemplate",
            component: () => import("@/views/demo/pageTemplate"),
            name: "PageTemplate",
            meta: {
                title: "页面模版",
                icon: ""
            }, hidden: false, alwaysShow: true
        },
        {
            path: "captchaPage",
            component: () => import("@/views/demo/captcha"),
            name: "CaptchaPage",
            meta: {
                title: "登录验证码",
                icon: ""
            }, hidden: false, alwaysShow: true
        },
        {
            path: "WPSOnline",
            component: () => import("@/views/demo/WPSOnline"),
            name: "WPSOnline",
            meta: {
                title: "wps在线文档",
                icon: ""
            }, hidden: false, alwaysShow: true
        }
        ]
    }, {
        path: "/robotDemo",
        component: Layout,
        name: "robetDemo",
        meta: {
            title: "请求示例",
            icon: "education"
        }, hidden: false, alwaysShow: true,
        children: [{
            path: "robots",
            component: SubMenuContainer,
            name: "robots",
            meta: {
                title: "robots",
                icon: "education"
            }, hidden: false, alwaysShow: true, children: [{
                path: "robot",
                component: () => import("@/views/robotDemo/robots/robot/robot_list"),
                name: "robot",
                meta: {
                    title: "robot",
                    icon: ""
                }, hidden: false, alwaysShow: true
            }]
        }]
    }, {
        path: "/paasPortal/test",
        name: "SUB--1",
        meta: {
            title: "SUB--1",
            icon: "education"
        }, hidden: false, alwaysShow: true,
        children: [{
            path: "dashboard",
            meta: {
                title: "dashboard",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "demo/labels",
            meta: {
                title: "标签",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "robetDemo/robots",
            meta: {
                title: "robots",
                icon: ""
            }, hidden: false, alwaysShow: true
        }]
    }, {
        path: "/paasPortal/demo",
        name: "SUB--2",
        meta: {
            title: "SUB--2",
            icon: "education"
        }, hidden: false, alwaysShow: true,
        children: [{
            path: "dashboard",
            meta: {
                title: "dashboard",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "demo/labels",
            meta: {
                title: "标签",
                icon: ""
            }, hidden: false, alwaysShow: true
        }, {
            path: "robetDemo/robots",
            meta: {
                title: "robots",
                icon: ""
            }, hidden: false, alwaysShow: true
        }]
    }
];
export default intranetRouter;