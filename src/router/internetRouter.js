/* Layout */
import Layout from "@/layout";
import SubMenuContainer from "@/layout/components/SubMenuContainer.vue";
let internetRouter = [ 
    {
        path: "/robotDemo",
        component: Layout,
        name: "robetDemo",
        meta: {
            title: "请求示例",
            icon: "education"
        }, hidden: false, alwaysShow: true, children: [{
            path: "robots",
            component: SubMenuContainer,
            name: "robots",
            meta: {
                title: "robots",
                icon: "education"
            }, hidden: false, alwaysShow: true, children: [{
                path: "robot",
                component: () => import(/*webpackname:robot*/"@/views/robotDemo/robots/robot/robot_list"),
                name: "robot",
                meta: {
                    title: "robot",
                    icon: ""
                }, hidden: false, alwaysShow: true
            }]
        }]
    }, 
    {
        path: "/paasPortal/test",
        name: "SUB--1",
        meta: {
            title: "SUB--1",
            icon: "education"
        }, hidden: false, alwaysShow: true, children: [
            {
                path: "dashboard",
                meta: {
                    title: "dashboard",
                    icon: ""
                }, hidden: false, alwaysShow: true
            },
            {
                path: "demo/labels",
                meta: {
                    title: "标签",
                    icon: ""
                }, hidden: false, alwaysShow: true
            }, 
            {
                path: "robetDemo/robots",
                meta: {
                    title: "robots",
                    icon: ""
                }, hidden: false, alwaysShow: true
            }]
    }, {
        path: "/paasPortal/demo",
        name: "SUB--2",
        meta: {
            title: "SUB--2",
            icon: "education"
        }, hidden: false, alwaysShow: true, children: [
            {
                path: "dashboard",
                meta: {
                    title: "dashboard",
                    icon: ""
                }, hidden: false, alwaysShow: true
            }, 
            {
                path: "demo/labels",
                meta: {
                    title: "标签",
                    icon: ""
                }, hidden: false, alwaysShow: true
            }, 
            {
                path: "robetDemo/robots",
                meta: {
                    title: "robots",
                    icon: ""
                }, hidden: false, alwaysShow: true
            }
        ]
    }];
export default internetRouter;