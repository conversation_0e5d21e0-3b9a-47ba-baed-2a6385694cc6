import Cookies from 'js-cookie';
const base64url = require('base64url');

const TokenKey = 'Admin-token';

export function getToken() {
    return Cookies.get(TokenKey);
}

export function setToken(token, time) {
    if (time && typeof time === 'number') {
        return Cookies.set(TokenKey, token, { expires: time });
    } else {
        return Cookies.set(TokenKey, token);
    }
}

export function removeToken() {
    return Cookies.remove(TokenKey);
}
// 设置其他的cookie
export function setOtherCookie(key, token) {
    return Cookies.set(key, token);
}

// 设置其他的cookie
export function getOtherCookie(TokenKey) {
    return Cookies.get(TokenKey);
}

export function validToken() {
    const ValidToken = getToken();
    const CurrentTime = new Date();
    const TokenTime = new Date(JSON.parse(base64url.decode(ValidToken.split('.')[1])).exp * 1000);
    // console.log(CurrentTime, '当前时间', TokenTime, '有效的时间')
    return CurrentTime <= TokenTime;
}

// 解析token获取用户id，系统id和分公司id
export function explainToken(token) {
    const sourceToken = JSON.parse(base64url.decode(token.split('.')[1]));
    return sourceToken;
}
