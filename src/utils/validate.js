/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */
/* eslint-disable */
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
    return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
    // const valid_map = ['admin', 'editor']
    //  return valid_map.indexOf(str.trim()) >= 0
    return str != null;
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
    const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
    return reg.test(url);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
    const reg = /^[a-z]+$/;
    return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
    const reg = /^[A-Z]+$/;
    return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
    const reg = /^[A-Za-z]+$/;
    return reg.test(str);
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
    const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return reg.test(email);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
    if (typeof str === "string" || str instanceof String) {
        return true;
    }
    return false;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
    if (typeof Array.isArray === "undefined") {
        return Object.prototype.toString.call(arg) === "[object Array]";
    }
    return Array.isArray(arg);
}

/* 手机号码 */
export function validateMobile(rule, value, callback) {
    const reg = /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/;
    if (reg.test(value)) {
        callback();
    } else {
        return callback(new Error("请输入正确的手机号"));
    }
}

/* 小数点后面只留两位 */

export function checkNumPot2(rule, value, callback) {
    const reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if (!value) {
        return callback(new Error("请填写数字"));
    } else if (!reg.test(value)) {
        return callback(new Error("请填写数字,最多2位小数"));
    } else {
        callback();
    }
}

/* 非法字符 字母数字中文 */

export function charActer(rule, value, callback) {
    // console.log(value);
    const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/;
    if (!value) {
        return callback(new Error("请填写用户名称"));
    } else if (!reg.test(value)) {
        return callback(new Error("请输入用户名称,且不能是非法字符"));
    } else {
        callback();
    }
}

/* 非法字符 字母数字 */

export function charActer2(rule, value, callback) {
    // console.log(value);
    const reg = /^[A-Za-z0-9]+$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入字母或数字"));
    } else {
        callback();
    }
}

/* 车牌号校验 */

export function licensePlateNumber(rule, value, callback) {
    // console.log(value);
    const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的车牌号"));
    } else {
        callback();
    }
}

/* 车架号校验 */

export function carFrameNumber(rule, value, callback) {
    // console.log(value);
    const reg = /^[A-Za-z0-9]+$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的车架号"));
    } else {
        callback();
    }
}

/* 固定电话校验 */

export function fixedTelephone(rule, value, callback) {
    // console.log(value);
    const reg = /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的固定电话号"));
    } else {
        callback();
    }
}

/* 银行卡号校验 */

export function bankCardNumber(rule, value, callback) {
    // console.log(value);
    const reg = /^([1-9]{1})(\d{14}|\d{18})$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的银行卡号"));
    } else {
        callback();
    }
}

/* 身份证号校验 */

export function IDNumber(rule, value, callback) {
    // console.log(value);
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的身份证号"));
    } else {
        callback();
    }
}

/* 组织机构代码证校验 */

export function organizationalCodeCertificate(rule, value, callback) {
    // console.log(value);
    const reg = /^[a-zA-Z0-9]{10,20}$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的组织机构代码证号"));
    } else {
        callback();
    }
}

/* 传真校验 */

export function Fax(rule, value, callback) {
    // console.log(value);
    const reg = /^(\d{3,4}-)?\d{7,8}$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的传真号"));
    } else {
        callback();
    }
}

/* 邮编校验 */

export function postCode(rule, value, callback) {
    // console.log(value);
    const reg = /^[1-9][0-9]{5}$/;
    if (!reg.test(value)) {
        return callback(new Error("请输入正确的邮编"));
    } else {
        callback();
    }
}
