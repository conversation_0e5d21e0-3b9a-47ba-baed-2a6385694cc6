// 写cookies
export function setCookie(name, value, expires) {
    let cookieVal = `${name}=${escape(value)};path=/`;
    if (expires) {
        var exp = new Date();
        exp.setTime(exp.getTime() + expires * 24 * 60 * 60 * 1000);
        cookieVal += `;expires=${exp.toGMTString()}`;
    }
    document.cookie = cookieVal;
}

// 读取cookies
export function getCookie(name) {
    let arr = [];
    const reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

    if ((arr = document.cookie.match(reg))) {
        return unescape(arr[2]);
    } else {
        return null;
    }
}

// 删除cookies
export function delCookie(name) {
    // var exp = new Date();
    // exp.setTime(exp.getTime() - 1);
    // var cval = getCookie(name);
    // if (cval != null) {
    //     // document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString();
    //     // document.cookie = name + "=" + cval + ";expires=Thu, 01 Jan 1970 00:00:00 UTC;";
    // }
    setCookie(name, "", -1);
}
