import Cookies from 'js-cookie';
/**
 * 存储数据的方法
 * @method setStorage
 * @param {String} key 必填 存储数据键值对中的键
 * @param {*} val 必填 存储数据键值对中的值
 * @param {String} type 非必填 默认local，参数只有三个:local/session/cookie。即设置存储的三种方式：local(相当于locaStorage)，session(相当于sessionStorage),cookie(相当于cookie)
 * @param {Number} time 非必填 存储的有效期，必须是整数，以天为单位
 * @param {String} path 非必填 存储方式为cookie时，指定path存储
*/
export function getType(val) {
    let valType = null;
    switch (Object.prototype.toString.call(val)) {
        case "[object String]":
            valType = "str";
            break;
        case "[object Object]":
            valType = "obj";
            break;
        case "[object Array]":
            valType = "ary";
            break;
    }
    return valType;
}
function getTime(value, time) {
    if (time && time > 0) {
        value.expires = time;
        value.startTime = new Date().getTime();
    }
    return value;
}
export function setStorage(key, val, type, time, path) {
    // const valType = null;
    if (!(key && typeof key === "string")) {
        console.error("key为必填参数且必须是String！");
        return;
    }
    if (!val) {
        console.error("val是必填参数！");
        return;
    }
    if (time && !(/(^[1-9]\d*$)/.test(time))) {
        console.error("time必须是正整数！");
        return;
    }
    if (path && typeof path !== "string") {
        console.error("path必须是String！");
        return;
    }
    const valType = getType(val);
    // if (typeof val === "string") {
    //     valType = "str";
    // } else if (val instanceof Date) {
    //     valType = "date";
    // } else if (Object.prototype.toString.call(val) === "[object Object]") {
    //     valType = "obj";
    // } else if (Object.prototype.toString.call(val) === "[object Array]") {
    //     valType = "ary";
    // }
    const value = { content: val, type: valType };
    !type && (type = "local");
    switch (type) {
        case 'local':
            // if (time && time > 0) {
            //     value.expires = time;
            //     value.startTime = new Date().getTime();
            // }
            getTime(value, time);
            localStorage.setItem(key, JSON.stringify(value));
            break;
        case 'session':
            // if (time && time > 0) {
            //     value.expires = time;
            //     value.startTime = new Date().getTime();
            // }
            getTime(value, time);
            sessionStorage.setItem(key, JSON.stringify(value));
            break;
        case 'cookie':
            // var str = `picc-${key}=${JSON.stringify(value)}`;
            // if (time > 0) {
            //     var date = new Date();
            //     date.setTime(date.getTime() + time * 1000);
            //     str += "; expires=" + date.toGMTString() + ";path=/" + path;
            // }
            // document.cookie = str;
            !time && !path && Cookies.set(key, JSON.stringify(value));
            if (time && time > 0) {
                if (path) {
                    Cookies.set(key, JSON.stringify(value), { expires: time, path: `/${path}` });
                } else {
                    Cookies.set(key, JSON.stringify(value), { expires: time });
                }
            }
            // time && time > 0 && path && Cookies.set(key, JSON.stringify(value), { expires: time, path: `/${path}` });
            // time && time > 0 && !path && Cookies.set(key, JSON.stringify(value), { expires: time });
            break;
        default:
            console.error("type必须是local/session/cookie哦！");
    }
}

/**
 * 获取单条存储数据的方法
 * @method getStorage
 * @param {String} key 必填 获取存储数据键值对中的键
 * @param {String} type 非必填 默认local，参数只有三个:local/session/cookie。即获取存储的三种方式：local(相当于locaStorage)，session(相当于sessionStorage),cookie(相当于cookie)
 * @returns {*} 返回存储的数据 如果没有存储则返回空字符串’‘
 */
export function getStorage(key, type) {
    if (!key) {
        return console.error("key为必填参数！");
    }
    if (typeof key !== "string") {
        return console.error("key必须是String！");
    }
    !type && (type = "local");
    switch (type) {
        case 'local':
            if (localStorage) {
                var localData = JSON.parse(localStorage.getItem(key));
                if (localData != null) {
                    if (localData.startTime) {
                        const date = new Date().getTime();
                        if (date - localData.startTime > localData.expires * 1000) {
                            localStorage.removeItem(key);
                            return console.error("此数据存储已过期！");
                        } else {
                            return localData.content;
                        }
                    } else {
                        return localData.content;
                    }
                } else {
                    return "";
                }
            }
            break;
        case 'session':
            if (sessionStorage) {
                var sessionData = JSON.parse(sessionStorage.getItem(key));
                if (sessionData != null) {
                    if (sessionData.startTime) {
                        const date = new Date().getTime();
                        if (date - sessionData.startTime > sessionData.expires * 1000) {
                            sessionStorage.removeItem(key);
                            return console.error("此数据存储已过期！");
                        } else {
                            return sessionData.content;
                        }
                    } else {
                        return sessionData.content;
                    }
                } else {
                    return "";
                }
            }
            break;
        case 'cookie':
            // if (document.cookie) {
            //     var reg = new RegExp("(^| )" + "picc-" + key + "=([^;]*)(;|$)");
            //     var cookieData = document.cookie.match(reg);
            //     if (cookieData === null) {
            //         return "";
            //     } else {
            //         return JSON.parse(cookieData[2]).content;
            //     }
            // }
            if (document.cookie) {
                return Cookies.get(key);
            }
            break;
        default:
            console.error("type必须是local/session/cookie哦！");
    }
}
/**
 * 获取key的所有存储数据列表
 * @method getAllStorage
 * @param {String} key 获取存储的key
 * @returns {Array} 返回key的所有数据存储列表 [{value:存储数据,type:存储类型}] value:* type：localStorage/sessionStorage/cookie
 */
export function getAllStorage(key) {
    if (!key) {
        return console.error("key为必填参数！");
    }
    if (typeof key !== "string") {
        return console.error("key必须是String！");
    }
    var localData = {};
    var sessionData = {};
    var cookieData = {};
    var reg = new RegExp("(^| )" + key + "=([^;]*)(;|$)");
    var ary = [];
    if (localStorage) {
        localData = JSON.parse(localStorage.getItem(key));
        if (localData != null && JSON.stringify(localData) !== "{}") {
            ary.push({ value: localData, type: "localStorage" });
        }
    }
    if (sessionStorage) {
        sessionData = JSON.parse(sessionStorage.getItem(key));
        if (sessionData != null && JSON.stringify(sessionData) !== "{}") {
            ary.push({ value: sessionData, type: "sessionStorage" });
        }
    }
    if (document.cookie) {
        cookieData = document.cookie.match(reg);
        if (cookieData != null && JSON.stringify(cookieData) !== "{}") {
            ary.push({ value: cookieData, type: "cookie" });
        }
    }
    if (ary.length >= 1) {
        return ary;
    } else {
        console.error(`没有任何key为${key}的存储数据！`);
    }
}

/**
 * 删除单条存储数据
 * @method removeStorage
 * @param {String} key 必填 存储数据键值对中的键
 * @param {String} type 非必填 默认local，参数只有三个:local/session/cookie。即存储的三种方式：local(相当于locaStorage)，session(相当于sessionStorage),cookie(相当于cookie)
 * @param {String} path 非必填 存储方式为cookie时，指定删除path上的存储
 */
export function removeStorage(key, type, path) {
    if (!key) {
        return console.error("key为必填参数！");
    }
    if (typeof key !== "string") {
        return console.error("key必须是String！");
    }
    !type && (type = "local");
    if (type === "local") {
        if (key in localStorage) {
            localStorage.removeItem(key);
        } else {
            return undefined;
        }
    } else if (type === "session") {
        if (key in sessionStorage) {
            sessionStorage.removeItem(key);
        } else {
            return undefined;
        }
    } else if (type === "cookie") {
        // var exp = new Date();
        // exp.setTime(exp.getTime() - 1);
        // var cval = getStorage(key, "cookie");
        // document.cookie =
        //     "picc-" + key + "=" + cval + "; expires=" + exp.toGMTString();
        !path && Cookies.remove(key);
        path && Cookies.remove(key, { path: `/${path}` });
    } else {
        return undefined;
    }
}

/**
 * 删除key的所有存储数据列表
 * @method removeStorageList
 * @param {string} key 必填 存储数据键值对中的键
 * @param {String} path 非必填 存储方式为cookie时，指定删除path上的存储
 */
export function removeStorageList(key, path) {
    if (!key) {
        return console.error("key为必填参数！");
    }
    if (typeof key !== "string") {
        return console.error("key必须是String！");
    }
    if (key in localStorage) {
        localStorage.removeItem(key);
    }
    if (key in sessionStorage) {
        sessionStorage.removeItem(key);
    }
    if (document.cookie) {
        // var exp = new Date();
        // exp.setTime(exp.getTime() - 1);
        // var cval = getStorage(key, "cookie");
        // document.cookie =
        //     "picc-" + key + "=" + cval + "; expires=" + exp.toGMTString();
        !path && Cookies.remove(key);
        path && Cookies.remove(key, { path: `/${path}` });
    }
}

/**
 * 删除浏览器中所有存储数据的函数
 * @method clearStorage
 */
export function clearStorage() {
    if (localStorage) {
        localStorage.clear();
    }
    if (sessionStorage) {
        sessionStorage.clear();
    }
    if (document.cookie) {
        // var key = document.cookie.match(/[^=;]+(?==")/g);
        // Cookies.remove(key);
        var keys = document.cookie.match(/[^=;]+(?==")/g);
        // keys.forEach(value => (document.cookie = `${value}=0; max-age=0`));
        keys.forEach(value => { document.cookie = `${value}=0; max-age=0`; });
    }
}

/**
 * 以用户为单位存储
 * @method setStorageUser
 * @param {String} key 必填 存储数据键值对中的键
 * @param {String} userId 必填 用户id
 * @param {*} val 必填 存储数据键值对中的值
 * @param {String} type 非必填 默认local，参数只有三个:local/session/cookie。即设置存储的三种方式：local(相当于locaStorage)，session(相当于sessionStorage),cookie(相当于cookie)
 * @param {Number} time 非必填 存储的有效期，必须是正整数，以天为单位
 * @param {String} path 非必填 存储方式为cookie时，指定path存储
 */
export function setStorageUser(key, userId, val, type, time, path) {
    if (!userId) {
        return console.error("userId为必填参数！");
    }
    if (typeof key !== "string") {
        return console.error("key必须是String！");
    }
    if (typeof userId !== "string") {
        return console.error("userId必须是String！");
    }
    var keyId = key + userId;
    setStorage(keyId, val, type, time, path);
}

/**
 * 获取相关用户的存储
 * @method getStorageUser
 * @param {String} key 必填 存储数据键值对中的键
 * @param {String} userId 必填 用户id
 * @param {String} type type 非必填 默认local，参数只有三个:local/session/cookie。即设置存储的三种方式：local(相当于locaStorage)，session(相当于sessionStorage),cookie(相当于cookie)
 * @returns {*} 返回存储的数据 如果没有存储则返回空字符串’‘
 */
export function getStorageUser(key, userId, type) {
    if (!userId) {
        return console.error("userId为必填参数！");
    }
    if (typeof userId !== "string") {
        return console.error("userId必须是String！");
    }
    var keyId = key + userId;
    return getStorage(keyId, type);
}
