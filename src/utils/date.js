export const formatDate = (value, fmt) => {
    if (value !== null) {
        var d = new Date(value);
        var o = {
            "M+": d.getMonth() + 1, // 月份
            "d+": d.getDate(), // 日
            "H+": d.getHours(), // 小时
            "m+": d.getMinutes(), // 分
            "s+": d.getSeconds(), // 秒
            "q+": Math.floor((d.getMonth() + 3) / 3), // 季度
            S: d.getMilliseconds() // 毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                (d.getFullYear() + "").substr(4 - RegExp.$1.length)
            );
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(
                    RegExp.$1,
                    RegExp.$1.length === 1
                        ? o[k]
                        : ("00" + o[k]).substr(("" + o[k]).length)
                );
            }
        }
        return fmt;
    }
};

export const dateTimeFilter = (input) => {
    if (input !== null) {
        var d = new Date(input);
        var year = d.getFullYear();
        var month =
            d.getMonth() + 1 < 10 ? "0" + (d.getMonth() + 1) : d.getMonth() + 1;
        var day = d.getDate() < 10 ? "0" + d.getDate() : "" + d.getDate();
        var hour = d.getHours() < 10 ? "0" + d.getHours() : d.getHours();
        var minutes =
            d.getMinutes() < 10 ? "0" + d.getMinutes() : d.getMinutes();
        var seconds =
            d.getSeconds() < 10 ? "0" + d.getSeconds() : d.getSeconds();
        return (
            year +
            "-" +
            month +
            "-" +
            day +
            " " +
            hour +
            ":" +
            minutes +
            ":" +
            seconds
        );
    }
};
export const dateFilter = (input) => {
    if (input !== null) {
        var d = new Date(input);
        var year = d.getFullYear();
        var month =
            d.getMonth() + 1 < 10 ? "0" + (d.getMonth() + 1) : d.getMonth() + 1;
        var day = d.getDate() < 10 ? "0" + d.getDate() : "" + d.getDate();
        return year + "-" + month + "-" + day;
    }
};
