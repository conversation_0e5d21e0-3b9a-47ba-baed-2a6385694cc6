// import Vue from 'vue'
// import VeeValidate from 'vee-validate'
// import VueI18n from 'vue-i18n'
// import zh_CN from '/vee-validate/dist/locale/zh_CN'
import VeeValidate, { Validator } from 'vee-validate';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import validationMessages from 'vee-validate/dist/locale/zh_CN';

Vue.use(VueI18n);

const i18n = new VueI18n();
i18n.locale = 'zh_CN'; // set a default locale (without it, it won't work)

Vue.use(VeeValidate, {
    fieldsBagName: 'vee-fields',
    classes: true,
    classNames: {
        valid: 'is-valid',
        invalid: 'is-invalid'
    },
    events: 'blur|keyup',
    // mode: 'eager',
    i18nRootKey: 'validations', // customize the root path for validation messages.
    i18n,
    dictionary: {
        zh_CN: validationMessages
    }
});
// 自定义validate
const Dictionary = {
    zh_CN: {
        messages: {
            required: (field) => field + '不能为空',
            email: name => name + '请使用正确的邮件格式'
        },
        attributes: {// 与标签的name属性相关联
            email: '邮件',
            password: '密码',
            phone: '手机'
        }
    }
};
// 自定义validate error 信息
Validator.localize(Dictionary);
/* 自定义方法 */
Validator.extend('mobile', {
    getMessage: field => '手机格式不正确',
    validate: value =>
        value.length === 11 && /^((13|14|15|17|18)[0-9]{1}\d{8})$/.test(value)
});
/* 汽车牌照方法 */
Validator.extend('car', {
    getMessage: field => '汽车牌照格式不正确',
    validate: value =>
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[警京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{0,1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/.test(value)
});
/* 手机号码 */
Validator.extend('validateMobile', {
    getMessage: field => '手机号码格式不正确',
    validate: value =>
        /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/.test(value)
});
/* 小数点后面只留两位 */
Validator.extend('checkNumPot2', {
    getMessage: field => '请填写数字,最多2位小数',
    validate: value =>
        /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(value)
});
/* 非法字符，字母数字中文 */
Validator.extend('charActer', {
    getMessage: field => '请输入用户名称,且不能是非法字符',
    validate: value =>
        /^[A-Za-z0-9\u4e00-\u9fa5]+$/.test(value)
});
/* 非法字符 字母数字 */
Validator.extend('carFrameNumber', {
    getMessage: field => '请输入字母或数字',
    validate: value =>
        /^[A-Za-z0-9]+$/.test(value)
});
/* 车架号校验 */
Validator.extend('carFrameNumber', {
    getMessage: field => '请输入正确的车架号',
    validate: value =>
        /^[A-Za-z0-9]+$/.test(value)
});
/* 固定电话校验 */
Validator.extend('fixedTelephone', {
    getMessage: field => '请输入正确的固定电话号',
    validate: value =>
        /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/.test(value)
});
/* 银行卡号校验 */
Validator.extend('bankCardNumber', {
    getMessage: field => '请输入正确的银行卡号',
    validate: value =>
        /^([1-9]{1})(\d{14}|\d{18})$/.test(value)
});
/* 身份证号校验 */
Validator.extend('IDNumber', {
    getMessage: field => '请输入正确的身份证号',
    validate: value =>
        /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)
});
/* 组织机构代码证校验 */
Validator.extend('organizationalCodeCertificate', {
    getMessage: field => '请输入正确的组织机构代码证号',
    validate: value =>
        /^[a-zA-Z0-9]{10,20}$/.test(value)
});
/* 传真校验 */
Validator.extend('Fax', {
    getMessage: field => '请输入正确的传真号',
    validate: value =>
        /^(\d{3,4}-)?\d{7,8}$/.test(value)
});
/* 邮编校验 */
Validator.extend('', {
    getMessage: field => '',
    validate: value =>
        /^[1-9][0-9]{5}$/.test(value)
});
/*  */
Validator.extend('postCode', {
    getMessage: field => '请输入正确的邮编',
    validate: value =>
        /^[1-9][0-9]{5}$/.test(value)
});
