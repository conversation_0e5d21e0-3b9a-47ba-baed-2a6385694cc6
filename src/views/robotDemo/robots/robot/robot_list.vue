<template>
    <div class="app-container">
        <div class="filter-container">
            <el-form class="isbackground" style="width: 95%">
                <!-- <h4 class="border-left">查询条件</h4> -->
                <el-row :gutter="10">
                    <el-col :span="9">
                        <el-form-item label="机构代码">
                            <el-input v-model="listQuery.comCode" style="width: 70%" placeholder="请输入..." />
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-form-item label="机器人编号">
                            <el-input v-model="listQuery.robotSn" style="width: 70%" placeholder="请输入..." />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" @click="queryList">
                                搜索
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="opera-table-btn">
            <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate">
                新增
            </el-button>
        </div>
        <div style="margintop: 30px">
            <el-table :data="taskList" style="width: 100%">
                <el-table-column prop="comCode" label="机构代码" />
                <el-table-column prop="nickname" label="昵称" />
                <el-table-column prop="rechargeCount" label="充电次数" />
                <el-table-column prop="robotHeight" label="高度" />
                <el-table-column prop="robotSn" label="机器人编号" />
                <el-table-column prop="version" label="版本号" />
                <el-table-column prop="insertTimeForHis" label="创建时间" />
                <el-table-column label="操作" align="center" width="200px">
                    <template slot-scope="scope">
                        <div style="display: inline-block">
                            <!-- <el-button-group> -->
                            <!-- <el-button type="primary" style="padding:0" size="mini" @click="edit(scope.row)">
                                编辑
                            </el-button> -->
                            <el-button icon="el-icon-edit" class="picc-button--icon" size="mini" style="min-width: 20px" @click="edit(scope.row)" />
                            <!-- <el-button type="danger" size="mini" @click="delete scope.row">
                                删除
                            </el-button> -->
                            <el-button icon="el-icon-delete" class="picc-button--icon" size="mini" style="min-width: 20px" @click="delete scope.row" />
                            <!-- </el-button-group> -->
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页
            current-page 第几页的下标
            page-sizes 每页显示个数选择器的选项设置
            page-size 每页显示条目个数
            total 总数据多少条
            -->
        <div class="pagination-container">
            <el-pagination
                :current-page="listQuery._pageNo"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="listQuery._pageSize"
                :total="listQuery.totalCount"
                background
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 公用组件 模态框
         dialogConfig 用来设置模态框的title 和尾部按钮
         dialogConfig:{
          titleMap:{ //title
          UserDetails:"详情"
          },
          submitTextMap: {} //代表尾部只出现取消
          },
          close  关闭的事件
          dialogAddType 控制模态框是否显示 不等于 close 就会显示
            -->
        <el-dialog title="样例添加表单" :visible.sync="dialogAddType" width="50%">
            <robotAdd ref="formLog" :model="model" @closeDialog="closeDialog" />
        </el-dialog>
        <el-dialog title="样例编辑表单" :visible.sync="dialogEditType" width="50%">
            <robotEdit ref="formLog" :model="model" :temp="editTemp" @closeDialog="closeDialog" />
        </el-dialog>
        <el-table :data="tableData" height="250" border style="width: 100%">
            <el-table-column prop="date" label="日期" width="180" />
            <el-table-column prop="name" label="姓名" width="180" />
            <el-table-column prop="address" label="地址" />
        </el-table>
    </div>
</template>

<script>
import robotAdd from "./robot_add"; // 引入模态框的表单列表
import robotEdit from "./robot_edit"; // 引入模态框的表单列表
import instance from "./service"; // 引入 service里面的对象
export default {
    components: {
        // 注入到vue模版中
        robotAdd,
        robotEdit
    },
    data() {
        return {
            tableData: [
                {
                    date: "2016-05-03",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-02",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-04",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-01",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-08",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-06",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-07",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                }
            ],
            editTemp: {},
            taskList: [], // 权限管理的列表
            listLoading: false, // 列表加载的loading
            dialogAddType: false, // 模态框默认隐藏
            dialogEditType: false,
            listQuery: {
                // 请求接口的参数
                _pageNo: 1,
                _pageSize: 10,
                totalCount: 0,
                comCode: undefined,
                robotSn: undefined,
                order: [
                    {
                        direction: "asc",
                        property: "taskCode"
                    }
                ]
            },
            selectedList: [], // 列表选中的数据
            model: {} // 模态框的数据
        };
    },
    created() {
        instance.queryRobotList(this.listQuery).then((list) => {
            // 对象结构赋值，大括号中的对象可以当作参数再本函数中使用
            console.log(list, "...............");
            const { pageNo, perPage, totalCount, data } = list;
            this.listQuery = { _pageNo: pageNo, _pageSize: perPage, totalCount: totalCount };
            this.taskList = [...data];
        });
    },
    methods: {
        queryList() {
            // 查询列表的方法
            this.listLoading = true;
            instance
                .queryRobotList(this.listQuery)
                .then((list) => {
                    // 对象结构赋值，大括号中的对象可以当作参数再本函数中使用
                    const { pageNo, perPage, totalCount, data } = list.data;
                    this.listQuery = { _pageNo: pageNo, _pageSize: perPage, totalCount: totalCount };
                    this.taskList = [...data];
                    for (let i = 0; i < this.taskList.length; i++) {
                        const element = this.taskList[i];
                        element.selectModel = false;
                    }
                    this.listLoading = false;
                })
                .catch(() => {
                    this.listLoading = false;
                });
        },
        //   删除当前列
        handleDelete(row) {
            this.$confirm("此操作将永久删除, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                instance
                    .deleteRobot(row.id)
                    .then((date) => {
                        this.$message.success("删除成功");
                        this.queryList();
                    })
                    .catch(() => {
                        this.$message.error("删除失败");
                    });
            });
        },
        // 每页几行
        handleSizeChange(size) {
            this.listQuery._pageSize = size;
            this.queryList();
        },
        // 第几页的下标
        handleCurrentChange(index) {
            this.listQuery._pageNo = index;
            this.queryList();
        },
        onSelect(selectedList) {
            // 当表格行被选中
            this.selectedList = selectedList;
        },
        // 模态框提交
        submit(dialogAddType, done) {
            this.$refs.formLog
                .validate()
                .then((data) => {
                    instance
                        .addRobot(data)
                        .then(() => {
                            this.$message.success("新增成功");
                            this.queryList();
                        })
                        .catch(() => {
                            this.$message.error("新增失败");
                        });

                    this.dialogAddType = "close";
                })
                .catch((err) => {
                    this.$message(err);
                });
        },
        // 关闭模态框
        onDialogClose() {
            this.dialogAddType = false;
        },
        //  新增模态框
        handleCreate() {
            this.dialogAddType = true;
        },
        // 编辑方法
        edit(row) {
            this.dialogEditType = true;
            this.editTemp = row;
            // console.log(row);
        },
        // 删除方法
        delete(row) {
            // console.log(row);
        },
        //关闭dialog
        closeDialog() {
            this.dialogAddType = false;
            this.dialogEditType = false;
        }
    }
};
</script>
<style scoped>
.border-left {
    border-left: 3px #8ab7de solid;
    padding-left: 20px;
}
.isbackground {
    background: white;
    border-radius: 5px;
}
</style>
<style lang="scss">
.el-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
/*正常情况下滑块的样式*/
.el-table__body {
    width: 100% !important;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 4px;
    // -webkit-box-shadow: inset 1px 1px 0 #dddddd;
}
/*鼠标悬浮在该类指向的控件上时滑块的样式*/

.el-table__body-wrapper:hover::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 4px;
    // -webkit-box-shadow: inset 1px 1px 0 #dddddd;
}
/*鼠标悬浮在滑块上时滑块的样式*/

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background-color: #dddddd;
    // -webkit-box-shadow: inset 1px 1px 0 #dddddd;
}
/*正常时候的主干部分*/

.el-table__body-wrapper::-webkit-scrollbar-track {
    border-radius: 4px;
    // -webkit-box-shadow: inset 0 0 6px #dddddd;
    background-color: white;
}
/*鼠标悬浮在滚动条上的主干部分*/

.el-table__body-wrapper::-webkit-scrollbar-track:hover {
    // -webkit-box-shadow: inset 0 0 6px #dddddd;
    background-color: #ffffff;
}
</style>
