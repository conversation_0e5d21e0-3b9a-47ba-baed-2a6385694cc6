<template>
    <div class="app-container ">
        <el-form ref="dataForm" :rules="rules" :model="tempData" label-position="" label-width="30%" class="user-Form-cntent" size="mini">
            <el-form-item label="机构代码" prop="comCode">
                <el-input v-model="tempData.comCode" placeholder="请输入..." />
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
                <el-input v-model="tempData.nickname" placeholder="请输入..." />
            </el-form-item>
            <el-form-item label="充电次数" prop="rechargeCount">
                <el-input v-model="tempData.rechargeCount" placeholder="请输入..." />
            </el-form-item>
            <el-form-item label="高度" prop="robotHeight">
                <el-input v-model="tempData.robotHeight" placeholder="请输入..." />
            </el-form-item>
            <el-form-item label="机器人编号" prop="robotSn">
                <el-input v-model="tempData.robotSn" placeholder="请输入..." />
            </el-form-item>
            <el-form-item label="机器人生产日期" prop="manufactureDate">
                <el-date-picker
                    v-model="tempData.manufactureDate"
                    type="date"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择日期"
                />
            </el-form-item>
            <el-form-item label="机器人生产厂商" prop="manufactureName">
                <el-input v-model="tempData.manufactureName" placeholder="请输入..." />
            </el-form-item>
            <el-form-item label="版本号" prop="version">
                <el-input v-model="tempData.version" placeholder="请输入..." />
            </el-form-item>
            <el-form-item label="创建时间" prop="insertTimeForHis">
                <el-date-picker
                    v-model="tempData.insertTimeForHis"
                    type="date"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择日期"
                />
            </el-form-item>
        </el-form>
        <el-row>
            <el-col style="text-align:center">
                <el-button type="primary" @click="validate">
                    保存
                </el-button>
                <el-button @click="tabsViewsClose">
                    取消
                </el-button>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import instance from "./service"; // 引入 service里面的对象
export default {
    props: {
        // 用来接受父组件传过来的值
        // model: {
        //     required: true
        // },
        temp: {
            // 表单的数据
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 表单校验规则，自定义校验规则从utils/validate.js引入
            tempData:this.temp,
            rules: {
                comCode: [{ required: true, message: '请输入机构代码', trigger: 'blur' }],
                nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
                rechargeCount: [{ required: true, message: '请输入充电次数', trigger: 'blur' }],
                robotHeight: [{ required: true, message: '请输入高度', trigger: 'blur' }],
                robotSn: [{ required: true, message: '请输入机器人编号', trigger: 'blur' }],
                version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
                insertTimeForHis: [{ required: true, message: '请输入创建时间', trigger: 'change' }],
                manufactureDate: [{ required: true, message: '请输入生产日期', trigger: 'change' }],
                manufactureName: [{ required: true, message: '请输入机器人生产厂商', trigger: 'change' }]
            }
        };
    },
    created() {
        instance.findTaskByRobotCode(this.$route.query.robotParam.id).then((data) => {
            // console.log(data.data);
            this.tempData = {
                // 初始化的时候加载
                ...data.data
            };
        });
        this.waitingResponse = true;
    },
    methods: {
        validate() {
            return new Promise((resolve, reject) => {
                this.$refs.dataForm.validate((vaild) => {
                    if (vaild) {
                        resolve({ ...this.tempData });
                        instance.editRobot({ ...this.tempData }).then(() => {
                            this.$message({
                                message: "保存成功！",
                                type: "success",
                                onClose: () => {
                                    this.tabsViewsClose();
                                }
                            });
                        });
                    } else {
                        reject(new Error("请正确输入内容！"));
                    }
                });
            });
        },
        // 关闭当前页面的方法
        tabsViewsClose() {
            // const spanContener = document.getElementsByClassName("tags-view-container")[0];
            // const selectTabs = spanContener.getElementsByClassName("active")[0];
            // const selectTabsclose = selectTabs.getElementsByClassName("el-icon-close")[0];
            // selectTabsclose.click();
            // this.$router.push({ path: "/robotDemo/robots/robot" });
            this.$emit('closeDialog');
        }
    }
};
</script>

<style scoped>
.user-Form {
    display: flex;
    flex-wrap: wrap;
}

.el-form-item {
    width: 80%;
    padding: 0 10px 0 30px;
}
.el-select {
    width: 100%;
}
.user-Form-cntent {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.el-form-item__label {
    width: 30%;
}
</style>
