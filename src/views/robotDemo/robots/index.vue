<template>
    <!-- <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews"> -->
    <router-view />
    <!-- </keep-alive>
    </transition>
  </section> -->
</template>
<!--  放置二级路由的容器 -->
<script>
export default {
    // 缓存处理的方法 先用computed监听 route.name的值 return 返回 然后通过wacth 监听key  赋值给 this.$options.name
    name: 'RoleAuthority',
    computed: {
        cachedViews() {
            return this.$store.state.tagsView.cachedViews;
        },
        key() {
            return this.$route.name;
        }
    },
    watch: {
        key(value) {
            this.$options.name = value;
        }
    }
};
</script>

<style scoped>
.app-main {
  /*84 = navbar + tags-view = 50 +34 */
  min-height: calc(100vh - 84px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
</style>
