<template>
    <div class="app-container">
        <!-- <el-form ref="dataForm" :rules="rules" :model="temp" label-position="" label-width="30%" class="user-Form-cntent" size="mini">
            <el-form-item label="机构代码" prop="comCode">
                <el-input v-model="temp.comCode" placeholder="请输入..." />
            </el-form-item>
        </el-form>
        <el-row style="padding-top: 10px">
            <el-col style="text-align: center">
                <el-button type="primary" @click="validate"> 保存 </el-button>
            </el-col>
        </el-row> -->
        <!-- <iframe
            src="/docs/viewweb/reader/788f2c4aecfb4b698796aa3edfba?_w_filecode=f943458f18cb6cccccd7e54b07cd4136&_w_scene_id=4fa9f2d416f27387e7936ac6130f3c1a&_w_third_appid=AK20240531JLPFRR&_w_third_file_id=788f2c4aecfb4b698796aa3edfba&_w_demo_token=5c712c7747e2463bb8b31494ec0c"
            frameborder="0"
            width="100%"
            scrolling="no"
            style="min-height: 80vh; vertical-align: top"
        ></iframe> -->
        <!-- <object
            data="/docs/viewweb/reader/788f2c4aecfb4b698796aa3edfba?_w_filecode=f943458f18cb6cccccd7e54b07cd4136&_w_scene_id=4fa9f2d416f27387e7936ac6130f3c1a&_w_third_appid=AK20240531JLPFRR&_w_third_file_id=788f2c4aecfb4b698796aa3edfba&_w_demo_token=5c712c7747e2463bb8b31494ec0c"
            type="text/html"
            width="100%"
            height="800px"
        ></object> -->
    </div>
</template>

<script>
import cookie from "js-cookie";
import axios from "axios";
export default {
    data() {
        return {};
    },
    created() {
        cookie.set("wpsqing_autoLoginV1", "1");
        cookie.set("weboffice_device_id", "74383ff70d5045f758d50a8b0b2dc832");
        cookie.set("csrf", "E38yCN5Rpw8QEjDnwMxcZYyDysGNMQ82");
        cookie.set("lang", "zh-CN");
        axios({
            method: "get",
            url: "/docs/viewweb/reader/788f2c4aecfb4b698796aa3edfba?_w_filecode=f943458f18cb6cccccd7e54b07cd4136&_w_scene_id=4fa9f2d416f27387e7936ac6130f3c1a&_w_third_appid=AK20240531JLPFRR&_w_third_file_id=788f2c4aecfb4b698796aa3edfba&_w_demo_token=5c712c7747e2463bb8b31494ec0c"
        });
    },
    methods: {}
};
</script>

<style scoped>
</style>
