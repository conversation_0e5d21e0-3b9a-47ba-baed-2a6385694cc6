<template>
    <!-- TODO -->
    <div class="dashboard">
        <el-select v-model="value" placeholder="请选择" @change="changeTheme">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="dashboard-item col-7">
            <div class="demo-dashboard-box">
                控制台界面布局1
            </div>
        </div>
        <div class="dashboard-item col-3">
            <div class="demo-dashboard-box">
                控制台界面布局2
            </div>
        </div>
        <div style="width: 35%" class="dashboard-item">
            <div class="demo-dashboard-box">
                控制台界面布局3
            </div>
        </div>
        <div style="width: 35%" class="dashboard-item">
            <div class="demo-dashboard-box">
                控制台界面布局4
            </div>
        </div>
        <div style="width: 30%" class="dashboard-item">
            <div class="demo-dashboard-box">
                控制台界面布局5
            </div>
        </div>
        <div style="width: 50%" class="dashboard-item">
            <div class="demo-dashboard-box">
                控制台界面布局6
            </div>
        </div>
        <div style="width: 50%" class="dashboard-item">
            <div class="demo-dashboard-box">
                控制台界面布局7
            </div>
        </div>
    </div>
</template>
<script>
export default {
    components: {},
    data() {
        return {
            options: [
                {
                    value: "culturegSkinColour",
                    label: "文化自信"
                },
                {
                    value: "healthEyeCareSkinColour",
                    label: "护眼模式"
                },
                {
                    value: "springSkinColour",
                    label: "春意盎然"
                },
                {
                    value: "summerSkinColour",
                    label: "盛夏热情"
                },
                {
                    value: "autumnSkinColour",
                    label: "秋日硕果"
                },
                {
                    value: "winterSkinColour",
                    label: "冬日暖阳"
                }
            ],
            value: ""
        };
    },
    created() {
        window.addEventListener("storage", (e) => {
            console.log(e);
        });
    },
    methods: {
        changeTheme(CurTheme) {
            localStorage.setItem("item", CurTheme);
            this.changeThemeFromMenhu(CurTheme);
        },
        changeThemeFromMenhu() {
            let themeMapToMenHu = {
                culturegSkinColour: "",
                healthEyeCareSkinColour: "eye-care",
                springSkinColour: "spring",
                summerSkinColour: "summer",
                autumnSkinColour: "autumn",
                winterSkinColour: "winter"
            };
            menhuCurTheme = localStorage.getItem("globalColor");
            let selectedTheme = themeMapToMenHu[menhuCurTheme];
            let prefix = "static/mutiTheme";
            let linkId = "theme_link";
            const dom = document.getElementById(linkId);
            if (dom) {
                if (prefix.startsWith("http")) {
                    dom.href = `${prefix}/index.${selectedTheme}.min.css`;
                } else {
                    dom.href = (dom.dataset.base || "/") + `${prefix}/index.${selectedTheme}.min.css`;
                }
            }
        }
    }
};
</script>
<style lang="scss">
.dashboard {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    // margin: 0 -8px;
    .dashboard-item {
        padding: 0 8px 16px;
        &.col-1 {
            width: 10%;
        }
        &.col-2 {
            width: 20%;
        }
        &.col-3 {
            width: 30%;
        }
        .col-4 {
            width: 40%;
        }
        .col-5 {
            width: 50%;
        }
        &.col-6 {
            width: 60%;
        }
        &.col-7 {
            width: 70%;
        }
        &.col-8 {
            width: 80%;
        }
        &.col-9 {
            width: 90%;
        }
        &.col-10 {
            width: 100%;
        }
    }
}
.demo-dashboard-box {
    height: 300px;
    width: 100%;
    background: cornflowerblue;
    font-size: 30px;
    color: white;
    line-height: 300px;
    text-align: center;
}
//
</style>
