<template>
    <div class="picc-login">
        <el-input v-model="user" size="large" type="text" prefix-icon="el-icon-user" />
        <span class="h24"></span>
        <el-input v-model="pwd" size="large" type="password" prefix-icon="el-icon-key" />
        <el-button value="sdfsdf" type="primary" size="large" class="login-btn" @click="doLogin">
            <!-- can be remove Start -->
            {{ $t("LOGIN") }}
            <!-- can be remove End -->
        </el-button>
        <lang-select />
        <verifition ref="verifitionclick" mode="pop" captcha-type="clickWord" :img-size="{ width: '320px', height: '140px' }" @success="success" />
    </div>
</template>
<script>
import { setStorage } from "@/utils/storage";
import { doLogin } from "@/api/mock";
import { setToken } from "@/utils/auth";
import LangSelect from "@/components/LangSelect";
import { getGrayInfo } from "@/api/grayVersion";
import { setCookie } from "@/utils/cookie";
import { apicaptchaIsLogin, apicaptchaIsTimeOut, checkResultApi } from "@/api/captcha";
// ----------  can be removed End ---------
export default {
    components: {
        // ----------  can be removed Start -------
        LangSelect
        // ----------  can be removed End -------
    },
    data() {
        return {
            // ---------- can be removed Start -------
            user: "",
            pwd: "",
            // ---------- can be removed End ---------
            redirect: undefined,
            otherQuery: {},
            grayInfo: {}
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                const query = route.query;
                if (query) {
                    this.redirect = query.redirect;
                    this.otherQuery = this.getOtherQuery(query);
                }
            },
            immediate: true
        }
    },
    created() {
        this.initGrayInfo();
    },
    methods: {
        initGrayInfo() {
            getGrayInfo("30350")
                .then((data) => {
                    // gray_tab:"30350" gray_type:"comcode" gray_version:"330"
                    this.grayInfo = data;
                })
                .catch((err) => {
                    if (err) {
                        console.error("灰度接口报错");
                    }
                });
        },
        getOtherQuery(query) {
            return Object.keys(query).reduce((acc, cur) => {
                if (cur !== "redirect") {
                    acc[cur] = query[cur];
                }
                return acc;
            }, {});
        },
        updateCookie() {
            // 设置灰度版本到cookie
            const cookieKey = "gray_version";
            const cookieVal = this.grayInfo.gray_version || null;
            setCookie(cookieKey, cookieVal, 365);
        },
        doLogin() {
            // TODO
            /**
             * @description There should be a function to bind login button;
             *                   Login api callback should save the data to local and vuex.
             */
            // ---------- can be removed Start, it's only the demo for development -------
            doLogin().then((data) => {
                // TODO
                setStorage("userInfo", { name: "wwr", age: 12 });
                setStorage("token", { name: "wwr", age: 12 });
                setStorage("other-token", "123456", "cookie");
                // 更新cookie
                this.updateCookie();

                //
                setToken("123456789");
                this.$store.dispatch("user/getUserInfo", data).then((data) => {
                    // console.log(this.$route);
                    // this.$router.push('/dashboard');
                    this.$router.push({ path: this.redirect || "/", query: this.otherQuery });
                    this.apicaptchaIsLoginFunc();
                });
            });
        },
        apicaptchaIsLoginFunc() {
            apicaptchaIsLogin().then((data) => {
                if (data.status === "1") {
                    this.apicaptchaTimeoutFunc();
                }
            });
        },
        apicaptchaTimeoutFunc() {
            let apicaptchaInerval = setInterval(() => {
                apicaptchaIsTimeOut(1200).then((data) => {
                    if (data.data === 1) {
                        this.useverifitionclick();
                    } else if (data.data === 0) {
                        clearInterval(apicaptchaInerval);
                        //退出登录
                    }
                });
                // }, 1200000);
            }, 10000);
        },
        checkResult(uuid, resultKey) {
            checkResultApi({ uuid: uuid, captchaCode: this[resultKey + "_result"] }).then((res) => {
                this[resultKey + "_flag"] = true;
                this[resultKey + "_check_result"] = res.data.data;
            });
        },
        success(params) {
            // params 返回的二次验证参数, 和登录参数一起回传给登录接口，方便后台进行二次验证
        },
        useverifition() {
            this.$refs.verifition.show();
        },
        useverifitionclick() {
            this.$refs.verifitionclick.show();
        }
    }
};
</script>
<style lang="scss">
.picc-login {
    width: 480px;
    height: 330px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    border: 1px solid #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.5);
    padding: 36px 56px 48px;
    .el-input__inner {
        height: 54px;
        line-height: 54px;
    }
    .el-input__icon {
        font-size: 20px;
        line-height: 54px;
    }
    .h24 {
        display: block;
        height: 24px;
    }
    .login-btn {
        height: 54px !important;
        margin-top: 24px;
        width: 100%;
        height: 54px;
        font-size: 20px;
        font-family: Microsoft Yahei;
    }
}
</style>
