<template>
    <div>
        <el-row>
            <el-col :span="12">
                <div class="show-region-one">
                    <List-info title="任务指标">
                        <el-dropdown slot="header-button" @command="handleCommand">
                            <el-button type="text">
                                {{ searchDate }}<i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="当日">
                                    当日
                                </el-dropdown-item>
                                <el-dropdown-item command="前一天">
                                    前一天
                                </el-dropdown-item>
                                <el-dropdown-item command="后一天">
                                    后一天
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <div slot="list-info-content">
                            <div class="item warn">
                                <div class="label">
                                    超时待办
                                </div>
                                <div class="number">
                                    4
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    车险出单量
                                </div>
                                <div class="number">
                                    19
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    非车险出单量
                                </div>
                                <div class="number">
                                    21
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    核保退回
                                </div>
                                <div class="number">
                                    9
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    车险出单时长
                                </div>
                                <div class="number">
                                    120
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    非车险出单时长
                                </div>
                                <div class="number">
                                    240
                                </div>
                            </div>
                        </div>
                    </List-info>
                </div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <div class="show-region-two">
                    <List-info title="保单信息" size="large">
                        <div slot="header-button" class="button-link">
                            <el-button type="text">
                                资料收集
                            </el-button>
                            <el-button type="text">
                                详情
                            </el-button>
                        </div>
                        <div slot="list-info-content">
                            <div class="item">
                                <div class="label">
                                    客户信息
                                </div>
                                <div class="value">
                                    德邦物流<el-rate v-model="value" disabled :colors="colors" score-template="{value}" :max="4" />
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    保单号
                                </div>
                                <div class="value figure">
                                    PYEJ20193200AC00013511
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    被保险人
                                </div>
                                <div class="value">
                                    货运
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    理赔历史
                                </div>
                                <div class="value">
                                    <span class="figure">3<span style="font-size: 14px;">次</span></span>
                                    <span class="el-icon-tickets"></span>
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    承保时间
                                </div>
                                <div class="value figure">
                                    2019/04/17 <span style="font-size: 14px;">至</span> 2020/04/16
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    保险金额
                                </div>
                                <div class="value figure">
                                    19234.00 <span style="font-size: 14px;">元</span>
                                </div>
                            </div>
                        </div>
                    </List-info>
                </div>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <div class="show-region-one">
                    <List-info title="任务指标" size="middle">
                        <div slot="list-info-content">
                            <div class="item warn">
                                <div class="label">
                                    产品名称
                                </div>
                                <div class="value">
                                    金锁家财险
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    产品方案
                                </div>
                                <div class="value">
                                    家安险
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    保险期限方案
                                </div>
                                <div class="value figure">
                                    2019/03/12 <span style="font-size: 14px;">到</span> 2019/03/12
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    操作员
                                </div>
                                <div class="value">
                                    刘冰凌
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    出单机构
                                </div>
                                <div class="value figure">
                                    11019300 <span style="font-size: 14px;">北京分公司营业部</span>
                                </div>
                            </div>
                            <div class="item">
                                <div class="label">
                                    签单日期
                                </div>
                                <div class="value figure">
                                    2019/09/10
                                </div>
                            </div>
                        </div>
                    </List-info>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
    name: "List",
    data() {
        return {
            value: 4,
            searchDate: "当日",
            colors:['#99A9BF', '#F7BA2A', '#FF6F3C']
        };
    },
    methods: {
        handleCommand(command) {
            this.searchDate = command;
        }
    }
};
</script>

<style scoped lang="scss">
.show-region-one,
.show-region-two {
    float: left;
}
.show-region-one {
    // height: 400px;
    // width: 460px;
    // background-color: #eee;
    // padding: 20px;

    .el-button {
        margin: 24px 0 0 8px;
        font-weight: bold;
        color: #292b34;
    }
    .picc-list-info-middle {
        width: auto;
    }
}
.show-region-two {
    width: 460px;
    margin: 20px 0;
    // background-color: #eee;
    .picc-list-info-large {
        // width: auto;
    }
    .button-link {
        float: right;
        margin: 18px 24px 0 0;

        button:last-child {
            margin-left: 16px;
        }
    }

    .el-icon-star-off {
        margin-right: 2px;
        &:last-child {
            margin-right: 0;
        }
        &:first-child {
            margin-left: 5px;
        }
    }

    .el-icon-tickets {
        // width: 20px;
        // padding: 3px 0;
        margin-left: 3px;
    }

    ::v-deep .el-rate {
        display: inline-block;
        position: relative;
        top: -3px;
        height: 16px;
        .el-rate__item {
            margin-right: 2px;
            height: 16px;
            width: 16px;
            &:first-child {
                margin-left: 5px;
            }

            .el-rate__icon {
                height: 16px;
            }
        }
    }
}
</style>
