<template>
    <div>
        <el-row>
            <el-col :span="18" :offset="3">
                <el-divider content-position="left">
                    反馈提示
                </el-divider>
                <el-button round plain type="primary" @click="open1">
                    成功反馈
                </el-button>
                <el-button round plain type="primary" @click="open2">
                    失败反馈
                </el-button>
                <el-button round plain type="primary" @click="open3">
                    警告反馈
                </el-button>
                <el-button round plain type="primary" @click="open4">
                    多行反馈
                </el-button>
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
    name:"Feedback",
    methods: {
        open1() {
            this.$message({
                message: "这是一条消息提示", // 多行的可以直接在这边写，长度看具体要求
                type: "success"
                // duration: 0  // 显示时间, 毫秒。设为 0 则不会自动关闭
            });
        },
        open2() {
            this.$message({
                message: "这是一条消息提示这是一条消息提示这是一条消息提示这是一条消息提示这是一条消息提示这是一条消息提示这是一条消息提示这是一条消息提示", // 多行的可以直接在这边写，长度看具体要求
                type: "error",
                //duration: 0  // 显示时间, 毫秒。设为 0 则不会自动关闭
            });
        },
        open3() {
            this.$message({
                message: "这是一条消息提示", // 多行的可以直接在这边写，长度看具体要求
                type: "warning",
                //duration: 0 // 显示时间, 毫秒。设为 0 则不会自动关闭
            });
        },
        open4() {
            this.$message({
                message: "这是一条消息提示这是一条消息提示这是一条消息提示这是一条消息提示这是一条消息提示", // 多行的可以直接在这边写，长度看具体要求
                type: "success"
                // duration: 0  // 显示时间, 毫秒。设为 0 则不会自动关闭
            });
        }
    }
};
</script>
