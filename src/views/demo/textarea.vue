<template>
    <div style="padding:30px;">
        <h1 style="color:red;font-size:16px;">
            纯文本输入框：1）带滑杆型输入框 2）全展示型输入框 3）限制字数型输入框
        </h1>
        <el-input
            v-model="formBox.textScroll"
            type="textarea"
            class="picc-textarea-puretext picc-textarea-puretext--slidebar picc-textarea-error--hover"
            resize="none"
            :autosize="{ minRows: 1, maxRows: 2 }"
            placeholder="带滑杆型输入框"
            @focus="inputFocus($event, 'textScroll')"
            @blur="inputBlur($event, 'textScroll')"
        />
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.textScroll1"
            type="textarea"
            class="picc-textarea-puretext picc-textarea-puretext--slidebar"
            resize="none"
            disabled
            :autosize="{ minRows: 1, maxRows: 2 }"
            placeholder="带滑杆型输入框"
            @focus="inputFocus($event, 'textScroll1')"
            @blur="inputBlur($event, 'textScroll1')"
        />
        <div style="margin-bottom:30px"></div>

        <el-input
            v-model="formBox.type"
            type="textarea"
            resize="none"
            autosize
            placeholder="全展示型输入框"
            class="picc-textarea-puretext picc-textarea-puretext--autosize"
        />
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.type1"
            type="textarea"
            resize="none"
            autosize
            placeholder="全展示型输入框"
            disabled
            class="picc-textarea-puretext picc-textarea-puretext--autosize"
        />
        <div style="margin-bottom:30px"></div>

        <el-input
            v-model="formBox.number"
            type="textarea"
            resize="none"
            autosize
            maxlength="30"
            show-word-limit
            placeholder="限制字数型输入框"
            class="picc-textarea-puretext picc-textarea-puretext--autosize"
        />
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.number1"
            type="textarea"
            resize="none"
            autosize
            placeholder="限制字数型输入框"
            disabled
            class="picc-textarea-puretext picc-textarea-puretext--autosize"
        />
        <div style="margin-bottom:30px"></div>

        <!-- 以下先隐藏 -->
        <div class="picc-el-form" style="display:none">
            <el-form
                ref="dialogBox"
                :model="formBox"
                label-width="120px"
                :rules="rules"
            >
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="scrollCode"
                            label="带滑杆型输入框"
                            class="picc-form-label-linefeed picc-linefeed--short"
                        >
                            <el-input
                                v-model="formBox.scrollCode"
                                type="textarea"
                                resize="none"
                                :autosize="{ minRows: 1, maxRows: 2 }"
                                class="picc-textarea-puretext picc-textarea-puretext--slidebar"
                                @focus="inputFocus($event, 'scrollCode')"
                                @blur="inputBlur($event, 'scrollCode')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="typeCode"
                            label="全展示型输入框"
                            class="picc-form-label-linefeed picc-linefeed--short"
                        >
                            <el-input
                                v-model="formBox.typeCode"
                                type="textarea"
                                resize="none"
                                autosize
                                class="picc-textarea-puretext picc-textarea-puretext--autosize"
                                @focus="inputFocus($event, 'typeCode')"
                                @blur="inputBlur($event, 'typeCode')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            label="全展示型输入框"
                            class="picc-form-label-linefeed picc-linefeed--short"
                        >
                            <el-input
                                v-model="formBox.typeCode1"
                                type="textarea"
                                resize="none"
                                autosize
                                class="picc-textarea-puretext picc-textarea-puretext--autosize"
                                @focus="inputFocus($event, 'typeCode1')"
                                @blur="inputBlur($event, 'typeCode1')"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="scrollCode1"
                            label="带滑杆型输入框"
                            class="picc-form-label-linefeed picc-linefeed--short"
                        >
                            <el-input
                                v-model="formBox.scrollCode1"
                                type="textarea"
                                resize="none"
                                disabled
                                :autosize="{ minRows: 1, maxRows: 2 }"
                                class="picc-textarea-puretext picc-textarea-puretext--slidebar"
                                @focus="inputFocus($event, 'scrollCode1')"
                                @blur="inputBlur($event, 'scrollCode1')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="typeCode2"
                            label="全展示型输入框"
                            class="picc-form-label-linefeed picc-linefeed--short"
                        >
                            <el-input
                                v-model="formBox.typeCode2"
                                type="textarea"
                                autosize
                                disabled
                                resize="none"
                                class="picc-textarea-puretext picc-textarea-puretext--autosize"
                                @focus="inputFocus($event, 'typeCode2')"
                                @blur="inputBlur($event, 'typeCode2')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            label="带滑杆型输入框"
                            class="picc-form-label-linefeed picc-linefeed--short"
                        >
                            <el-input
                                v-model="formBox.scrollCode2"
                                type="textarea"
                                resize="none"
                                :autosize="{ minRows: 1, maxRows: 2 }"
                                class="picc-textarea-puretext picc-textarea-puretext--slidebar"
                                @focus="inputFocus($event, 'scrollCode2')"
                                @blur="inputBlur($event, 'scrollCode2')"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="24" style="text-align:center;">
                        <el-button @click="ddd">
                            点击校验表单
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </div>
</template>

<script>
export default {
    name: "PureTextInput",
    data() {
        return {
            formBox: {
                scrollCode: "",
                scrollCode1: "",
                scrollCode2: "",
                typeCode: "",
                typeCode1:
                    "填写内容文档填写内容文档填写内容文档填写内容文档填写内容文档 填写内容",
                typeCode2:
                    "填写内容文档填写内容文档填写内容文档填写内容文档填写内容文档 填写内容",

                textScroll: "",
                textScroll1: "",
                text: "",
                text1: "",
                number: "",
                number1: ""
            },
            rules: {
                scrollCode: [
                    { required: true, message: "请选择", trigger: "blur" }
                ],
                scrollCode1: [
                    { required: true, message: "请选择", trigger: "blur" }
                ],
                scrollCode2: [
                    { required: true, message: "请选择", trigger: "blur" }
                ],
                typeCode: [
                    {
                        required: true,
                        message: `气泡9 - 错误提示的文案示的文示的文示的文~~~`,
                        trigger: "blur"
                    },
                    {
                        min: 5,
                        message: `气泡9 - 错误提示的文案需要大于5个字`,
                        trigger: "blur"
                    },
                    {
                        max: 8,
                        message: `气泡9 - 错误提示的文案需要小于8个字`,
                        trigger: "blur"
                    }
                ],
                typeCode1: [
                    { required: true, message: "请选择", trigger: "blur" }
                ],
                typeCode2: [
                    { required: true, message: "请选择", trigger: "blur" }
                ]
            }
        };
    },
    methods: {
        ddd() {
            this.$refs.dialogBox.validate((valid) => {
                if (valid) {
                    alert("submit!");
                } else {
                    // console.log("error submit!!");
                    return false;
                }
            });
        },

        inputFocus: function(event, target) {
            if (event.target.type === "textarea") {
                event.target.parentNode.style.borderColor = "#234DCC";
            }
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        },
        inputBlur: function(event, target) {
            console.log(target);
            if (event.target.type === "textarea") {
                event.target.parentNode.style.borderColor = "#cdcdcd";
            }
        }
    }
};
</script>

<style scoped></style>
