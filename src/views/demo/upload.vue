<template>
    <div style="padding: 20px">
        <div>
            <div class="picc-container">
                <!-- 内页卡片内标题栏 -->
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        3-8-2 及时反馈：单个控件操作的结果反馈
                    </div>
                </div>
                <div class="picc-el-form">
                    <el-row type="flex" class="row-bg">
                        <el-col :span="6">
                            <div class="picc-form-tipmessage picc-form-tipmessage--info picc-form-tipmessage--textellipsis">
                                <i class="el-icon-warning-outline"></i>
                                <strong> 消息信息的文案 </strong>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row type="flex" class="row-bg">
                        <el-col :span="6">
                            <div
                                class="picc-form-tipmessage picc-form-tipmessage--success picc-form-tipmessage--textellipsis"
                            >
                                <i class="el-icon-success"></i>
                                <strong>
                                    已成功上传
                                    <em class="picc-tipMessage-success"> 10 </em>
                                    个标的信息
                                </strong>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row type="flex" class="row-bg">
                        <el-col :span="6">
                            <div
                                class="picc-form-tipmessage picc-form-tipmessage--error picc-form-tipmessage--textellipsis"
                            >
                                <i class="el-icon-warning-outline"></i>
                                <strong> 错误提示的文案 </strong>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="picc-el-form">
                    <el-row type="flex" class="row-bg">
                        <el-col :span="6">
                            <div class="picc-form-tipmessage picc-form-tipmessage--info picc-form-tipmessage--textellipsis">
                                <el-tooltip placement="right" class="">
                                    <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip_info">
                                        <p>消息信息的文案消息信息的文案消息信息的文案消息信息的文案</p>
                                    </div>
                                    <i class="el-icon-warning-outline"></i>
                                </el-tooltip>
                                <strong>气泡 - 消息信息的文案</strong>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row type="flex" class="row-bg">
                        <el-col :span="6">
                            <div
                                class="picc-form-tipmessage picc-form-tipmessage--success picc-form-tipmessage--textellipsis"
                            >
                                <el-tooltip placement="right" class="">
                                    <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip_success">
                                        <p>消息信息的文案消息信息的文案消息信息的文案消息信息的文案</p>
                                    </div>
                                    <i class="el-icon-success"></i>
                                </el-tooltip>
                                <strong>
                                    气泡 - 已成功上传
                                    <em class="picc-tipMessage-success"> 10 </em>
                                    个标的信息
                                </strong>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row type="flex" class="row-bg">
                        <el-col :span="6">
                            <div
                                class="picc-form-tipmessage picc-form-tipmessage--error picc-form-tipmessage--textellipsis"
                            >
                                <el-tooltip placement="right" class="">
                                    <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip_error">
                                        <p>消息信息的文案消息信息的文案消息信息的文案消息信息的文案</p>
                                    </div>
                                    <i class="picc-icon picc-icon-warning-mini-red"></i>
                                </el-tooltip>
                                <strong>气泡 - 错误提示的文案</strong>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </div>
        <div style="margin: 30px"></div>
        <!-- form 表单 -->
        <div>
            <div class="picc-container">
                <!-- 内页卡片内标题栏 -->
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        标的信息01
                    </div>
                    <div class="picc-operation-area">
                        <span class="picc-operation-superior">
                            <i class="el-icon-delete-solid"></i>
                        </span>
                    </div>
                </div>
                <div class="picc-el-form">
                    <el-form ref="formBox" :model="formBox" label-width="120px" :rules="rules">
                        <el-row v-piccLoading:progress="{ show: tuLogo1, percentage: percentage1 }" class="row-bg">
                            <el-col :span="24">
                                <el-form-item v-picc-input-error prop="typeCode" label="标题" class="picc-upload-form-item">
                                    <el-upload
                                        ref="uploadBatch" class="picc-upload--batch"
                                        action="https://jsonplaceholder.typicode.com/posts/" multiple
                                        :show-file-list="false"
                                        :on-progress="(oldval, newval, newval2) => batchProgress(oldval, newval, newval2, 'picc-upload--batch')"
                                        :before-upload="batchBeforeUpload"
                                        :on-success="(oldval, newval, newval2) => batchSuccess(oldval, newval, newval2, 'picc-upload--batch')"
                                        :on-error="(err, file, fileList) => batchError(err, file, fileList, 'picc-upload--batch')"
                                        :on-change="handleChange1"
                                    >
                                        <el-button
                                            type="primary" icon="picc-icon picc-icon-upload-o" class="flex" round
                                            plain
                                        >
                                            批量上传
                                        </el-button>

                                        <div v-if="uploadType == 0" class="picc-form-tipmessage picc-form-tipmessage--info">
                                            <i class="el-icon-warning-outline"></i>
                                            <strong>
                                                已上传
                                                <em class="picc-tipMessage-info">
                                                    {{ uploadBatchSuccess }}
                                                </em>
                                                个附件信息
                                            </strong>
                                        </div>
                                        <div
                                            v-if="uploadType == 1"
                                            class="picc-form-tipmessage picc-form-tipmessage--success"
                                        >
                                            <i class="el-icon-success"></i>
                                            <strong>
                                                已成功上传
                                                <em class="picc-tipMessage-success">
                                                    {{ uploadBatchSuccess }}
                                                </em>
                                                个附件信息
                                            </strong>
                                        </div>
                                        <div
                                            v-if="uploadType == 2"
                                            class="picc-form-tipmessage picc-form-tipmessage--error"
                                        >
                                            <i class="picc-icon picc-icon-warning-mini-red"></i>
                                            <strong>
                                                <em class="picc-tipMessage-error">
                                                    已成功上传
                                                    <em class="picc-tipMessage-success">
                                                        {{ uploadBatchSuccess }}
                                                    </em>
                                                    个附件信息，
                                                </em>
                                                {{ uploadBatchError }}个附件信息错误，
                                                <em class="picc-tipMessage-error"> 请 </em>
                                                <el-button
                                                    type="text" class="picc-button--icon flex"
                                                    style="display: inline-block"
                                                > 下载修改 </el-button>
                                            </strong>
                                        </div>
                                    </el-upload>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-if="annex2.length > 0" type="flex" class="row-bg">
                            <el-col :span="24">
                                <el-form-item class="picc-form-item--cancel">
                                    <div
                                        v-for="(item, index) in annex2" :key="index"
                                        class="picc-form-tipmessage picc-form-tipmessage--success"
                                    >
                                        <i v-if="item.status === 'success'" class="el-icon-success"></i>
                                        <i v-else class="picc-icon picc-icon-warning-mini-red"></i>
                                        <strong>
                                            {{ item.name }}
                                        </strong>
                                        <i class="el-icon-error"></i>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <div style="margin: 30px"></div>
        <div>
            <div class="picc-container">
                <!-- 内页卡片内标题栏 -->
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        标的信息02
                    </div>
                    <div class="picc-operation-area">
                        <span class="picc-operation-superior">
                            <i class="el-icon-delete-solid"></i>
                        </span>
                    </div>
                </div>
                <div class="picc-el-form">
                    <el-form :model="formBox" label-width="120px">
                        <el-row v-piccLoading:progress="{ show: tuLogo2, percentage: percentage2 }" class="row-bg">
                            <el-col :span="24">
                                <el-form-item v-picc-input-error prop="typeCode1" label="标题" class="picc-upload-form-item">
                                    <el-upload
                                        ref="uploadBatch" class="picc-upload--batch"
                                        action="https://jsonplaceholder.typicode.com/posts/" multiple
                                        :on-progress="(oldval, newval, newval2) => batchProgress(oldval, newval, newval2, 'picc-upload--batch')"
                                        :before-upload="batchBeforeUpload"
                                        :on-success="(oldval, newval, newval2) => batchSuccess(oldval, newval, newval2, 'picc-upload--batch')"
                                        :on-error="(err, file, fileList) => batchError(err, file, fileList, 'picc-upload--batch')"
                                        :on-change="handleChange2"
                                    >
                                        <el-button
                                            type="primary" icon="picc-icon picc-icon-upload-o" class="flex" round
                                            plain
                                        >
                                            批量上传
                                        </el-button>
                                    </el-upload>
                                    <div class="picc-form-tipmessage picc-form-tipmessage--success">
                                        <i class="el-icon-success"></i>
                                        <strong>
                                            已成功上传
                                            <em class="picc-tipMessage-success">
                                                {{ uploadBatchSuccess }}
                                            </em>
                                            个附件信息
                                        </strong>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex" class="row-bg">
                            <el-col :span="24">
                                <el-form-item class="picc-form-item--cancel">
                                    <div
                                        v-for="(item, index) in annex" :key="index"
                                        class="picc-form-tipmessage picc-form-tipmessage--success"
                                    >
                                        <i v-if="item.success" class="el-icon-success"></i>
                                        <i v-else class="picc-icon picc-icon-warning-mini-red"></i>
                                        <strong>
                                            {{ item.title }}
                                        </strong>
                                        <i class="el-icon-error"></i>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <div style="margin: 30px"></div>
        <div>
            <div class="picc-container">
                <!-- 内页卡片内标题栏 -->
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        标的信息03
                    </div>
                    <div class="picc-operation-area">
                        <span class="picc-operation-superior">
                            <i class="el-icon-delete-solid"></i>
                        </span>
                    </div>
                </div>
                <div class="picc-el-form">
                    <el-form :model="formBox" label-width="120px">
                        <el-row
                            v-piccLoading:progress="{ show: tuLogo3, percentage: percentage3 }" type="flex"
                            class="row-bg"
                        >
                            <el-col :span="24">
                                <el-form-item v-picc-input-error prop="typeCode" label="标题" class="picc-upload-form-item">
                                    <el-upload
                                        ref="uploadBatch" class="picc-upload--batch"
                                        action="https://jsonplaceholder.typicode.com/posts/" multiple
                                        :on-progress="(oldval, newval, newval2) => batchProgress(oldval, newval, newval2, 'picc-upload--batch')"
                                        :before-upload="batchBeforeUpload"
                                        :on-success="(oldval, newval, newval2) => batchSuccess(oldval, newval, newval2, 'picc-upload--batch')"
                                        :on-error="(err, file, fileList) => batchError(err, file, fileList, 'picc-upload--batch')"
                                        :on-change="handleChange3"
                                    >
                                        <el-button
                                            type="primary" icon="picc-icon picc-icon-upload-o" class="flex" round
                                            plain
                                        >
                                            批量上传
                                        </el-button>
                                    </el-upload>
                                    <div class="picc-form-tipmessage picc-form-tipmessage--error">
                                        <i class="picc-icon picc-icon-warning-mini-red"></i>
                                        <strong>
                                            <em class="picc-tipMessage-error">
                                                已成功上传
                                                <em class="picc-tipMessage-success">
                                                    {{ uploadBatchSuccess }}
                                                </em>
                                                个标的信息，
                                            </em>
                                            {{ uploadBatchError }}个标的信息错误，
                                            <em class="picc-tipMessage-error"> 请 </em>
                                            <el-button type="text" class="picc-button--icon"> 下载修改 </el-button>
                                        </strong>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex" class="row-bg">
                            <el-col :span="24">
                                <el-form-item class="picc-form-item--cancel">
                                    <div
                                        v-for="(item, index) in annex1" :key="index"
                                        class="picc-form-tipmessage picc-form-tipmessage--success"
                                    >
                                        <i v-if="item.success" class="el-icon-success"></i>
                                        <i v-else class="picc-icon picc-icon-warning-mini-red"></i>
                                        <strong>
                                            {{ item.title }}
                                        </strong>
                                        <i class="el-icon-error"></i>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <div style="margin: 30px"></div>
        <el-upload
            v-loading="loading" class="picc-upload picc-upload-IDcard" action="/" :class="{ cur: imageUrlNegative }"
            list-type="picture-card" :auto-upload="false" :show-file-list="false" :on-change="onchangeNegative"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.5)"
        >
            <div v-if="imageUrlNegative" class="img" @mouseover="enterNegative()" @mouseout="leaveNegative()">
                <i class="picc-icon picc-icon-success-sm"></i>
                <img :src="imageUrlNegative" class="avatar" />
            </div>

            <i class="el-icon-upload"><span>点击上传</span></i>
        </el-upload>
        <div style="margin: 30px"></div>
        <el-upload
            v-loading="loading" class="picc-upload picc-upload-IDcard--back" action="/"
            :class="{ cur: imageUrlNegative }" list-type="picture-card" :auto-upload="false" :show-file-list="false"
            :on-change="onchangeNegative" element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.5)"
        >
            <div v-if="imageUrlNegative" class="img" @mouseover="enterNegative()" @mouseout="leaveNegative()">
                <i class="picc-icon picc-icon-success-sm"></i>
                <img :src="imageUrlNegative" class="avatar" />
            </div>

            <i class="el-icon-upload"><span>点击上传</span></i>
        </el-upload>
        <div style="margin: 30px"></div>
        <el-upload
            v-loading="loading" class="picc-upload picc-upload-video" action="/"
            :class="{ cur: imageUrlNegativeVideo }" list-type="picture-card" :auto-upload="false" :show-file-list="false"
            :on-change="onchangeNegativeVideo" element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.5)"
        >
            <div v-if="imageUrlNegativeVideo" class="img" @mouseover="enterNegative()" @mouseout="leaveNegative()">
                <i class="picc-icon picc-icon-success-sm"></i>
                <dev :src="imageUrlNegativeVideo" class="avatar" />
            </div>

            <i class="el-icon-upload"><span>点击上传</span></i>
        </el-upload>
        <div style="margin: 30px"></div>
        <el-upload
            v-loading="loading" class="picc-upload picc-upload-word" action="/"
            :class="{ cur: imageUrlNegativeWord }" list-type="picture-card" :auto-upload="false" :show-file-list="false"
            :on-change="onchangeNegativeWord" element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.5)"
        >
            <div v-if="imageUrlNegativeWord" class="img" @mouseover="enterNegative()" @mouseout="leaveNegative()">
                <i class="picc-icon picc-icon-success-sm"></i>
                <!-- <img :src="imageUrlNegative" class="avatar" /> -->
            </div>

            <i class="el-icon-upload"><span>点击上传</span></i>
        </el-upload>
        <div style="margin: 30px"></div>

        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Upload",
    data() {
        return {
            // 初始0 成功1 失败2
            uploadType: 0,
            uploadBatchSuccess: 4,
            uploadBatchError: 2,
            formBox: {
                typeCode: "",
                typeCode1: "",
                /* 上传 */
                dialogImageUrl: "",
                dialogVisible: false
            },
            rules: {
                typeCode: [
                    {
                        required: true,
                        message: `气泡 - 错误提示的文案错误提示的文案错误提示的文案错误提示的文案误提示的文案错误提示的文案错误提示的文案错误提示的文案`,
                        trigger: "blur"
                    },
                    {
                        min: 5,
                        message: `气泡 - 错误提示的文案需要大于5个字`,
                        trigger: "blur"
                    },
                    {
                        max: 8,
                        message: `气泡 - 错误提示的文案需要小于8个字`,
                        trigger: "blur"
                    }
                ]
            },
            dialogImageUrl: "",
            dialogVisible: false,
            successNum: 0,
            errorNum: 0,
            // 上传图片路径
            imageUrlNegative: "",
            imageUrlNegativeVideo: "",
            imageUrlNegativeWord: "",
            loading: false,

            // curPercentage: 0,
            tuLogo1: false,
            tuLogo2: false,
            tuLogo3: false,
            percentage1: 0,
            percentage2: 0,
            percentage3: 0,

            annex: [{ title: '信息技术部外部驻场地人员申请表-王萌.doc', success: true },
                { title: '附件文件标题全称1.doc', success: true },
                { title: '附件文件标题全称2.doc', success: true },
                { title: '附件文件标题全称3.doc', success: true },
            ],
            annex1: [{ title: '信息技术部外部驻场地人员申请表-王萌.doc', success: true },
                { title: '附件文件标题全称1.doc', success: true },
                { title: '附件文件标题全称2.doc', success: true },
                { title: '附件文件标题全称3.doc', success: true },
                { title: '信息技术部外部驻场地人员申请表-王萌.doc', success: false },
                { title: '附件文件标题全称1.doc', success: false },
            ],
            annex2: [],

        };
    },
    // mounted() {
    //     this.increace();
    // },
    methods: {
        // showLoading(type = null) {
        //     this.loadingInstance = this.$piccLoading({
        //         type: type,
        //         text: "正在加载…",
        //         percentage: 0
        //     });
        //     const timer = setInterval(() => {
        //         this.loadingInstance.percentage < 100 ? (this.loadingInstance.percentage += 20) : clearInterval(timer);
        //     }, 200);
        // },
        // increace() {
        //     const timer = setInterval(() => {
        //         this.curPercentage < 100 ? (this.curPercentage += 5) : clearInterval(timer);
        //     }, 200);
        // },

        /* -------------------------------------------------------- 批量上传 --------------------------------------------------------  */
        batchProgress(event, file, fileList, target) {
            // 需添加全局加载
            // console.log("batchProgress------------文件上传时的钩子");
            // console.log(event);
            // console.log(file);
            // console.log(fileList);
            // console.log(target);
            // this.tuLogo = true;
        },
        handleChange1(file, fileList) {
            if (file.status === "ready") {
                this.percentage1 = 0;
                this.tuLogo1 = true;
                const interval = setInterval(() => {
                    if (this.percentage1 >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            this.tuLogo1 = false;
                            this.percentage1 = 0;
                        }, 200);
                        return;
                    }
                    this.percentage1 += 1;
                }, 20);
            }
            if (file.status === "success") {
                this.percentage1 = 100;
                this.tuLogo1 = false;
            }
        },
        handleChange2(file, fileList) {
            if (file.status === "ready") {
                this.percentage2 = 0;
                this.tuLogo2 = true;
                const interval = setInterval(() => {
                    if (this.percentage2 >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            this.tuLogo2 = false;
                            this.percentage2 = 0;
                        }, 200);
                        return;
                    }
                    this.percentage2 += 1;
                }, 20);
            }
            if (file.status === "success") {
                this.percentage2 = 100;
                this.tuLogo2 = false;
            }
        },
        handleChange3(file, fileList) {
            if (file.status === "ready") {
                this.percentage3 = 0;
                this.tuLogo3 = true;
                const interval = setInterval(() => {
                    if (this.percentage3 >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            this.tuLogo3 = false;
                            this.percentage3 = 0;
                        }, 200);
                        return;
                    }
                    this.percentage3 += 1;
                }, 20);
            }
            if (file.status === "success") {
                this.percentage3 = 100;
                this.tuLogo3 = false;
            }
        },
        batchBeforeUpload(file) {
            // console.log(
            //     "batchBeforeUpload------------上传文件之前的钩子，参数为上传的文件"
            // );
            // console.log(file);
            // this.showLoading('line')
            // console.log('2222')
        },
        batchError: function (err, file, fileList, target) {
            // console.log("batchError------------文件上传失败时的钩子");
            // console.log(err);

            // console.log(file);
            // console.log(fileList);
            // console.log(target);

            // this.tuLogo = false;

            // 隐藏进度条
            this.annex2.push(file);
            this.uploadBatchSuccess = 4;
            this.uploadBatchError = 2;
            this.uploadType = 2;
            this.$confirm("请检查文件是否正确后，重新上传文件。", "文件上传失败！", {
                confirmButtonText: "再试一次",
                cancelButtonText: "取消",
                roundButton: true,
                type: "error",
                customClass: "picc-message-box",
                center: true,
                showClose: false
            })
                .then(() => {
                    this.$message({
                        type: "success",
                        message: "重新上传",
                        offset: 20
                    });

                    this.$refs.uploadBatch.clearFiles();
                    const fileList = file;
                    this.$refs.uploadBatch.submit(fileList);
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "取消重新上传",
                        offset: 20
                    });
                });
            // throw new Error(err);
        },
        batchSuccess: function (response, file, fileList, target) {
            // console.log("batchSuccess------------文件上传成功时的钩子");
            // console.log(response);
            // console.log(file);
            // console.log(fileList);
            // console.log(target);
            // 隐藏进度条/蒙版

            this.annex2.push(file);
            this.uploadBatchSuccess = 10;
            this.uploadType = 1;
            this.$message({
                message: "批量上传成功，请前往“标的信息”模块，查看上传结果",
                type: "success",
                offset: 20
            });
        },

        /* -------------------------------------------------------- 单独上传 --------------------------------------------------------  */
        // handleSuccess: function (response, file, fileList, target) {
        //     // 将后台提供的封面替换掉上传的背景图
        //     const loadingBox = document.getElementsByClassName(target)[0];
        //     const $target = loadingBox.querySelectorAll(".el-upload-list__item");
        //     for (let i = 0; i < $target.length; i++) {
        //         if ($target[i].getAttribute("uid") === file.uid) {
        //             // console.log($target[i]);
        //             // 自定义
        //         }
        //     }
        // },
        // focusFN: function (target) {
        //     // this.rules[target][0].required = false
        //     // 部分表单校验
        //     this.$refs.formBox.clearValidate(target);
        // },
        // handleRemove(file, fileList) {
        //     // console.log(file, fileList);
        // },
        // handlePictureCardPreview(file) {
        //     this.dialogImageUrl = file.url;
        //     this.dialogVisible = true;
        // },
        // beforeAvatarUploadWord(file) {
        //     const isJPG = file.type.split("/")[0] === "application";
        //     // console.log(file.type.split("/"));
        //     const isLt2M = file.size / 1024 / 1024 < 100;

        //     if (!isJPG) {
        //         this.$message.error("上传文档只能是 application/vnd.* 格式!");
        //     } else if (!isLt2M) {
        //         this.$message.error("上传文档大小不能超过 100MB!");
        //     }
        //     return isJPG && isLt2M;
        // },
        // beforeAvatarUploadVideo(file) {
        //     const isJPG = file.type.split("/")[0] === "video";
        //     // console.log(file.type.split("/"));
        //     const isLt2M = file.size / 1024 / 1024 < 100;

        //     if (!isJPG) {
        //         this.$message.error("上传影像只能是 video/* 格式!");
        //     } else if (!isLt2M) {
        //         this.$message.error("上传影像大小不能超过 100MB!");
        //     }
        //     return isJPG && isLt2M;
        // },
        // beforeAvatarUpload(file) {
        //     const isJPG = file.type.split("/")[0] === "image";
        //     // console.log(file.type.split("/"));
        //     const isLt2M = file.size / 1024 / 1024 < 2;

        //     if (!isJPG) {
        //         this.$message.error("上传IDCard图片只能是 image/* 格式!");
        //     } else if (!isLt2M) {
        //         this.$message.error("上传IDCard图片大小不能超过 2MB!");
        //     }
        //     return isJPG && isLt2M;
        // },
        // 单个 - 文件上传时的钩子
        // handlePictureCardProgress(event, file, fileList, target) {
        //     const _type = file.raw.type.split("/")[0];
        //     const loadingBox = document.getElementsByClassName(target)[0];
        //     const lengths = loadingBox.querySelectorAll(".el-upload-list__item").length;
        //     const isUploading = loadingBox.getElementsByClassName("el-upload-list__item")[lengths - 1];
        //     // console.log(file)
        //     isUploading.setAttribute("uid", file.uid);
        //     // console.log(isUploading)
        //     if (isUploading) {
        //         // console.log(file.url)
        //         if (_type === "image") {
        //             isUploading.style.background = "url(" + file.url + ") no-repeat center";
        //             isUploading.style.backgroundSize = "100% 100%";
        //         } else if (_type === "video") {
        //             // isUploading.style.background = 'url(' + file.url + ') no-repeat center'
        //         } else if (_type === "application") {
        //             // isUploading.style.background = 'url(' + file.url + ') no-repeat center'x
        //         }
        //         const icons = '<i class="el-icon-loading"></i>';
        //         const textIcon = isUploading.getElementsByClassName("el-progress__text")[0];
        //         if (textIcon) {
        //             textIcon.innerHTML = icons;
        //             textIcon.style.color = "#292b34";
        //         }
        //     }
        // },
        // 批量上传 请求
        // getData() {
        //     const formData = new FormData();
        //     formData.append("filePic", this.filePic);
        //     formData.append("fileDoc", this.fileDoc);
        //     // axios({
        //     //     method: 'post',
        //     //     data: formData,
        //     //     headers: {
        //     //         'Content-Type': 'application/json;'
        //     //     },
        //     //     url: '/upload/images'
        //     // }).then(() => {
        //     //     this.$refs.upload.clearFiles();
        //     //     this.fileDoc = {};
        //     //     this.filePic = '';
        //     //     this.successNum += 1;
        //     //     Message.success('上传成功！');
        //     // }).catch(() => {
        //     //     this.$refs.upload.clearFiles();
        //     //     this.fileDoc = {};
        //     //     this.filePic = '';
        //     //     this.errorNum += 1;
        //     //     Message.error('上传失败！');
        //     // });
        // },
        enterNegative() {
            this.hoverTextNegative = true;
        },
        leaveNegative() {
            this.hoverTextNegative = false;
        },
        onchangeNegative(file) {
            this.loading = true;
            const fileName = file.name;
            const isLt2M = file.size;
            const regex = /(.jpg|.jpeg|.png|.bmp|.tif|.gif|.pcx|.tga|.exif|.fpx|.svg|.psd|.cdr|.pcd|.dxf|.ufo|.eps|.ai|.raw|.WMF|.webp)$/;
            if (regex.test(fileName.toLowerCase())) {
                if (isLt2M !== 0) {
                    if (isLt2M / 1024 / 1024 < 10 && isLt2M / 2 / 2 > 0) {
                        this.imageUrlNegative = file.url;
                        this.rawNegativeImg = file.raw;
                        this.loading = false;
                    } else {
                        this.$message.error(`上传图片大小不能超过10M!`);
                        this.loading = false;
                    }
                } else {
                    this.$message.error(`上传影像大小不能为0!`);
                    this.loading = false;
                }
            } else {
                this.$message.error("上传图片只支持图片格式!");
                this.loading = false;
            }
        },
        onchangeNegativeVideo(file) {
            this.loading = true;
            const regex = file.raw.type.split("/")[0] === "video";
            const isLt2M = file.size;
            if (regex) {
                if (isLt2M !== 0) {
                    if (isLt2M / 1024 / 1024 < 10 && isLt2M / 2 / 2 > 0) {
                        this.imageUrlNegativeVideo = file.url;
                        this.loading = false;
                    } else {
                        this.$message.error(`上传影像大小不能超过10M!`);
                        this.loading = false;
                    }
                } else {
                    this.$message.error(`上传影像大小不能为0!`);
                    this.loading = false;
                }
            } else {
                this.$message.error("上传影像只能是 video/* 格式!");
                this.loading = false;
            }
        },
        onchangeNegativeWord(file) {
            this.loading = true;
            const isLt2M = file.size;
            const regex = file.raw.type.split("/")[0] === "application";
            if (regex) {
                if (isLt2M !== 0) {
                    if (isLt2M / 1024 / 1024 < 2 && isLt2M / 2 / 2 > 0) {
                        this.imageUrlNegativeWord = file.url;
                        this.loading = false;
                    } else {
                        this.$message.error(`上传文档大小不能超过2M!`);
                        this.loading = false;
                    }
                } else {
                    this.$message.error(`上传文档大小不能为0!`);
                    this.loading = false;
                }
            } else {
                this.$message.error("上传文档只能是 application/vnd.* 格式!");
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.picc-form-item--cancel {
    padding-bottom: 27px;
}

.el-col.el-col-8 {
    height: 52px;
}

.flex {
    display: flex;
    align-items: center;
}

::v-deep.picc-upload {
    width: 156px;
    height: 96px;
    line-height: 96px;

    &.cur {
        .el-upload--picture-card {
            border: 1px solid #1ac88e !important;
        }
    }

    .el-upload--picture-card {
        position: relative;

        .img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 154px;
            height: 94px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 6px;
            overflow: hidden;

            .picc-icon {
                position: absolute;
                top: 4px;
                right: 4px;
            }

            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}

::v-deep {
    .picc-upload--batch {
        line-height: 0;

        .el-upload--text {
            display: flex;
            align-items: center;
        }

        .el-upload-list {
            .el-upload-list__item {
                font-size: 12px;
                height: 20px;
            }
        }
    }

    .picc-upload-form-item .el-form-item__content {
        display: flex;
        align-items: center;

        .picc-form-tipmessage {
            margin-left: 14px;
            width: auto;
        }
    }
}
</style>
