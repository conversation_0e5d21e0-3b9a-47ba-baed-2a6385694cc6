<template>
    <div>
        <el-button @click="centerDialogVisible = true">
            点击打开 Dialog
        </el-button>

        <el-dialog title="提示" :visible.sync="centerDialogVisible" width="30%" class="picc-dialog">
            <span>需要注意的是内容是默认不居中的</span>
            <div slot="footer" class="dialog-footer">
                <el-button size="mini" round plain type="primary" @click="centerDialogVisible = false">
                    取 消
                </el-button>
                <el-button size="mini" round type="primary" @click="centerDialogVisible = false">
                    确 定
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "Modal",
    data() {
        return {
            centerDialogVisible: false
        };
    }
};
</script>
<style lang="scss" scoped>
.el-dialog__footer {
    text-align: center;
    border-top: 1px solid #cdcdcd;
}
::v-deep .el-button.el-button--primary:last-child {
    margin-left: 16px;
}
::v-deep .el-dialog__footer {
    padding: 12px 24px 24px;
}
::v-deep .el-dialog__body span{
    height: 20px;
    line-height: 20px;
}
::v-deep .el-dialog__header {
    padding: 22px 24px;
    height: 64px;
    .el-dialog__headerbtn {
    top: 24px;
    right: 24px;
}
}
</style>
