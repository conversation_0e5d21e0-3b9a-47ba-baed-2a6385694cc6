<template>
    <div>
        <FullScreenBar :showdata="showdata" />
        <div class="inPageOutCardTitlebar">
            <BoxHead title="客户信息1111">
                <div slot="outsideOperation">
                    <el-button type="text">
                        添加客户
                    </el-button>
                </div>
            </BoxHead>
        </div>
    </div>
</template>
<script>
export default {
    name:"Titlebar",
    data() {
        return {
            showdata: [
                {
                    title: "本月案件(件)",
                    value: "189394",
                    subValue: "",
                    icons: {
                        type: "none",
                        value: ""
                    }
                },
                {
                    title: "总任务完成情况(件)",
                    value: "16394",
                    subValue: "/18394",
                    icons: {
                        type: "image",
                        value: ["1", "2", "3"]
                    }
                },
                {
                    title: "总任务完成率",
                    value: "90.3",
                    subValue: "%",
                    icons: {
                        type: "trend",
                        value: "arrow-up"
                    }
                },
                {
                    title: "全险种赔付率",
                    value: "89.0",
                    subValue: "%",
                    icons: {
                        type: "trend",
                        value: "arrow-down"
                    }
                }
            ]
        };
    }
};
</script>
