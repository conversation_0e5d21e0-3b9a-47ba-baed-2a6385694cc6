<template>
    <el-row class="temBtn">
        <el-col :span="24">
            <h2>1、通用按钮</h2>
            <h4>1-1、卡片内按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button round size="mini" type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">加载状态</span>
            <el-button round loading size="mini" type="primary" />
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button round disabled size="mini" type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="24">
            <h4>1-2、基础核心按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button round type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">加载状态</span>
            <el-button round loading type="primary" />
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button round disabled type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="24">
            <h4>1-3、卡片内次要按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button round plain size="mini" type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">加载状态</span>
            <el-button round plain size="mini" loading type="primary" />
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button round plain size="mini" disabled type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="24">
            <h4>1-4、基础次要按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button round plain type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">加载状态</span>
            <el-button round plain loading type="primary" />
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button round plain disabled type="primary">
                圆角按钮
            </el-button>
        </el-col>
        <el-col :span="24">
            <h4>1-5、次要组合按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button icon="el-icon-search" round plain type="primary">
                搜索
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">加载状态</span>
            <el-button round loading plain type="primary" />
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button icon="el-icon-search" round plain disabled type="primary">
                搜索
            </el-button>
        </el-col>
        <el-col :span="24">
            <h4>1-6、底部栏按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button class="picc-button-bottom" type="primary">
                核心按钮
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">加载状态</span>
            <el-button loading class="picc-button-bottom" type="primary" />
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button disabled class="picc-button-bottom" type="primary">
                核心按钮
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">描边展示</span>
            <el-button plain class="picc-button-bottom" type="primary">
                核心按钮
            </el-button>
        </el-col>
        <el-col :span="24">
            <h2>2、其他按钮</h2>
            <h4>2-1、图标按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button icon="el-icon-search" class="picc-button--icon" />
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button icon="el-icon-search" disabled class="picc-button--icon" />
        </el-col>
        <el-col :span="24">
            <h4>2-2、文字按钮</h4>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">常规状态</span>
            <el-button type="text">
                文字按钮
            </el-button>
        </el-col>
        <el-col :span="6">
            <span class="btnTitle">不可用状态</span>
            <el-button type="text" disabled>
                文字按钮
            </el-button>
        </el-col>
        <el-col :span="24">
            <h4>2-3、展开按钮</h4>
            <p>该按钮需要文字按钮和图标组合。</p>
        </el-col>
        <el-col :span="6">
            <el-button type="primary" icon="picc-icon picc-icon-upload-o" class="flex" round plain>
                批量上传
            </el-button>
        </el-col>
        <el-col :span="6">
            <el-button type="text" icon="picc-icon picc-icon-excel" class="picc-button--icon flex">
                下载模板
            </el-button>
        </el-col>
        <el-col :span="24">
            <h4>2-4、模块按钮</h4>
        </el-col>
        <el-col :span="24">
            <tab-item
                v-for="(value, index) in tabs_num"
                :key="index"
                class="items"
                :title="value.title"
                :type="value.type"
                :num="value.num"
                :selected="value.selected"
                @click.native="choose('tabs_num', index)"
            />
        </el-col>
        <el-col :span="24">
            <h4>2-5、标签按钮</h4>
        </el-col>
        <el-col :span="24">
            <tab-item
                v-for="(value, index) in tabs_state"
                :key="index"
                class="items"
                :title="value.title"
                :type="value.type"
                :num="value.num"
                :selected="value.selected"
                :state="value.state"
                :size="value.size"
                @click.native="choose('tabs_state', index)"
            />
        </el-col>
    </el-row>
</template>
<script>
export default {
    name: "Button",
    data() {
        return {
            tabs_num: [
                { title: "我的待办", type: "number", selected: true, state: "" },
                { title: "预约待办", type: "number", num: 2, selected: false, state: "" },
                { title: "待办活动", type: "number", num: 5, selected: false, state: "" }
            ],
            tabs_state: [
                { size: "", title: "立案申请", type: "status", num: 0, selected: true, state: "success" },
                { size: "", title: "查勘定损", type: "status", num: 2, selected: false, state: "reject" },
                { size: "", title: "领款人登记", type: "status", num: 5, selected: false, state: "waiting" },
                { size: "", title: "理算申请", type: "status", num: 5, selected: false, state: "" },
                { size: "", title: "一站式案件处理", type: "status", num: 5, selected: false, state: "" }
            ]
        };
    },
    methods: {
        choose(data, index) {
            this[data] = this[data].map((v, i) => {
                v.selected = i === index;
                return v;
            });
        }
    }
};
</script>
<style>
.btnTitle {
    margin-right: 20px;
}
.temBtn,
h2,
h4,
span {
    font-family: Microsoft Yahei;
}
.flex {
    display: flex;
    align-items: center;
}
</style>
