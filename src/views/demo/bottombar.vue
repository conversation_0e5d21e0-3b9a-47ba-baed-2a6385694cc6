<template>
    <bottom-bar :class="$store.state.app.sideBarSpread ? 'side-bottombar' : ''">
        <div slot="left-info">
            我是左侧slot内容{{ $store.state.app.sideBarSpread }}
        </div>
        <template slot="button-group">
            <div class="button-group-container">
                <el-button type="text">
                    暂存
                </el-button>
                <el-button type="text">
                    提交
                </el-button>
                <el-dropdown trigger="click" @visible-change="visibleChange">
                    <span class="el-dropdown-link"> 更多<i class="el-icon--right" :class="caretClass"></i></span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item>对账单号</el-dropdown-item>
                        <el-dropdown-item>最新支付跟单信息</el-dropdown-item>
                        <el-dropdown-item>再保信息</el-dropdown-item>
                        <el-dropdown-item>风险单位</el-dropdown-item>
                        <el-dropdown-item>其他信息</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-button type="primary" class="el-button-no-border">
                    提交核保
                </el-button>
            </div>
        </template>
    </bottom-bar>
</template>

<script>
export default {
    name: "Bottombar",
    data() {
        return {
            caretClass: "el-icon-caret-bottom"
        };
    },
    methods: {
        visibleChange(boolean) {
            this.caretClass = boolean ? "el-icon-caret-top" : "el-icon-caret-bottom";
        }
    }
};
</script>

<style scoped lang="scss">
.side-bottombar {
    padding: 0 !important;
}
.button-group-container{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
}
.el-button--primary {
    height: 56px !important;
    border-radius: 0;
}
.el-dropdown {
    margin-left: 0;
    padding: 0 24px;
    min-width: 104px;
    position: relative;
    height: 56px;

    &:hover {
        cursor: pointer;
        span {
            color: #2a47a5;
        }
    }

    span {
        color: #1c61fc;
        padding-left: 8px;
    }

    &:before {
        content: "";
        display: block;
        height: 24px;
        border-left: 1px solid #dddddd;
        position: absolute;
        left: 0;
        top: 16px;
    }
}

::v-deep .popper__arrow {
    display: none;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
    color: #606266;
}
</style>
