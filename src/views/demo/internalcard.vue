<template>
    <div class="inPageOutCardTitlebar">
        <BoxHead title="客户信息1111">
            <div slot="outsideOperation">
                <el-button type="text">
                    添加客户
                </el-button>
            </div>
        </BoxHead>
        <div class="form-cord">
            <formCard v-for="(item, index) in dataArray" :key="index" title="用户信息" :create-is-open="createIsOpen">
                <div slot="operation" class="slot-info">
                    <span></span>
                    <el-button type="text">
                        重置
                    </el-button>
                    <span></span>
                    <el-button type="text">
                        添加
                    </el-button>
                    <span></span>
                    <el-button type="text">
                        编辑
                    </el-button>
                    <span></span>
                    <el-button type="text">
                        <i class="el-icon-delete-solid"></i>
                    </el-button>
                </div>
                <!-- main form items -->
                <div>
                    <el-form label-width="120px" size="medium">
                        <el-row type="flex" class="row-bg">
                            <el-col :span="8">
                                <el-form-item label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-input v-model="item.demo1" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="16"></el-col> -->
                        </el-row>
                        <el-row type="flex" class="row-bg">
                            <el-col :span="8">
                                <el-form-item label="展开文本样式">
                                    <el-input v-model="item.demo2" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="16"></el-col> -->
                        </el-row>
                    </el-form>
                </div>
                <!--  -->
                <div slot="expanderBox">
                    <el-form label-width="120px" size="medium">
                        <el-row type="flex" class="row-bg">
                            <el-col :span="8">
                                <el-form-item label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-input v-model="item.demo3" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="16"></el-col> -->
                        </el-row>
                        <el-row type="flex" class="row-bg">
                            <el-col :span="8">
                                <el-form-item label="展开文本样式">
                                    <el-input v-model="item.demo4" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="16"></el-col> -->
                        </el-row>
                    </el-form>
                </div>
            </formCard>

            <div class="picc-form-card--add" @click="dataArray.push({ demo1: '', demo2: '', demo3: '', demo4: '' })">
                <i class="el-icon-plus"></i><span>添加客户</span>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name:"Internalcard",
    data() {
        return {
            createIsOpen: false,
            dataArray: [
                {
                    demo1: "测试信息1",
                    demo2: "测试信息2",
                    demo3: "测试信息3",
                    demo4: "测试信息4"
                },
                {
                    demo1: "测试信息1",
                    demo2: "测试信息2",
                    demo3: "测试信息3",
                    demo4: "测试信息4"
                },
                {
                    demo1: "测试信息1",
                    demo2: "测试信息2",
                    demo3: "测试信息3",
                    demo4: "测试信息4"
                },
                {
                    demo1: "测试信息1",
                    demo2: "测试信息2",
                    demo3: "测试信息3",
                    demo4: "测试信息4"
                },
                {
                    demo1: "测试信息1",
                    demo2: "测试信息2",
                    demo3: "测试信息3",
                    demo4: "测试信息4"
                }
            ]
        };
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.form-cord {
    .slot-info {
        & > span {
            width: 1px;
            height: 12px;
            display: inline-block;
            background: #cdcdcd;
            margin: 0 16px;
        }
    }
}
.inPageOutCardTitlebar {
    text-align: left;
}
</style>
