<template>
    <div>
        <div class="Echarts">
            <div id="main" style="width: 600px;height:400px;"></div>
        </div>
    </div>
</template>
<script>
export default {
    name: "Chart",
    mounted() {
        this.myEcharts();
    },
    methods: {
        myEcharts() {
            // 基于准备好的dom，初始化echarts实例
            var myChart = this.$echarts.init(document.getElementById("main"));

            // 指定图表的配置项和数据
            var option = {
                title: {
                    text: "基础雷达图"
                },
                tooltip: {},
                legend: {
                    data: ["预算分配（Allocated Budget）", "实际开销（Actual Spending）"]
                },
                radar: {
                    // shape: 'circle',
                    name: {
                        textStyle: {
                            color: "#fff",
                            backgroundColor: "#999",
                            borderRadius: 3,
                            padding: [3, 5]
                        }
                    },
                    indicator: [
                        { name: "销售", max: 6500 },
                        { name: "管理", max: 16000 },
                        { name: "信息技术", max: 30000 },
                        { name: "客服", max: 38000 },
                        { name: "研发", max: 52000 },
                        { name: "市场", max: 25000 }
                    ]
                },
                series: [
                    {
                        name: "预算 vs 开销（Budget vs spending）",
                        type: "radar",
                        // areaStyle: {normal: {}},
                        data: [
                            {
                                value: [4300, 10000, 28000, 35000, 50000, 19000],
                                name: "预算分配（Allocated Budget）"
                            },
                            {
                                value: [5000, 14000, 28000, 31000, 42000, 21000],
                                name: "实际开销（Actual Spending）"
                            }
                        ]
                    }
                ]
            };
            myChart.on("updateAxisPointer", function(event) {
                var xAxisInfo = event.axesInfo[0];
                if (xAxisInfo) {
                    var dimension = xAxisInfo.value + 1;
                    myChart.setOption({
                        series: {
                            id: "pie",
                            label: {
                                formatter: "{b}: {@[" + dimension + "]} ({d}%)"
                            },
                            encode: {
                                value: dimension,
                                tooltip: dimension
                            }
                        }
                    });
                }
            });

            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);
        }
    }
};
</script>
