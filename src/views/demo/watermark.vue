<template>
    <div class="watermark">
        <el-row>
            <el-col :span="18">
                <el-card id="watermark_content" header="">
                    <template v-if="!decryptedImg">
                        <h3>《临江仙》</h3>
                        <p>滚滚长江东逝水，浪花淘尽英雄。</p>
                        <p>是非成败转头空。青山依旧在，几度夕阳红。</p>
                        <p>白发渔樵江渚上，惯看秋月春风。</p>
                        <p>一壶浊酒喜相逢。古今多少事，都付笑谈中。</p>
                        <img :src="img" />
                    </template>

                    <img v-else style="width: 100%; height: 100%" :src="decryptedImg" />
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="setting" header="配置器" shadow="never">
                    <el-form label-position="top">
                        <el-divider>方法（点击触发）</el-divider>
                        <el-button type="warning" @click="clickUpdate">
                            update
                        </el-button>
                        <el-button type="danger" @click="clickRemove">
                            remove
                        </el-button>
                        <el-divider>水印</el-divider>
                        <el-form-item label="使用水印">
                            <el-switch v-model="form.useLight" />
                        </el-form-item>
                        <div v-show="form.useLight">
                            <el-divider>水印内容</el-divider>
                            <el-form-item label="水印文字">
                                <el-input v-model="form.text" type="textarea" />
                            </el-form-item>
                            <el-divider>水印样式</el-divider>
                            <el-form-item label="透明度">
                                <el-slider v-model="form.alpha" :min="0" :max="1" :step="0.01" />
                            </el-form-item>
                            <el-form-item label="垂直间距">
                                <el-input-number v-model="form.paddingX" :step="1" :min="0" />
                            </el-form-item>
                            <el-form-item label="水平间距">
                                <el-input-number v-model="form.paddingY" :step="1" :min="0" />
                            </el-form-item>
                            <el-form-item label="zIndex">
                                <el-input-number v-model="form.zIndex" :step="1" :min="0" />
                            </el-form-item>
                            <el-form-item label="旋转角度">
                                <el-input-number v-model="form.angle" :step="1" />
                            </el-form-item>
                            <el-divider>文本样式</el-divider>
                            <el-form-item label="字体大小">
                                <el-input-number v-model="form.fontSize" :step="1" :min="12" />
                            </el-form-item>
                            <el-form-item label="字体行高">
                                <el-input v-model="form.lineHeight" />
                            </el-form-item>
                            <el-form-item label="对齐方式">
                                <el-select v-model="form.textAlign">
                                    <el-option value="left">
                                        left
                                    </el-option>
                                    <el-option value="center">
                                        center
                                    </el-option>
                                    <el-option value="right">
                                        right
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="字体粗细">
                                <el-select v-model="form.fontWeight">
                                    <el-option value="lighter">
                                        lighter
                                    </el-option>
                                    <el-option value="normal">
                                        normal
                                    </el-option>
                                    <el-option value="bold">
                                        bold
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="字体颜色">
                                <el-color-picker v-model="form.color" />
                            </el-form-item>
                            <el-form-item label="字体">
                                <el-input v-model="form.font" />
                            </el-form-item>
                        </div>
                        <el-divider>盲水印</el-divider>
                        <el-form-item label="使用盲水印">
                            <el-switch v-model="form.useBlind" />
                        </el-form-item>
                        <div v-show="form.useBlind">
                            <el-form-item label="水印文字">
                                <el-input v-model="form.blindText" type="textarea" />
                            </el-form-item>
                            <el-form-item label="透明度">
                                <el-input-number v-model="form.blindAlpha" :min="0" :max="1" :step="0.001" />
                            </el-form-item>
                            <el-form-item label="水平间距">
                                <el-input-number v-model="form.blindPaddingX" :step="1" :min="0" />
                            </el-form-item>
                            <el-form-item label="垂直间距">
                                <el-input-number v-model="form.blindPaddingY" :step="1" :min="0" />
                            </el-form-item>
                            <el-form-item label="旋转角度">
                                <el-input-number v-model="form.blindAngle" :step="1" />
                            </el-form-item>
                            <el-form-item label="字体大小">
                                <el-input-number v-model="form.blindFontSize" :step="1" :min="12" />
                            </el-form-item>
                            <el-form-item label="字体行高">
                                <el-input v-model="form.blindLineHeight" />
                            </el-form-item>
                            <el-form-item label="对齐方式">
                                <el-select v-model="form.blindTextAlign">
                                    <el-option value="left">
                                        left
                                    </el-option>
                                    <el-option value="center">
                                        center
                                    </el-option>
                                    <el-option value="right">
                                        right
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="字体粗细">
                                <el-input v-model="form.blindFontWeight" />
                            </el-form-item>
                            <el-form-item label="字体颜色">
                                <el-color-picker v-model="form.blindColor" />
                            </el-form-item>
                            <el-form-item label="字体">
                                <el-input v-model="form.blindFont" />
                            </el-form-item>
                            <el-form-item label="盲水印解密">
                                <el-button @click="blindDecryption">
                                    {{ decryptedImg ? "恢复" : "解密" }}
                                </el-button>
                            </el-form-item>
                        </div>
                    </el-form>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import Watermark from "@picc/watermark";
import img from "@/assets/images/1.jpg";
import { debounce } from "@/utils";
import html2canvas from "html2canvas";

export default {
    name: "Watermark",
    data() {
        return {
            decryptedImg: null,
            watermark: null,
            form: {
                text: "PICC",
                alpha: 0.15,
                paddingX: 16,
                paddingY: 16,
                zIndex: 99999,
                angle: -22,
                fontSize: 16,
                lineHeight: 1.4,
                textAlign: "center",
                fontWeight: "normal",
                color: "#000000",
                font: "微软雅黑",
                useLight: true,
                useBlind: false,
                blindText: "看不见我",
                blindAlpha: 0.01,
                blindPaddingX: 28,
                blindPaddingY: 28,
                blindAngle: 22,
                blindFontSize: 16,
                blindLineHeight: 1.4,
                blindTextAlign: "center",
                blindFontWeight: "normal",
                blindColor: "#000000",
                blindFont: "微软雅黑"
            },
            config: {}
        };
    },
    computed: {
        img: () => img
    },
    watch: {
        form: {
            deep: true,
            handler(val) {
                this.config = {
                    noLight: !val.useLight,
                    container: "watermark_content",
                    blindContainer: "watermark_content",
                    text: val.text.split("\n"),
                    font: val.font,
                    fontSize: val.fontSize + "px",
                    fontWeight: val.fontWeight,
                    textAlign: val.textAlign,
                    color: val.color,
                    lineHeight: val.lineHeight,
                    alpha: val.alpha,
                    padding: `${val.paddingX}px ${val.paddingY}px`,
                    angle: val.angle,
                    zIndex: val.zIndex,
                    useBlind: val.useBlind,
                    blindFullscreen: false,
                    blindText: val.blindText.split("\n"),
                    blindFont: val.blindFont,
                    blindFontSize: val.blindFontSize + "px",
                    blindFontWeight: val.blindFontWeight,
                    blindTextAlign: val.blindTextAlign,
                    blindColor: val.blindColor,
                    blindLineHeight: val.blindLineHeight,
                    blindAlpha: val.blindAlpha,
                    blindPadding: `${val.blindPaddingX}px ${val.blindPaddingY}px`,
                    blindAngle: val.blindAngle
                };
            }
        },
        config() {
            this.update();
        }
    },
    mounted() {
        this.watermark = new Watermark({ text: "picc" });
        this.form2Config();
    },
    destroyed() {
        this.watermark.remove();
        this.watermark = null;
    },
    methods: {
        update: debounce(
            function () {
                if (!this.decryptedImg) {
                    setTimeout(() => this.watermark.update(this.config));
                }
            },
            500,
            false
        ),
        form2Config() {
            this.config = {
                noLight: !this.form.useLight,
                container: "watermark_content",
                blindContainer: "watermark_content",
                text: this.form.text.split("\n"),
                font: this.form.font,
                fontSize: this.form.fontSize + "px",
                fontWeight: this.form.fontWeight,
                textAlign: this.form.textAlign,
                color: this.form.color,
                lineHeight: this.form.lineHeight,
                alpha: this.form.alpha,
                padding: `${this.form.paddingX}px ${this.form.paddingY}px`,
                angle: this.form.angle,
                zIndex: this.form.zIndex,
                useBlind: this.form.useBlind,
                blindFullscreen: false,
                blindText: this.form.blindText.split("\n"),
                blindFont: this.form.blindFont,
                blindFontSize: this.form.blindFontSize + "px",
                blindFontWeight: this.form.blindFontWeight,
                blindTextAlign: this.form.blindTextAlign,
                blindColor: this.form.blindColor,
                blindLineHeight: this.form.blindLineHeight,
                blindAlpha: this.form.blindAlpha,
                blindPadding: `${this.form.blindPaddingX}px ${this.form.blindPaddingY}px`,
                blindAngle: this.form.blindAngle
            };
        },
        blindDecryption() {
            if (this.decryptedImg) {
                this.decryptedImg = null;
                this.update();
            } else {
                html2canvas(document.querySelector("#watermark_content")).then((canvas) => {
                    this.watermark.remove();
                    const ctx = canvas.getContext("2d");
                    const originalData = ctx.getImageData(0, 0, ctx.canvas.width, ctx.canvas.height);
                    let data = originalData.data;
                    for (let i = 0; i < data.length; i++) {
                        // 筛选每个像素点的R值
                        if (i % 4 === 0) {
                            if (data[i] % 2 === 0) {
                                // 如果R值为偶数，说明这个点是没有水印信息的，将其R值设为0
                                data[i] = 0;
                            } else {
                                // 如果R值为奇数，说明这个点是有水印信息的，将其R值设为255
                                data[i] = 255;
                            }
                        } else if (i % 4 !== 3) {
                            // G、B值设置为0，不影响
                            data[i] = 0;
                        }
                    }
                    // 至此，带有水印信息的点都将展示为255,0,0   而没有水印信息的点将展示为0,0,0  将结果绘制到画布
                    ctx.putImageData(originalData, 0, 0);
                    this.decryptedImg = canvas.toDataURL("image/png");
                });
            }
        },
        clickUpdate() {
            // this.watermark.update();
            let configTest = {
                text: "点击update"
            };
            this.watermark.update(configTest);
        },
        clickRemove() {
            this.watermark.remove();
        }
    }
};
</script>

<style lang="scss" scoped>
.watermark {
    padding: 16px;

    .setting {
        height: 600px;
        overflow: auto;
    }

    #watermark_content {
        ::v-deep {
            .el-card__body {
                height: 600px;
                overflow: auto;
            }
        }
    }

    ::v-deep {
        .el-form-item label.el-form-item__label {
            padding-left: 0;
        }
    }
}
</style>
