<template>
    <div class="todolist">
        <div style="padding:20px"></div>
        <el-divider content-position="left">
            To Do List
        </el-divider>
        <el-row :gutter="20">
            <el-col :span="24">
                <ToDoList title="今日提醒" :num="5" :data="todoData" @selectOption="selectFun" />
            </el-col>
        </el-row>
        <el-card class="box-card" shadow="never">
            <div class="code"></div>
        </el-card>
        <!-- {{ todoData }} -->
    </div>
</template>
<script>
export default {
    name:"Todolist",
    data() {
        return {
            todoData: [
                { status: "emergency", mainTip: "本月抽查及抽查报告", subTip: "抽查报告撰写", isSelect: true },
                { status: "meeting", mainTip: "组织重要案件学习", subTip: "地铁三号漏水...", isSelect: false },
                { status: "emergency", mainTip: "下午4点会议", subTip: "抽查报告撰写", isSelect: false },
                { status: "complete", mainTip: "讨论为什么要加班", subTip: "什么是无意义的加班什么是无意义的加班什么是无意义的加班", isSelect: false },
                { status: "complete", mainTip: "讨论为加班", subTip: "什么是无意义的加班什么是无意义", isSelect: false }
            ]
        };
    },
    methods: {
        selectFun(item) {
            // this.$message.success(item.mainTip);
        }
    }
};
</script>
<style lang="scss">
.todolist {
    text-align: left;
}
</style>
