<template>
    <div style="padding:30px;">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-divider content-position="left">
                    步骤式导航1
                </el-divider>
                <el-steps :active="active" :space="200" simple class="picc-steps-simple">
                    <el-step title="步骤 1" :icon="stepsIcons[0]" :status="steps[0]" />
                    <el-step title="步骤 2" :icon="stepsIcons[1]" :status="steps[1]" />
                    <el-step title="步骤 3" :icon="stepsIcons[2]" :status="steps[2]" />
                    <el-step title="步骤 4" :icon="stepsIcons[3]" :status="steps[3]" />
                </el-steps>
                <el-button round plain type="primary" style="margin-top: 12px;" @click="next">
                    同意
                </el-button>
                <el-button round plain type="primary" style="margin-top: 12px;" @click="pre">
                    拒绝
                </el-button>
            </el-col>
            <el-col :span="12">
                <el-divider content-position="left">
                    步骤式导航2
                </el-divider>
                <el-timeline class="picc-steps-vertical">
                    <el-timeline-item :icon="stepsIcons[0]" :type="steps[0]">
                        <StepCard title="sdfsdfsdf" description="sdfsdfsdsdfsdfdsf">
                            <div>sdfsdfsdfsdf</div>
                        </StepCard>
                    </el-timeline-item>
                    <el-timeline-item :icon="stepsIcons[1]" :type="steps[1]">
                        <StepCard title="sdfsdfsdf" description="sdfsdfsdsdfsdfdsf">
                            <div>sdfsdfsdfsdf</div>
                        </StepCard>
                    </el-timeline-item>
                    <el-timeline-item :icon="stepsIcons[2]" :type="steps[2]">
                        <StepCard title="sdfsdfsdf" description="sdfsdfsdsdfsdfdsf">
                            <div>sdfsdfsdfsdf</div>
                        </StepCard>
                    </el-timeline-item>
                    <el-timeline-item :icon="stepsIcons[3]" :type="steps[3]">
                        <StepCard title="sdfsdfsdf" description="sdfsdfsdsdfsdfdsf">
                            <div>sdfsdfsdfsdf</div>
                        </StepCard>
                    </el-timeline-item>
                </el-timeline>
                <el-button round plain type="primary" style="margin-top: 12px;" @click="next">
                    同意
                </el-button>
                <el-button round plain type="primary" style="margin-top: 12px;" @click="pre">
                    拒绝
                </el-button>
            </el-col>
        </el-row>
        <el-row>
            <el-col>
                <el-divider content-position="left">
                    步骤式导航3
                </el-divider>
                <el-steps direction="vertical" :active="1" space="36px">
                    <el-step title="步骤 1" />
                    <el-step title="步骤 2" />
                    <el-step title="步骤 3" description="这是一段很长很长很长的描述性文字" />
                </el-steps>
                <el-steps direction="vertical" :active="active" :space="200" simple class="picc-steps-simple">
                    <el-step title="步骤 1" :icon="stepsIcons[0]" :status="steps[0]" />
                    <el-step title="步骤 2" :icon="stepsIcons[1]" :status="steps[1]" />
                    <el-step title="步骤 3" :icon="stepsIcons[2]" :status="steps[2]" />
                    <el-step title="步骤 4" :icon="stepsIcons[3]" :status="steps[3]" />
                </el-steps>
                <el-button round plain type="primary" style="margin-top: 12px;" @click="next">
                    同意
                </el-button>
                <el-button round plain type="primary" style="margin-top: 12px;" @click="pre">
                    拒绝
                </el-button>
            </el-col>
        </el-row>
    </div>
</template>
<script>
//
// import StepCard from "@/components/StepCard";
export default {
    name:"Steps",
    components: {
        // StepCard
    },
    data() {
        return {
            steps: ["process", "wait", "wait", "wait"],
            active: 0,
            valTrue: true,
            activeName: "first",
            stepsIcons: ["", "", "", ""]
        };
    },
    methods: {
        next() {
            const count = this.active;
            this.stepsIcons[count] = "el-icon-check";
            this.steps[count] = "success";
            this.steps[count + 1] = "process";
            this.active++;
            if (this.active > 4) {
                this.steps = ["process", "wait", "wait", "wait"];
                this.stepsIcons = ["", "", "", ""];
                this.active = 0;
            }
        },
        pre() {
            const count = this.active;
            this.stepsIcons[count] = "el-icon-close";
            this.steps[count] = "error";
            this.steps[count + 1] = "process";
            this.active++;
            if (this.active > 4) {
                this.steps = ["process", "wait", "wait", "wait"];
                this.stepsIcons = ["", "", "", ""];
                this.active = 0;
            }
        }
    }
};
</script>
