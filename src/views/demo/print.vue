<template>
    <div class="wrap">
        <h2>此内容打印会被忽略</h2>
        <div id="printContent">
            <el-button @click="print">
                {{ title }}
            </el-button>
            <h1 class="custom">
                {{ title }}
            </h1>
            <img style="width: 200px" src="https://img2.baidu.com/it/u=1814268193,3619863984&fm=253&fmt=auto&app=138&f=JPEG?w=632&h=500" />
            <table style="width: 100%">
                <thead>
                    <tr class="tb-header">
                        <th>集群名称</th>
                        <th>项目名称</th>
                        <th>版本</th>
                        <th>节点（主-从）</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in tableData" :key="item">
                        <td>{{ item.name }}</td>
                        <td>{{ item.application }}</td>
                        <td>{{ item.version }}</td>
                        <td>{{ item.node }}</td>
                        <td>
                            <div class="state">
                                <span>{{ item.state }}</span>
                            </div>
                        </td>
                        <td>
                            <el-button type="text">
                                {{ start }}
                            </el-button>
                            <span> / </span>
                            <el-button type="text">
                                {{ stop }}
                            </el-button>
                            <el-button type="text" class="red">
                                {{ recycle }}
                            </el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>
<script>
import printHtml from "../../utils/print";
export default {
    name:"Printtest",
    data() {
        return {
            title: "打印测试",
            start: "启动",
            stop: "停止",
            recycle: "回收",
            show: true,
            ruleForm: {
                name: "",
                state: ""
            },
            tableData: [
                {
                    name: "00008081-V1.0.8",
                    application: "中后台应用",
                    version: "V1.0.0",
                    node: "3-3",
                    state: "未运行"
                },
                {
                    name: "00008081-V1.0.8",
                    application: "中后台应用",
                    version: "00008081-V1.0.8",
                    node: "V2.0.0",
                    state: "离线"
                },
                {
                    name: "00008081-V1.0.8",
                    application: "中后台应用",
                    version: "00008081-V1.0.8",
                    node: "V3.0.0",
                    state: "警告"
                },
                {
                    name: "00008081-V1.0.8",
                    application: "中后台应用",
                    version: "00008081-V1.0.8",
                    node: "V3.0.0",
                    state: "运行中"
                },
                {
                    name: "00008081-V1.0.8",
                    application: "中后台应用",
                    version: "V1.0.0",
                    node: "3-3",
                    state: "未运行"
                },
                {
                    name: "00008081-V1.0.8",
                    application: "中后台应用",
                    version: "00008081-V1.0.8",
                    node: "V2.0.0",
                    state: "离线"
                },
                {
                    name: "00008081-V1.0.8",
                    application: "中后台应用",
                    version: "00008081-V1.0.8",
                    node: "V3.0.0",
                    state: "警告"
                }
            ]
        };
    },
    mounted() {},
    methods: {
        print() {
            let newstr = document.getElementById("printContent").innerHTML;
            let printData = `<div>打印内容</div>`;
            printHtml(newstr);
        }
    }
};
</script>
<style scoped lang="scss">
@media print {
    h1 {
        font-size: 20px;
        color: red;
        // page-break-before: always;
    }
    .red {
        color: #d50000 !important;
    }
    table {
        font-family: Arial, sans-serif;
        font-size: 14px;
        background-color: #f0f2f5;
        border-collapse: collapse;
        color: #454545;
        table-layout: auto;
        width: 100%;
        text-align: center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: #dadcde;
        thead {
            border-top-width: 1px;
            border-top-style: solid;
            border-top-color: #dadcde;
            line-height: 40px;
            font-weight: bold;
            color: #454c70;
        }
        tr {
            border-top-width: 1px;
            border-top-style: solid;
            border-top-color: #dadcde;
            line-height: 23px;
        }
        td {
            padding: 15px 10px;
            font-size: 14px;
            font-family: Verdana;
            width: 100px;
            word-break: break-all; // 元素换行
        }
        // 斑马纹效果stripe
        tr:nth-child(even) {
            background: #f5f7f9;
        }
        tr:nth-child(odd) {
            background: #fff;
        }
    }
}

.wrap {
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    margin: 20px;

    .red {
        color: #d50000 !important;
    }
    .state {
        > span {
            display: inline-block;
            width: 60px;
        }
    }
    .tb-header {
        background-color: rgb(245, 245, 245);
    }
    table {
        font-family: Arial, sans-serif;
        font-size: 14px;
        background-color: #f0f2f5;
        border-collapse: collapse;
        color: #454545;
        table-layout: auto;
        width: 100%;
        text-align: center;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: #dadcde;
        thead {
            border-top-width: 1px;
            border-top-style: solid;
            border-top-color: #dadcde;
            line-height: 40px;
            font-weight: bold;
            color: #454c70;
        }
        tr {
            border-top-width: 1px;
            border-top-style: solid;
            border-top-color: #dadcde;
            line-height: 23px;
        }
        td {
            padding: 15px 10px;
            font-size: 14px;
            font-family: Verdana;
            width: 100px;
            word-break: break-all; // 元素换行
        }
        // 斑马纹效果stripe
        tr:nth-child(even) {
            background: #f5f7f9;
        }
        tr:nth-child(odd) {
            background: #fff;
        }
    }
}
</style>
