<template>
    <div class="tabs_demo">
        <el-row :gutter="20">
            <el-col :span="12">
                <el-divider content-position="left">
                    Tab切换
                </el-divider>
                <el-tabs v-model="activeName" class="picc-tabs">
                    <el-tab-pane label="用户管理" name="first">
                        用户管理
                    </el-tab-pane>
                    <el-tab-pane label="配置管理" name="second">
                        配置管理
                    </el-tab-pane>
                    <el-tab-pane label="角色管理" name="third">
                        角色管理
                    </el-tab-pane>
                    <el-tab-pane label="定时任务补偿" name="fourth">
                        定时任务补偿
                    </el-tab-pane>
                </el-tabs>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="24">
                <el-divider content-position="left">
                    二级页面内标签切换
                </el-divider>
                <el-tabs v-model="activeName" type="card" class="picc-tabs-card" @tab-click="handleClick">
                    <el-tab-pane label="用户管理" name="first">
                        用户管理
                    </el-tab-pane>
                    <el-tab-pane label="配置管理" name="second">
                        配置管理
                    </el-tab-pane>
                    <el-tab-pane label="角色管理" name="third">
                        角色管理
                    </el-tab-pane>
                    <el-tab-pane label="定时任务补偿" name="fourth">
                        定时任务补偿
                    </el-tab-pane>
                </el-tabs>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
    name: "Tabs",
    data() {
        return {
            activeName: "first"
        };
    },
    methods: {
        handleClick(tab, event) {
            console.log(tab, event);
        }
    }
};
</script>

<style scoped lang="scss">
</style>
