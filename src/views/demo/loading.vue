<template>
    <div class="loading-wrapper">
        <h2>服务方式调用</h2>
        <el-divider />
        <el-button type="info" round @click="showLoading()">
            默认Loading
        </el-button>
        <el-button type="success" round @click="showLoading('line')">
            进度条Loading
        </el-button>
        <el-button type="danger" round @click="showLoading('circle')">
            环形Loading
        </el-button>
        <h2 style="margin-top: 30px;">
            局部调用
        </h2>
        <el-divider />
        <el-table v-piccLoading="{ show: true }" picc-loading-text="" :data="tableData" style="width: 100%">
            <el-table-column prop="type" label="保障类型" />
            <el-table-column prop="range" label="保障范围" />
            <el-table-column prop="account" label="保额/每份(元)" />
        </el-table>
        <el-divider />
        <el-table v-piccLoading:progress="{ show: loading, percentage: curPercentage }" :data="tableData">
            <el-table-column prop="type" label="保障类型" />
            <el-table-column prop="range" label="保障范围" />
            <el-table-column prop="account" label="保额/每份(元)" />
        </el-table>
    </div>
</template>

<script>
export default {
    name:"Loading",
    data() {
        return {
            loading: true,
            curPercentage: 0,
            tableData: [
                { type: "家财综合险", range: "室内财产", account: "10,000,00" },
                { type: "家财综合险", range: "室内财产", account: "10,000,00" },
                { type: "家财综合险", range: "室内财产", account: "10,000,00" }
            ]
        };
    },
    mounted() {
        this.increace();
    },
    methods: {
        showLoading(type = null) {
            this.loadingInstance = this.$piccLoading({
                type: type,
                text: "正在识别证件信息…",
                percentage: 0
            });
            const timer = setInterval(() => {
                this.loadingInstance.percentage < 100 ? (this.loadingInstance.percentage += 20) : clearInterval(timer);
            }, 200);
        },
        increace() {
            const timer = setInterval(() => {
                this.curPercentage < 100 ? (this.curPercentage += 5) : clearInterval(timer);
            }, 200);
        }
    }
};
</script>

<style scoped lang="scss">
.loading-wrapper {
    margin-top: -60px;
    padding-top: 60px;
    text-align: left;
    ::v-deep .picc-loading-mask {
        z-index: 999 !important;
    }
}
</style>
