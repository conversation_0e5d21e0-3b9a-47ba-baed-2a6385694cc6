<template>
    <div>
        <el-row :gutter="20">
            <el-col :span="6" :offset="9">
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <el-input v-model="input" class="el-input-style" :placeholder="$t('form.demo_input_text')" />
                <div style="display: flex; justify-content: space-between">
                    <el-button round plain type="primary">
                        {{ $t("form.demo_input_button") }}
                    </el-button>
                    <lang-select />
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script>
import LangSelect from "@/components/LangSelect";
export default {
    name: "I18n",
    components: {
        LangSelect
    },
    data() {
        return {
            input: ""
        };
    }
};
</script>
<style lang="scss" scoped>
.el-input-style {
    margin-bottom: 15px;
}
</style>
