<template>
    <div class="tabs_demo">
        <div class="demo_num">
            <tab-item
                v-for="(value,index) in tabs_num" :key="index" class="items"
                :title="value.title" :type="value.type" :num="value.num" :selected="value.selected" @click.native="choose('tabs_num',index)"
            />
        </div>
        <div class="demo_state">
            <div>
                <tab-item
                    v-for="(value,index) in tabs_state" :key="index" class="items"
                    :title="value.title" :type="value.type" :num="value.num" :selected="value.selected" :state="value.state" :size="value.size" @click.native="choose('tabs_state',index)"
                />
                <el-button v-if="!add" type="primary" class="addButton" plain @click="addItem">
                    添加环节
                </el-button>
                <el-button v-if="add" type="primary" class="addButton" plain @click="addItem">
                    完成添加
                </el-button>
                <div v-show="add">
                    <div class="add_layer">
                        选择常规案件处理环节，或选择<span>一站式案件处理模式</span>
                    </div>
                    <div>
                        <tab-item
                            v-for="(value,index) in tabs_addItem" :key="index" class="items"
                            :title="value.title" :type="value.type" :num="value.num" :selected="value.selected" :state="value.state" @click.native="pushItem(value)"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="demo_mini">
            <tab-item
                class="items"
                title="查" type="status" :selected="true" size="mini" state="success"
            />
            <tab-item
                class="items"
                title="查" type="status" :selected="false" size="mini" state="reject"
            />
            <tab-item
                class="items"
                title="查" type="status" :selected="true" size="mini" state="waiting"
            />
        </div>
        <el-button type="primary" @click="changeSize">
            改变尺寸
        </el-button>
    </div>
</template>

<script>
// import TabItem from '../../components/TabItem';
export default {
    name: "Labels",
    // components:{TabItem},
    data(){
        return {
            tabs_num:[
                {title:"我的待办",type:"number",selected:true,state:''},
                {title:"预约待办",type:"number",num:2,selected:false,state:''},
                {title:"待办活动",type:"number",num:5,selected:false,state:''}

            ],
            tabs_state:[
                {size:'',title:"立案申请",type:"status",num:0,selected:true,state:'success'},
                {size:'',title:"查勘定损",type:"status",num:2,selected:false,state:'reject'},
                {size:'',title:"领款人登记",type:"status",num:5,selected:false,state:'waiting'},
                {size:'',title:"理算申请",type:"status",num:5,selected:false,state:''},
                {size:'',title:"一站式案件处理",type:"status",num:5,selected:false,state:''}

            ],
            tabs_addItem:[
                {title:"第三方机构",type:"addItem"},
                {title:"关键环节管控",type:"addItem"},
                {title:"保险合同诉讼",type:"addItem"},
                {title:"预赔付",type:"addItem"},
                {title:"担保信息",type:"addItem"},
                {title:"损余录入",type:"addItem"},
                {title:"残值处理",type:"addItem"},
                {title:"追偿发起",type:"addItem"},
                {title:"查勘定损",type:"addItem",state:'reject'},
                {title:"领款人登记",type:"addItem",state:'chose'}

            ],
            add:false
        };
    },
    methods:{
        choose(data,index){
            this[data] = this[data].map((v,i) =>{
                v.selected = i === index ?  true : false;
                return v;
            });
        },
        addItem(){
            this.add = !this.add;
        },
        pushItem(value){
            if(value.state === "reject" || value.state === "chose") return;
            let addOne = Object.assign({},value,{type:'status'});
            this.tabs_state.push(addOne);
            value.state = "chose";
        },
        changeSize(){
            if(this.tabs_state[0].size){
                this.tabs_state = this.tabs_state.map(function (v) {
                    v.size = "";
                    return v;
                });
            }else{
                this.tabs_state = this.tabs_state.map(function (v) {
                    v.size = "mini";
                    return v;
                });
            }
        }
    }
};
</script>

<style scoped lang="scss">
    .tabs_demo>div{
        background-color: #fff;
        margin-bottom: 30px;
        text-align: left;
        padding-left: 16px;
        font-size: 0;
    }
    .demo_num{
        height: 74px;
        padding-top: 10px;
    }
    .demo_state{
        padding: 20px 0 0 0;
        .addButton{
            float: right;
            margin-right: 16px;
            margin-top: 6px;
        }
        .add_layer{
            font-size: 14px;
            margin-bottom: 29px;
            padding-top: 32px;
            span{
                display: inline-block;
                margin-left: 18px;
                color: #001AFF;
            }
        }
    }
    .items{
        margin-bottom: 20px;
    }
    .demo_mini{
        padding-top: 10px;
    }
</style>
