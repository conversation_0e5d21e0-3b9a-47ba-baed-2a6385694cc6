<template>
    <div>
        <!-- 提示信息 -->
        <div class="top-tip-div">
            <div class="top-tip">
                <p>页面模板中，“内页卡片外标题栏”距模块上下间距，存在两种版本：一是【上下间距舒适版】；二是【上下间距紧凑版】；各项目组可根据实际业务需求，灵活使用；</p>
            </div>
        </div>
        <div style="margin-top: 44px;">
            <BoxHead title="上下间距舒适版(“内页卡片外标题栏”高52px，距模板上16px，下8px)">
                <div slot="outsideOperation">
                    <span class="span-red">在使用过程中，需注意，同一系统必须使用同一版本！</span>
                </div>
            </BoxHead>
        </div>

        <div class="picc-container-border">
            <BoxHead title="选择产品" />
            <div class="picc-container">
                <el-form ref="dialogBox" :model="formBox" label-width="120px">
                    <el-row type="flex">
                        <el-col :span="8">
                            <el-form-item v-picc-input-error prop="typeCodea" label="产品类型" class="picc-linefeed--medium">
                                <el-input
                                    ref="input" v-model="formBox.typeCodea" v-input-overflow-popup="formBox.typeCodea"
                                    class="el-input-style" placeholder="输入文本信息" clearable @focus="inputFocus('typeCodea')"
                                    @blur="inputBlur('typeCodea')"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="产品名称" class="picc-linefeed--medium">
                                <el-input
                                    v-model="formBox.typeCode" v-popover:popover2 class="el-input-style"
                                    placeholder="输入文本信息" disabled
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" />
                    </el-row>
                </el-form>
            </div>
            <BoxHead title="选择出单方式" />
            <div class="picc-container">
                <el-form ref="dialogBox" :model="formBox" label-width="120px">
                    <el-row type="flex">
                        <el-col :span="8">
                            <el-form-item label="下拉选择器-仅操作型" class="picc-form-label-linefeed picc-linefeed--medium">
                                <el-select v-model="selectOper.optionsValue" placeholder="请选择">
                                    <el-option-group
                                        v-for="group in selectOper.options" :key="group.label"
                                        :label="group.label"
                                    >
                                        <el-option
                                            v-for="item in group.options" :key="item.value" :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-option-group>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                v-picc-input-error prop="insuranceShow" label="下拉选择器-仅操作型2"
                                class="picc-form-label-linefeed picc-linefeed--medium"
                            >
                                <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                                    <el-option
                                        v-for="(item, index) in selectOper.insurance" :key="index"
                                        :label="`${item.num}-${item.value}`" :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" />
                    </el-row>
                </el-form>
            </div>
        </div>


        <BoxHead title="上下间距紧凑版(“内页卡片外标题栏”高52px，距模板上下间距0px)" class="picc-box-head-compact" />
        <div class="picc-container-border">
            <BoxHead title="选择产品" class="picc-box-head-compact" />
            <div class="picc-container picc-container-compact">
                <el-form ref="dialogBox" :model="formBox" label-width="120px">
                    <el-row type="flex">
                        <el-col :span="8">
                            <el-form-item v-picc-input-error prop="typeCodea" label="产品类型" class="picc-linefeed--medium picc-form-compact">
                                <el-input
                                    ref="input" v-model="formBox.typeCodea" v-input-overflow-popup="formBox.typeCodea"
                                    class="el-input-style" placeholder="输入文本信息" clearable @focus="inputFocus('typeCodea')"
                                    @blur="inputBlur('typeCodea')"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="产品名称" class="picc-linefeed--medium picc-form-compact">
                                <el-input
                                    v-model="formBox.typeCode" v-popover:popover2 class="el-input-style"
                                    placeholder="输入文本信息" disabled
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" />
                    </el-row>
                </el-form>
            </div>
            <BoxHead title="选择出单方式" class="picc-box-head-compact" />
            <div class="picc-container picc-container-compact">
                <el-form ref="dialogBox" :model="formBox" label-width="120px">
                    <el-row type="flex">
                        <el-col :span="8">
                            <el-form-item label="下拉选择器-仅操作型" class="picc-form-label-linefeed picc-linefeed--medium picc-form-compact">
                                <el-select v-model="selectOper.optionsValue" placeholder="请选择">
                                    <el-option-group
                                        v-for="group in selectOper.options" :key="group.label"
                                        :label="group.label"
                                    >
                                        <el-option
                                            v-for="item in group.options" :key="item.value" :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-option-group>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                v-picc-input-error prop="insuranceShow" label="下拉选择器-仅操作型2"
                                class="picc-form-label-linefeed picc-linefeed--medium picc-form-compact"
                            >
                                <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                                    <el-option
                                        v-for="(item, index) in selectOper.insurance" :key="index"
                                        :label="`${item.num}-${item.value}`" :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" />
                    </el-row>
                </el-form>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "PageTemplate",
    data() {
        return {
            formBox: {
                typeCodea: "",
                typeCode: "",
            },
            /* 下拉选择器 */
            selectOper: {
                validStatus: "",
                insuranceShow: "",
                insurance: [{ num: "01", value: "普通家庭财产保险1" },
                    { num: "02", value: "普通家庭财产保险2" },
                    { num: "03", value: "普通家庭财产保险3" },
                    { num: "04", value: "普通家庭财产保险财产保险1" },
                    { num: "05", value: "普通家庭财产保险财产保险2" },
                    { num: "06", value: "普通家庭财产保险财产保险3" },
                    { num: "07", value: "普通家庭财产保险4" },
                    { num: "08", value: "普通家庭财产保险财产保险4" },
                    { num: "09", value: "普通家庭财产保险5" },
                    { num: "10", value: "普通家庭财产保险财产保险5" }],
                insuranceShow1: "",
                insuranceShow2: "",
                insuranceShow3: "",
                insurance1: [{ num: "01", value: "普通家庭财产保险1" },
                    { num: "02", value: "普通家庭财产保险财产保险3普通家庭财产保险财产保险" },
                    { num: "03", value: "普通家庭财产保险3" }],
                options: [{
                    label: "热门城市", options: [{ value: "Shanghai", label: "上海" },
                        { value: "Beijing", label: "北京" }]
                },
                {
                    label: "城市名", options: [{ value: "Chengdu", label: "成都" },
                        { value: "Shenzhen", label: "深圳" },
                        { value: "Guangzhou", label: "广州" },
                        { value: "Dalian", label: "大连" }]
                }],
                optionsValue: ""
            },
        };
    },
    created() {

    },
    methods: {
        inputBlur: function (target) {
            // this.rules[target][0].required = true
        },
        inputFocus: function (target) {
            // this.rules[target][0].required = false
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        },
    }
};
</script>
<style lang="scss" scoped>
// 顶部提示信息样式
.top-tip-div {
    width: 100%;
    background-color: #fff;
    position: fixed;
    top: 92px;
    z-index: 5;

    .top-tip {
        width: 100%;
        background-color: rgba($color: #ffe4e0, $alpha: 0.8);
        padding: 14px 0 14px 24px;

        p {
            font-size: 14px;
            color: #292b34;
            line-height: 16px;
            margin: 0;
        }

        span {
            color: red;
        }
    }
}

// .picc-container-border {
//     border: 1px solid rgba(205, 205, 205, 1);
//     border-radius: 4px;
//     padding-bottom: 20px;
//     margin: 0 24px;
// }

.picc-container {
    margin: 8px 24px 16px;
}

.picc-container-compact {
    margin: 0px 24px;
    padding: 2px 0;
}

::v-deep .picc-box-head {
    justify-content: left;

    .span-red {
        margin-left: 6px;
        color: red;
        font-size: 14px;
    }
}
</style>
