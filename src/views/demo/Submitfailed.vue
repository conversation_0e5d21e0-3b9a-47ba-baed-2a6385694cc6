<template>
    <div>
        <el-button type="primary" @click="dialogShowChange(true)">
            点击出现报错框
        </el-button>
        <div v-if="dialogShow" class="dialogBox">
            <!-- <myDialog :dialog-show="dialogShow" @dialogShowChange="dialogShowChange" /> -->
            <Submitfailed :dialog-show="dialogShow" @dialogShowChange="dialogShowChange" />
        </div>
    </div>
</template>
<script>
// import myDialog from "../../../src/components/NewSubmitfailed";
export default {
    name: "Failed",
    // components: {
    //     myDialog
    // },
    data() {
        return {
            dialogShow: true
        };
    },
    methods: {
        dialogShowChange(val) {
            this.dialogShow = val;
        }
    },
};
</script>
