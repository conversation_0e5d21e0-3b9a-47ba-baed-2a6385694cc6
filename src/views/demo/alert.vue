<template>
    <div>
        <el-row>
            <el-col :span="18" :offset="3">
                <el-divider content-position="left">
                    对话框
                </el-divider>
                <el-button round plain type="primary" @click="open1">
                    成功提示
                </el-button>
                <el-button round plain type="primary" @click="open2">
                    失败反馈
                </el-button>
                <el-button round plain type="primary" @click="open3">
                    警告反馈
                </el-button>
                <el-button round plain type="primary" @click="open4">
                    多行反馈
                </el-button>
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
    name:"Alert",
    methods: {
        open1 () {
            this.$confirm('对话框内容', '对话框标题', {
                dangerouslyUseHTMLString: true, // 必填
                confirmButtonText: '再试一次',
                cancelButtonText: '取消',
                roundButton: true, // 必填
                type: 'success', // 必填
                customClass: 'picc-message-box', // 必填
                center: true,
                showClose: false
            }).catch(err=>{
                console.error(err);
            });
        },
        open2 () {
            this.$confirm('对话框内容', '对话框标题', {
                dangerouslyUseHTMLString: true, // 必填
                confirmButtonText: '再试一次',
                cancelButtonText: '取消',
                roundButton: true, // 必填
                type: 'error', // 必填
                customClass: 'picc-message-box', // 必填
                center: true,
                showClose: false
            }).catch(err=>{
                console.error(err);
            });
        },
        open3 () {
            this.$confirm('对话框内容', '对话框标题', {
                dangerouslyUseHTMLString: true, // 必填
                confirmButtonText: '再试一次',
                cancelButtonText: '取消',
                roundButton: true, // 必填
                type: 'warning', // 必填
                customClass: 'picc-message-box', // 必填
                center: true,
                showClose: false
            }).catch(err=>{
                console.error(err);
            });
        },
        open4 () {
            this.$confirm(`
                <el-button round plain type="primary" @click="open1">
                    成功提示
                </el-button>
            `, '对话框标题', {
                dangerouslyUseHTMLString: true, // 必填
                confirmButtonText: '再试一次',
                cancelButtonText: '取消',
                roundButton: true, // 必填
                type: 'warning', // 必填
                customClass: 'picc-message-box', // 必填
                center: true,
                showClose: false
            }).catch(err=>{
                console.error(err);
            });
        }
    }
};
</script>
