<template>
    <div class="app-container ">
        <el-descriptions title="公共方法测试">
            <el-descriptions-item label="setCookie">
                <button @click="setCookie">
                    测试setCookie
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="getCookie">
                <button @click="getCookie">
                    测试getCookie
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="deleteCookie">
                <button @click="deleteCookie">
                    测试deleteCookie
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="settoken">
                <button @click="settoken">
                    测试settoken
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="gettoken">
                <button @click="gettoken">
                    测试gettoken
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="removetoken">
                <button @click="removetoken">
                    测试removetoken
                </button>
            </el-descriptions-item>
            <!-- <el-descriptions-item label="验证token是否有效">
        <button @click="validtoken">验证token</button>
      </el-descriptions-item>
      <el-descriptions-item label="通过token获取信息">
        <button @click="explainToken">获取id</button>
      </el-descriptions-item> -->
            <el-descriptions-item label="日期格式化">
                <button @click="formatTimeDate">
                    年月日时分秒
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="日期格式化">
                <button @click="formatDate">
                    年月日
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="获取页面title">
                <button @click="getTitle">
                    获取页面title
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="判断时间距离">
                <button @click="getTime">
                    判断时间距离
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="getQueryObject">
                <button @click="getQueryObject">
                    调用getQueryObject
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="获取字节长度">
                <button @click="byteLength">
                    调用byteLength
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="解析json内容">
                <button @click="param">
                    调用param
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="解析json内容">
                <button @click="param2Obj">
                    调用param2Obj
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="获取div内容">
                <button @click="html2Text">
                    调用html2Text
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="调用objectMerge">
                <button @click="objectMerge">
                    调用objectMerge
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="复制对象">
                <button @click="deepClone">
                    调用deepClone
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="数组去重">
                <button @click="uniqueArr">
                    调用uniqueArr
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="生成随机字符串">
                <button @click="createUniqueString">
                    调用createUniqueString
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="更改组件名称">
                <button
                    class="test1"
                    @click="toggleClass"
                >
                    调用toggleClass
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="通过类名判断组件">
                <button
                    class="test2"
                    @click="hasClass"
                >
                    调用hasClass
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="拼接类名">
                <button
                    class="test3"
                    @click="addClass"
                >
                    调用addClass
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="移除类名">
                <button
                    class="test123"
                    @click="removeClass"
                >
                    调用removeClass
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="弹窗">
                <button @click="openW">
                    调用openWindow
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="滚动">
                <button @click="scroll">
                    调用scrollTo
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="存储数据">
                <button @click="setStorage">
                    调用setStorage
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="取单条数据">
                <button @click="getStorage">
                    调用getStorage
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="取多条数据">
                <button @click="getAllStorage">
                    调用getAllStorage
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="删除单条数据">
                <button @click="removeStorage">
                    调用removeStorage
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="删除多条数据">
                <button @click="removeStorageList">
                    调用removeStorageList
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="清空数据">
                <button @click="clearStorage">
                    调用clearStorage
                </button>
            </el-descriptions-item>
            <el-descriptions-item label="查看存储类型">
                <button @click="getType">
                    调用getType
                </button>
            </el-descriptions-item>
        </el-descriptions>
        <el-form
            ref="dataForm"
            :rules="rules"
            :model="tempData"
            label-position=""
            label-width="30%"
            class="user-Form-cntent"
            size="mini"
        >
            <el-form-item
                label="手机号码验证"
                prop="phone"
            >
                <el-input
                    v-model="tempData.phone"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="小数点后俩位验证"
                prop="NumPot"
            >
                <el-input
                    v-model="tempData.NumPot"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="字母数字中文验证"
                prop="character"
            >
                <el-input
                    v-model="tempData.character"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="字母数字验证"
                prop="character2"
            >
                <el-input
                    v-model="tempData.character2"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="车牌号验证"
                prop="licensePlateNumber"
            >
                <el-input
                    v-model="tempData.licensePlateNumber"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="车架号验证"
                prop="carFrameNumber"
            >
                <el-input
                    v-model="tempData.carFrameNumber"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="固定电话验证"
                prop="fixedTelephone"
            >
                <el-input
                    v-model="tempData.fixedTelephone"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="银行卡号验证"
                prop="bankCardNumber"
            >
                <el-input
                    v-model="tempData.bankCardNumber"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="身份证验证"
                prop="IDNumber"
            >
                <el-input
                    v-model="tempData.IDNumber"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="组织机构代码验证"
                prop="organizationalCodeCertificate"
            >
                <el-input
                    v-model="tempData.organizationalCodeCertificate"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="传真验证"
                prop="Fax"
            >
                <el-input
                    v-model="tempData.Fax"
                    placeholder="请输入..."
                />
            </el-form-item>
            <el-form-item
                label="邮编验证"
                prop="postCode"
            >
                <el-input
                    v-model="tempData.postCode"
                    placeholder="请输入..."
                />
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import { setCookie } from "@/utils/cookie";
import { getCookie } from "@/utils/cookie";
import { delCookie } from "@/utils/cookie";
import getPageTitle from "@/utils/get-page-title.js";
import openWindow from "@/utils/open-window.js";
import { scrollTo } from "@/utils/scroll-to.js";
import * as time from "@/utils/index.js";
import * as token from "@/utils/auth.js";
import * as date from "@/utils/date.js";
import Button from '../demo/button.vue';
import * as storage from "@/utils/storage.js";
import * as validate from "@/utils/validate.js";
export default {
    name:"TestUtilsFunction",
    // components: { Button },
    data() {
        return {
            // 表单校验规则，自定义校验规则从utils/validate.js引入
            tempData: {},
            rules: {
                phone: [{ validator: validate.validateMobile, trigger: "blur" }],
                NumPot: [{ validator: validate.checkNumPot2, trigger: "blur" }],
                character: [{ validator: validate.charActer, trigger: "blur" }],
                character2: [{ validator: validate.charActer2, trigger: "blur" }],
                licensePlateNumber: [{ validator: validate.licensePlateNumber, trigger: "blur" }],
                carFrameNumber: [{ validator: validate.carFrameNumber, trigger: "blur" }],
                fixedTelephone: [{ validator: validate.fixedTelephone, trigger: "blur" }],
                bankCardNumber: [{ validator: validate.bankCardNumber, trigger: "blur" }],
                IDNumber: [{ validator: validate.IDNumber, trigger: "blur" }],
                organizationalCodeCertificate: [{ validator: validate.organizationalCodeCertificate, trigger: "blur" }],
                Fax: [{ validator: validate.Fax, trigger: "blur" }],
                postCode: [{ validator: validate.postCode, trigger: "blur" }],
            }
        };
    },
    methods: {
        setCookie() {
            const cookieKey = "testcookieKey";
            const cookieVal = "testcookieValue";
            setCookie(cookieKey, cookieVal, 365);
        },
        getCookie() {
            var coo = getCookie("testcookieKey");
            alert(coo);
        },
        deleteCookie() {
            delCookie("testcookieKey");
        },
        settoken() {
            token.setToken("c2xramRmbGFza2Y=.MTY0NzIzNDkxMTk3MQ==", true);
        },
        gettoken() {
            var tk = token.getToken();
            alert(tk);
        },
        removetoken() {
            token.removeToken();
        },
        validtoken() {
            var valid = token.validToken();
            alert(valid);
        },
        explainToken() {
            var id = token.explainToken("c2xramRmbGFza2Y=.MTY0NzIzNDkxMTk3MQ==");
            alert(id);
        },
        formatTimeDate() {
            var time = date.dateTimeFilter(1647234911971);
            alert(time);
        },
        formatDate() {
            var time = date.dateFilter(1647234911971);
            alert(time);
        },
        getTitle() {
            var title = getPageTitle("dashboard");
            alert(title);
        },
        getTime() {
            var t = time.formatTime(1647234911971, false);
            alert(t);
        },
        getQueryObject() {
            var t = time.getQueryObject("http://localhost:13081/devops/findApolloClusterInfo?apolloClusterId=1234234");
            alert(JSON.stringify(t));
        },
        byteLength() {
            var t = time.byteLength("testalskdjflaksflk");
            alert(t);
        },
        param() {
            var t = time.param({
                "clusterName": "1",
                "dataCenter": "2",
                "state": "3",
                "validInd": "0"
            });
            alert(t);
        },
        param2Obj() {
            var t = time.param2Obj("http://localhost:13081/devops/findApolloClusterInfo?apolloClusterId=1234234");
            alert(JSON.stringify(t));
        },
        html2Text() {
            var t = time.html2Text("http://localhost:13081/devops/findApolloClusterInfo?apolloClusterId=1234234");
            alert(t);
        },
        objectMerge() {
            var t = time.objectMerge({
                "clusterName": "1",
                "dataCenter": "2",
                "state": "3",
                "validInd": "0"
            }, {
                "test1": "q",
                "test2": "w",
                "test3": "e",
                "test4": "r"
            });
            alert(JSON.stringify(t));
        },
        deepClone() {
            var t = time.deepClone({
                "clusterName": "1",
                "dataCenter": "2",
                "state": "3",
                "validInd": "0"
            });
            alert(JSON.stringify(t));
        },
        uniqueArr() {
            var t = time.uniqueArr([1, 2, 3, 4, 5, 2, 2, 3]);
            alert(t);
        },
        createUniqueString() {
            var t = time.createUniqueString();
            alert(t);
        },
        toggleClass() {
            var x = document.getElementsByClassName("test1")[0];
            var t = time.toggleClass(x, "change");
            alert(t);
        },
        hasClass() {
            var x = document.getElementsByClassName("test2")[0];
            var t = time.hasClass(x, "test2");
            alert(t);
        },
        addClass() {
            var x = document.getElementsByClassName("test3")[0];
            var t = time.addClass(x, "test4");
            alert(t);
        },
        removeClass() {
            var x = document.getElementsByClassName("test123")[0];
            var t = time.removeClass(x, "test");
            alert(t);
        },
        openW() {
            openWindow('http://www.baidu.com', '百度', 300, 200);
        },
        scroll() {
            scrollTo(400, 1000, () => {
            });
        },
        setStorage() {
            storage.setStorage("testkey", "testvalue");
            alert("存储成功");
        },
        getStorage() {
            var t = storage.getStorage("testkey");
            alert("取值为:" + t);
        },
        getAllStorage() {
            var t = storage.getAllStorage("testkey");
            alert(JSON.stringify(t));
        },
        removeStorage() {
            storage.removeStorage("testkey");
            alert("删除成功");
        },
        removeStorageList() {
            storage.removeStorageList("testkey");
            alert("删除成功");
        },
        clearStorage() {
            storage.clearStorage();
            alert("删除成功");
        },
        getType() {
            var t = storage.getType("testkey");
            alert(t);
        },
    }
};
</script>
<style lang="scss">
</style>
