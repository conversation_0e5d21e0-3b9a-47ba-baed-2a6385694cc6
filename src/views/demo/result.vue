<template>
    <div class="">
        <Result message="保单已支付成功" type="success">
            <!--  -->
            <template slot="body">
                <div class="mt40">
                    <div class="picc-result-list_title">
                        订单号
                    </div>
                    <span style="font-size: 12px">YGD0201911010000000812</span>
                </div>
                <div class="mt16">
                    <div class="picc-result-list_title">
                        订单号
                    </div>
                    <span style="font-size: 12px">YGD0201911010000000812</span>
                </div>
            </template>
        </Result>
        <br />
        <Result message="保单已支付失败" type="error">
            <!--  -->
            <template slot="body">
                <div class="mt28">
                    <div class="picc-result-list_title">
                        订单号
                    </div>
                    <order-expand style="font-size: 12px" :data="data" />
                </div>
                <div class="">
                    <div class="picc-result-list_title">
                        订单号
                    </div>
                    <order-expand style="font-size: 12px" :data="data1" />
                </div>
            </template>
        </Result>
        <br />
        <Result message="保单未支付" type="warning">
            <!--  -->
            <template slot="body">
                <div class="picc-result-msg">
                    此保单正在等待客户支付，后续可在保单管理中跟进支付完成情况
                </div>
                <div class="mt16">
                    <div class="picc-result-list_title">
                        订单号
                    </div>
                    <span style="font-size: 12px">YGD0201911010000000812</span>
                </div>
                <el-button class="mt32" type="primary" round>
                    查询支付结果
                </el-button>
            </template>
            <!--  -->
            <template slot="footer">
                <div class="picc-result-btm">
                    <el-button type="primary" round plain>
                        管理保单
                    </el-button>
                    <el-button type="primary" round plain>
                        录入新的投保单
                    </el-button>
                </div>
            </template>
        </Result>
    </div>
</template>
<script>
import OrderExpand from "@/components/OrderExpand";

export default {
    name: "ResultDemo",
    components: {
        OrderExpand
    },
    data() {
        return {
            data: [
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812",
                "YGD0201911010000000812"
            ],
            data1: ["YGD0201911010000000812"]
        };
    }
};
</script>
<style lang="scss" scoped>
.picc-result-body li {
    font-size: 12px !important;
    ul {
        li {
            font-family: arial;
            font-size: 14px;
            line-height: 14px;
            margin-bottom: 12px;
            // &:nth-child(even) {
            //     padding-left: 8px;
            // }
            // &:nth-child(odd) {
            //     padding-right: 8px;
            // }
        }
    }
}
</style>
