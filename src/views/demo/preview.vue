<template>
    <div>
        <carousel :img-array="images" />
    </div>
</template>
<script>
import a from "@/assets/images/1.jpg";
import b from "@/assets/images/2.jpg";
import c from "@/assets/images/3.jpg";
import d from "@/assets/images/4.jpg";
import e from "@/assets/images/5.jpg";
import f from "@/assets/images/6.jpg";
export default {
    name:"Preview",
    data() {
        return {
            images: [
                { name: "a", url: a },
                { name: "b", url: b },
                { name: "c", url: c },
                { name: "d", url: d },
                { name: "e", url: e },
                { name: "f", url: f }
            ]
        };
    }
};
</script>
