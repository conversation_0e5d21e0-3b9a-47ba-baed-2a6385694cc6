<template>
    <div>
        <el-form class="isbackground" style="width: 95%">
            <!-- <h4 class="border-left">查询条件</h4> -->
            <el-row :gutter="10">
                <el-col :span="9">
                    <el-form-item label="文档展示地址">
                        <el-input v-model="docViewURL" style="width: 70%" placeholder="请输入..." />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item>
                        <el-button type="primary" @click="refreash">
                            刷新
                        </el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div id="office" style="display: block; margin-top: 15px; width: 100%; height: calc(100vh - 170px)"></div>
    </div>
</template>

<script>
import OpenSDK from "@/assets/js/open-jssdk-v1.2.0.es.js";
export default {
    data() {
        return {
            docViewURL:
                "http://**************/docs/viewweb/reader/788f2c4aecfb4b698796aa3edfba?_w_filecode=f943458f18cb6cccccd7e54b07cd4136&_w_scene_id=4fa9f2d416f27387e7936ac6130f3c1a&_w_third_appid=AK20240531JLPFRR&_w_third_file_id=788f2c4aecfb4b698796aa3edfba&_w_demo_token=5c712c7747e2463bb8b31494ec0c"
        };
    },
    mounted() {
        this.initWPS();
        // OpenSDK.config({
        //     url: "http://**************/docs/viewweb/reader/788f2c4aecfb4b698796aa3edfba?_w_filecode=f943458f18cb6cccccd7e54b07cd4136&_w_scene_id=4fa9f2d416f27387e7936ac6130f3c1a&_w_third_appid=AK20240531JLPFRR&_w_third_file_id=788f2c4aecfb4b698796aa3edfba&_w_demo_token=5c712c7747e2463bb8b31494ec0c",
        //     mount: document.querySelector("#office"),
        //     setToken: {
        //         token: "your token", //根据自身的业务需求，通过异步请求或者模板输出的方式，取得token
        //         timeout: 30 * 60 * 1000
        //     }
        // });
    },
    methods: {
        initWPS() {
            OpenSDK.config({
                url: this.docViewURL,
                mount: document.querySelector("#office"),
                setToken: {
                    token: "your token", //根据自身的业务需求，通过异步请求或者模板输出的方式，取得token
                    timeout: 30 * 60 * 1000
                }
            });
        },
        refreash() {}
    }
};
</script>

<style>
</style>