<template>
    <div>
        <el-row>
            <el-col :span="12" style="margin: 20px">
                <handy-entry :data="entries" @addConfirm="addConfirmHandle" @cellClick="cellClickHandle" />
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
    name:"Handentry",
    data() {
        return {
            entries: [
                {
                    title: "保单查询",
                    icon: "entries-order-check",
                    url: "/test",
                    id: "entries-order-check",
                    checked: false
                },
                {
                    title: "我的团队",
                    icon: "entries-my-team",
                    url: "/test1",
                    id: "entries-my-team",
                    checked: false
                },
                {
                    title: "我的活动",
                    icon: "entries-my-activities",
                    url: "/test1",
                    id: "entries-my-activities",
                    checked: false
                },
                {
                    title: "报表管理",
                    icon: "entries-report-mng",
                    url: "/test1",
                    id: "entries-report-mng",
                    checked: false
                },
                {
                    title: "非车报价",
                    icon: "entries-nor-car-quote",
                    url: "/test1",
                    id: "entries-nor-car-quote",
                    checked: false
                },
                {
                    title: "车险报价",
                    icon: "entries-car-assurance-quote",
                    url: "/test1",
                    id: "entries-car-assurance-quote",
                    checked: false
                },
                {
                    title: "综合查询",
                    icon: "entries-repository",
                    url: "/test1",
                    id: "entries-repository",
                    checked: false
                },
                {
                    title: "代办列表",
                    icon: "entries-tasks-list",
                    url: "/test1",
                    id: "entries-tasks-list",
                    checked: false
                },
                {
                    title: "我的客户",
                    icon: "entries-my-customers",
                    url: "/test1",
                    id: "entries-my-customers",
                    checked: false
                },
                {
                    title: "我的任务",
                    icon: "entries-data-gather",
                    url: "/test1",
                    id: "entries-data-gather",
                    checked: false
                },
                {
                    title: "卡片管理",
                    icon: "hint-radio-bank",
                    url: "/test1",
                    id: "hint-radio-bank",
                    checked: false
                },
                {
                    title: "图片管理",
                    icon: "hint-upload-image",
                    url: "/test1",
                    id: "hint-upload-image",
                    checked: false
                },
                {
                    title: "上传管理",
                    icon: "hint-upload-word",
                    url: "/test1",
                    id: "hint-upload-word",
                    checked: false
                },
                {
                    title: "PICC",
                    icon: "nav-logo-open",
                    url: "/test",
                    id: "nav-logo-open",
                    checked: false
                }
            ]
        };
    },
    methods: {
        cellClickHandle(id, url) {
            // eslint-disable-line
            // console.log(id, url)
        },
        addConfirmHandle(data, callback) {
            // eslint-disable-line
            // console.log(data)
            // 更新数据接口(模拟)
            setTimeout(() => {
                callback && callback();
            }, 500);
        }
    }
};
</script>
