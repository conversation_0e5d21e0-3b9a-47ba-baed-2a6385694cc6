<template id="">
    <div class="">
        <el-dialog title="" :visible.sync="wechatPayShow" custom-class="picc-dialog-payment">
            <qr-code-pay total="1200" type="wechatpay" countdown="12312312312" order-no="123456789asd" qr-code="" text="复制链接地址11111" />
        </el-dialog>
        <el-dialog title="" :visible.sync="alipayShow" custom-class="picc-dialog-payment">
            <qr-code-pay
                total="1200"
                type="alipay"
                countdown="1231231231"
                order-no="123456789asd"
                qr-code="https://img.jigao616.com/upload/patent/2017/9/9/32336933.gif"
                text="复制链接地址222222222"
            />
        </el-dialog>
        <button type="button" name="button" @click="wechatPayShow = true">
            微信支付
        </button>
        <button type="button" name="button" @click="alipayShow = true">
            支付宝支付
        </button>
        <!-- <qr-code-pay class="border-red" total="1200" type="wechatpay" countdown="12312312312" order-no="123456789asd" qr-code="" text="复制链接地址11111" />
        <qr-code-pay class="border-red" total="1200" type="alipay" countdown="12312312312" order-no="123456789asd" qr-code="" text="复制链接地址11111" /> -->
    </div>
</template>
<script>
export default {
    name:'Payment',
    data() {
        return {
            wechatPayShow: false,
            alipayShow: false
        };
    }
};
</script>
<style lang="scss">
.border-red {
    border: 1px solid red;
    display: inline-block;
    margin: 15px;
    padding: 15px 0;
}
</style>
