<template>
    <div>
        <div style="margin: 20px;">
            <el-badge v-for="(item,index) in imageDatas" :key="index" :value="item.length" class="item">
                <!-- :src="item[0]" -->
                <img v-imageAsyncLoad="item[0]" alt="" class="list" @click="centerDialogVisiblehandle(index)" />
            </el-badge>
        </div>
        
        <el-dialog
            title="提示"
            :visible.sync="centerDialogVisible"
            width="30%"
            center
        >
            <imgDialog v-if="centerDialogVisible" :data-list="currentImgList" />
        </el-dialog>
    </div>
</template>
<script>
import imgDialog from "./imgDialog.vue";
export default  {
    name:"ImageAsyncLoad",
    components:{
        imgDialog : imgDialog
    },
    data(){
        return {
            centerDialogVisible : false,
            imageDatas:[
                [
                    "/img/1.jpg",
                    "/img/2.jpg",
                    "/img/3.jpg",
                ]
            ],
            currentImgList:[]
        };
    },
    methods:{
        centerDialogVisiblehandle(index){
            this.centerDialogVisible=true;
            this.currentImgList = this.imageDatas[index];
            console.log(index);
        }
    }
};
</script>
<style scoped>
.list{
    width:150px;
    height: 100px;
}
</style>