<template>
    <div>
        <div class="content">
            <div>
                <!-- :src="bigImageSrc" -->
                <img alt="" style="width: 450px;height:300px;" />
            </div>
            <div>
                <!-- :src="item"  -->
                <img v-for="(item,index) in dataList" :key="index" v-imageAsyncLoad="item" alt="" class="dialoglist" @click="imageClickHandle(index)" />
                {{ item }}
            </div>
        </div>
        <!-- <span slot="footer" class="dialog-footer">
            <el-button @click="centerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="centerDialogVisible = false">确 定</el-button>
        </span> -->
    </div>
</template>
<script>
export default  {
    name:"ImageAsyncLoad",
    props:{
        dataList:{
            type:Array,
            default:()=>[]
        }
    },
    data(){
        // console.log(this.dataList,"..........................");
        // bigImageSrc = this.dataList[0];
        return {
            bigImageSrc:""
        };
    },
    mounted(){
        this.bigImageSrc = this.dataList[0];
    },
    methods:{
        imageClickHandle(index){
            this.bigImageSrc = this.dataList[index];
        }
    }
    
};
</script>
<style scoped>
.dialoglist{
    width:150px;
    height: 100px;
}
</style>