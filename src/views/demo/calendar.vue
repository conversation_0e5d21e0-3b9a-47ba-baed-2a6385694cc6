<template>
    <div>
        <Calendar :task-list="taskList" @calendarShow="calendarShow" />
    </div>
</template>
<script>
export default {
    name:"CalendarDemo",
    data() {
        return {
            // 0-紧急事务  1-常规事务
            taskList: [
                { time: "2018-1-29", workList: [{ daily: "0" }, { daily: "1" }] },
                { time: "2019-12-15", workList: [{ daily: "0" }] },
                { time: "2019-12-18", workList: [{ daily: "1" }] },
                { time: "2019-12-25", workList: [{ daily: "1" }] },
                { time: "2019-12-29", workList: [{ daily: "1" }, { daily: "0" }] },
                { time: "2020-1-18", workList: [{ daily: "1" }] },
                { time: "2020-1-26", workList: [{ daily: "0" }] },
                { time: "2020-5-18", workList: [{ daily: "1" }] }
            ],
            calendar: ''
        };
    },
    methods: {
        calendarShow: function(data) {
            this.calendar = data;
            this.$forceUpdate();
        }
    }
};
</script>
