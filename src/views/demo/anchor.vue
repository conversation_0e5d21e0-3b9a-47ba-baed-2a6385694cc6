<template>
    <div :style="$store.state.app.toolbarOpened ? 'width:100%;' : 'width:80%;'">
        <anchor-point :point-list="testList" top="132px" right="calc(50vw - 725px)" />
        <div id="content1" class="achor-content anchorCard">
            <span style="font-size:30px;font-weight:bold;line-heght:50px;">content1</span>
            <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="typeCodea" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input
                                v-model="formBox.typeCodea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('typeCodea')"
                                @blur="inputBlur('typeCodea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="文本输入框不可用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input v-model="formBox.typeCode" v-popover:popover2 class="el-input-style" placeholder="输入文本信息" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            v-picc-input-longtext:value="formBox.textLong"
                            prop="textLong"
                            label="文本输入框文本过长"
                            class="picc-form-label-linefeed picc-linefeed--medium picc-inline-textarea"
                        >
                            <el-input v-model="formBox.textLong" type="text" class="el-input-style" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="foldLinea"
                            label="标题过长可以折行处理处理处理处理"
                            class="picc-form-label-linefeed picc-linefeed--long"
                        >
                            <el-input
                                id="foldLine"
                                v-model="formBox.foldLinea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('foldLinea')"
                                @blur="inputBlur('foldLinea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="标题过长可以折行" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.foldLine" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="tipInputa" label="带提示说明" class="picc-form-label-tipInfo-icon">
                            <el-input
                                id="tipInput"
                                v-model="formBox.tipInputa"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('tipInputa')"
                                @blur="inputBlur('tipInputa')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="带提示说明输入框" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.tipInput" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass1')"
                                @hide="isHide('casualClass1')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible1 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible1 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass1"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible2"
                                placement="bottom-start"
                                width="550"
                                disabled
                                :visible-arrow="false"
                                @show="isShow('casualClass2')"
                                @hide="isHide('casualClass2')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible2 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible2 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass2"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    disabled
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
            </el-form>
        </div>
        <div id="content2" class="achor-content anchorCard">
            <span style="font-size:30px;font-weight:bold;line-heght:50px;">content2</span>
            <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="typeCodea" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input
                                v-model="formBox.typeCodea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('typeCodea')"
                                @blur="inputBlur('typeCodea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="文本输入框不可用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input v-model="formBox.typeCode" v-popover:popover2 class="el-input-style" placeholder="输入文本信息" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            v-picc-input-longtext:value="formBox.textLong"
                            prop="textLong"
                            label="文本输入框文本过长"
                            class="picc-form-label-linefeed picc-linefeed--medium picc-inline-textarea"
                        >
                            <el-input v-model="formBox.textLong" type="text" class="el-input-style" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="foldLinea"
                            label="标题过长可以折行处理处理处理处理"
                            class="picc-form-label-linefeed picc-linefeed--long"
                        >
                            <el-input
                                id="foldLine"
                                v-model="formBox.foldLinea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('foldLinea')"
                                @blur="inputBlur('foldLinea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="标题过长可以折行" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.foldLine" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="tipInputa" label="带提示说明" class="picc-form-label-tipInfo-icon">
                            <el-input
                                id="tipInput"
                                v-model="formBox.tipInputa"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('tipInputa')"
                                @blur="inputBlur('tipInputa')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="带提示说明输入框" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.tipInput" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass1')"
                                @hide="isHide('casualClass1')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible1 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible1 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass1"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible2"
                                placement="bottom-start"
                                width="550"
                                disabled
                                :visible-arrow="false"
                                @show="isShow('casualClass2')"
                                @hide="isHide('casualClass2')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible2 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible2 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass2"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    disabled
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
            </el-form>
        </div>
        <div id="content3" class="achor-content anchorCard">
            <span style="font-size:30px;font-weight:bold;line-heght:50px;">content3</span>
            <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="typeCodea" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input
                                v-model="formBox.typeCodea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('typeCodea')"
                                @blur="inputBlur('typeCodea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="文本输入框不可用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input v-model="formBox.typeCode" v-popover:popover2 class="el-input-style" placeholder="输入文本信息" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            v-picc-input-longtext:value="formBox.textLong"
                            prop="textLong"
                            label="文本输入框文本过长"
                            class="picc-form-label-linefeed picc-linefeed--medium picc-inline-textarea"
                        >
                            <el-input v-model="formBox.textLong" type="text" class="el-input-style" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="foldLinea"
                            label="标题过长可以折行处理处理处理处理"
                            class="picc-form-label-linefeed picc-linefeed--long"
                        >
                            <el-input
                                id="foldLine"
                                v-model="formBox.foldLinea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('foldLinea')"
                                @blur="inputBlur('foldLinea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="标题过长可以折行" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.foldLine" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="tipInputa" label="带提示说明" class="picc-form-label-tipInfo-icon">
                            <el-input
                                id="tipInput"
                                v-model="formBox.tipInputa"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('tipInputa')"
                                @blur="inputBlur('tipInputa')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="带提示说明输入框" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.tipInput" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass1')"
                                @hide="isHide('casualClass1')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible1 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible1 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass1"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible2"
                                placement="bottom-start"
                                width="550"
                                disabled
                                :visible-arrow="false"
                                @show="isShow('casualClass2')"
                                @hide="isHide('casualClass2')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible2 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible2 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass2"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    disabled
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
            </el-form>
        </div>
        <div id="content4" class="achor-content anchorCard">
            <span style="font-size:30px;font-weight:bold;line-heght:50px;">content4</span>
            <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="typeCodea" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input
                                v-model="formBox.typeCodea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('typeCodea')"
                                @blur="inputBlur('typeCodea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="文本输入框不可用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input v-model="formBox.typeCode" v-popover:popover2 class="el-input-style" placeholder="输入文本信息" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            v-picc-input-longtext:value="formBox.textLong"
                            prop="textLong"
                            label="文本输入框文本过长"
                            class="picc-form-label-linefeed picc-linefeed--medium picc-inline-textarea"
                        >
                            <el-input v-model="formBox.textLong" type="text" class="el-input-style" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="foldLinea"
                            label="标题过长可以折行处理处理处理处理"
                            class="picc-form-label-linefeed picc-linefeed--long"
                        >
                            <el-input
                                id="foldLine"
                                v-model="formBox.foldLinea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('foldLinea')"
                                @blur="inputBlur('foldLinea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="标题过长可以折行" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.foldLine" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="tipInputa" label="带提示说明" class="picc-form-label-tipInfo-icon">
                            <el-input
                                id="tipInput"
                                v-model="formBox.tipInputa"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('tipInputa')"
                                @blur="inputBlur('tipInputa')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="带提示说明输入框" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.tipInput" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass1')"
                                @hide="isHide('casualClass1')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible1 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible1 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass1"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible2"
                                placement="bottom-start"
                                width="550"
                                disabled
                                :visible-arrow="false"
                                @show="isShow('casualClass2')"
                                @hide="isHide('casualClass2')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible2 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible2 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass2"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    disabled
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
            </el-form>
        </div>
        <div id="content5" class="achor-content anchorCard">
            <span style="font-size:30px;font-weight:bold;line-heght:50px;">content5</span>
            <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="typeCodea" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input
                                v-model="formBox.typeCodea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('typeCodea')"
                                @blur="inputBlur('typeCodea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="文本输入框不可用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input v-model="formBox.typeCode" v-popover:popover2 class="el-input-style" placeholder="输入文本信息" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            v-picc-input-longtext:value="formBox.textLong"
                            prop="textLong"
                            label="文本输入框文本过长"
                            class="picc-form-label-linefeed picc-linefeed--medium picc-inline-textarea"
                        >
                            <el-input v-model="formBox.textLong" type="text" class="el-input-style" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="foldLinea"
                            label="标题过长可以折行处理处理处理处理"
                            class="picc-form-label-linefeed picc-linefeed--long"
                        >
                            <el-input
                                id="foldLine"
                                v-model="formBox.foldLinea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('foldLinea')"
                                @blur="inputBlur('foldLinea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="标题过长可以折行" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.foldLine" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="tipInputa" label="带提示说明" class="picc-form-label-tipInfo-icon">
                            <el-input
                                id="tipInput"
                                v-model="formBox.tipInputa"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('tipInputa')"
                                @blur="inputBlur('tipInputa')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="带提示说明输入框" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.tipInput" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass1')"
                                @hide="isHide('casualClass1')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible1 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible1 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass1"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible2"
                                placement="bottom-start"
                                width="550"
                                disabled
                                :visible-arrow="false"
                                @show="isShow('casualClass2')"
                                @hide="isHide('casualClass2')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible2 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible2 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass2"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    disabled
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
            </el-form>
        </div>
        <div id="content6" class="achor-content anchorCard">
            <span style="font-size:30px;font-weight:bold;line-heght:50px;">content6</span>
            <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="typeCodea" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input
                                v-model="formBox.typeCodea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('typeCodea')"
                                @blur="inputBlur('typeCodea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="文本输入框不可用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-input v-model="formBox.typeCode" v-popover:popover2 class="el-input-style" placeholder="输入文本信息" disabled />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            v-picc-input-longtext:value="formBox.textLong"
                            prop="textLong"
                            label="文本输入框文本过长"
                            class="picc-form-label-linefeed picc-linefeed--medium picc-inline-textarea"
                        >
                            <el-input v-model="formBox.textLong" type="text" class="el-input-style" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="foldLinea"
                            label="标题过长可以折行处理处理处理处理"
                            class="picc-form-label-linefeed picc-linefeed--long"
                        >
                            <el-input
                                id="foldLine"
                                v-model="formBox.foldLinea"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('foldLinea')"
                                @blur="inputBlur('foldLinea')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="标题过长可以折行" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.foldLine" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="tipInputa" label="带提示说明" class="picc-form-label-tipInfo-icon">
                            <el-input
                                id="tipInput"
                                v-model="formBox.tipInputa"
                                class="el-input-style"
                                placeholder="输入文本信息"
                                @focus="inputFocus('tipInputa')"
                                @blur="inputBlur('tipInputa')"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="带提示说明输入框" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-input v-model="formBox.tipInput" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass1')"
                                @hide="isHide('casualClass1')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible1 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible1 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass1"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visible2"
                                placement="bottom-start"
                                width="550"
                                disabled
                                :visible-arrow="false"
                                @show="isShow('casualClass2')"
                                @hide="isHide('casualClass2')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column width="150" property="date" label="日期" />
                                    <el-table-column width="100" property="name" label="姓名" />
                                    <el-table-column width="300" property="address" label="地址" />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible2 = false">
                                        取 消
                                    </el-button>
                                    <el-button type="primary" @click="formBox.visible2 = false">
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass2"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    disabled
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" />
                </el-row>
            </el-form>
        </div>
    </div>
</template>
<script>
// import AnchorPoint from '@/components/AnchorPoint';
export default {
    name:"Anchor",
    // components: {
    //     AnchorPoint
    // },
    data() {
        return {
            testList: [
                { title: "测试第一条数据测试一下", id: "content1" },
                { title: "测试第二条数据", id: "content2" },
                { title: "测试第三条数据", id: "content3" },
                { title: "测试第四条数据", id: "content4" },
                { title: "测试第五条数据", id: "content5" },
                { title: "测试第六条数据", id: "content6" }
            ],
            formBox: {
                inputGroupzz: "",
                textLong: "",
                typeCodev: "",
                typeCodew: "",
                typeCodea: "",
                typeCode: "",
                foldLinea: "",
                foldLine: "",
                tipInputa: "",
                tipInput: "",
                visible: false,
                visible1: false,
                visible2: false,
                visible3: false,
                visible4: false,
                btnInputBox: "",
                iconBtnInputBox: "",
                resource: "",
                resource1: "是",
                resource2: "",
                types: [],
                types1: ["投保人"],
                types2: [],
                num: "",
                num1: "",
                delivery: true,
                delivery1: false,
                doublecheck: "",
                doublecheck1: ""
            },
            /* 下拉选择器 */
            selectOper: {
                validStatus: "",
                insuranceShow: "",
                insurance: [
                    {
                        num: "01",
                        value: "普通家庭财产保险1"
                    },
                    {
                        num: "02",
                        value: "普通家庭财产保险2"
                    },
                    {
                        num: "03",
                        value: "普通家庭财产保险3"
                    },
                    {
                        num: "04",
                        value: "普通家庭财产保险财产保险1"
                    },
                    {
                        num: "05",
                        value: "普通家庭财产保险财产保险2"
                    },
                    {
                        num: "06",
                        value: "普通家庭财产保险财产保险3"
                    },
                    {
                        num: "07",
                        value: "普通家庭财产保险4"
                    },
                    {
                        num: "08",
                        value: "普通家庭财产保险财产保险4"
                    },
                    {
                        num: "09",
                        value: "普通家庭财产保险5"
                    },
                    {
                        num: "10",
                        value: "普通家庭财产保险财产保险5"
                    }
                ],
                insuranceShow1: "",
                insuranceShow2: "",
                insuranceShow3: "",
                insurance1: [
                    {
                        num: "01",
                        value: "普通家庭财产保险1"
                    },
                    {
                        num: "02",
                        value: "普通家庭财产保险财产保险3普通家庭财产保险财产保险"
                    },
                    {
                        num: "03",
                        value: "普通家庭财产保险3"
                    }
                ],
                options: [
                    {
                        label: "热门城市",
                        options: [
                            {
                                value: "Shanghai",
                                label: "上海"
                            },
                            {
                                value: "Beijing",
                                label: "北京"
                            }
                        ]
                    },
                    {
                        label: "城市名",
                        options: [
                            {
                                value: "Chengdu",
                                label: "成都"
                            },
                            {
                                value: "Shenzhen",
                                label: "深圳"
                            },
                            {
                                value: "Guangzhou",
                                label: "广州"
                            },
                            {
                                value: "Dalian",
                                label: "大连"
                            }
                        ]
                    }
                ],
                optionsValue: ""
            },
            /* 下拉选择器 - 模糊搜索兼下拉选择 */
            fuzzySearch: {
                fuzzynn: "",
                fuzzy: "",
                options: [
                    {
                        value: "选项1",
                        label: "黄金糕"
                    },
                    {
                        value: "选项2",
                        label: "双皮奶"
                    },
                    {
                        value: "选项3",
                        label: "蚵仔煎"
                    },
                    {
                        value: "选项4",
                        label: "龙须面"
                    },
                    {
                        value: "选项5",
                        label: "北京烤鸭"
                    }
                ]
            },
            /* 下拉选择器 - 不可用状态 */
            disabledOper: {
                disabledOper: "",
                validStatus: "",
                options: [
                    {
                        value: "选项1",
                        label: "黄金糕"
                    },
                    {
                        value: "选项2",
                        label: "双皮奶"
                    },
                    {
                        value: "选项3",
                        label: "蚵仔煎"
                    },
                    {
                        value: "选项4",
                        label: "龙须面"
                    },
                    {
                        value: "选项5",
                        label: "北京烤鸭"
                    }
                ]
            },
            /* 下拉选择器 - 级联选择 */
            cascadeSelection: {
                options: [
                    {
                        value: "zhinan",
                        label: "指南",
                        children: [
                            {
                                value: "shejiyuanze",
                                label: "设计原则",
                                children: [
                                    {
                                        value: "yizhi",
                                        label: "一致"
                                    },
                                    {
                                        value: "fankui",
                                        label: "反馈"
                                    },
                                    {
                                        value: "xiaolv",
                                        label: "效率"
                                    },
                                    {
                                        value: "kekong",
                                        label: "可控"
                                    }
                                ]
                            },
                            {
                                value: "daohang",
                                label: "导航",
                                children: [
                                    {
                                        value: "cexiangdaohang",
                                        label: "侧向导航"
                                    },
                                    {
                                        value: "dingbudaohang",
                                        label: "顶部导航"
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        value: "zujian",
                        label: "组件",
                        children: [
                            {
                                value: "basic",
                                label: "Basic",
                                children: [
                                    {
                                        value: "layout",
                                        label: "Layout 布局"
                                    },
                                    {
                                        value: "color",
                                        label: "Color 色彩"
                                    },
                                    {
                                        value: "typography",
                                        label: "Typography 字体"
                                    },
                                    {
                                        value: "icon",
                                        label: "Icon 图标"
                                    },
                                    {
                                        value: "button",
                                        label: "Button 按钮"
                                    }
                                ]
                            },
                            {
                                value: "form",
                                label: "Form",
                                children: [
                                    {
                                        value: "radio",
                                        label: "Radio 单选框"
                                    },
                                    {
                                        value: "checkbox",
                                        label: "Checkbox 多选框"
                                    },
                                    {
                                        value: "input",
                                        label: "Input 输入框"
                                    },
                                    {
                                        value: "input-number",
                                        label: "InputNumber 计数器"
                                    },
                                    {
                                        value: "select",
                                        label: "Select 选择器"
                                    },
                                    {
                                        value: "cascader",
                                        label: "Cascader 级联选择器"
                                    },
                                    {
                                        value: "switch",
                                        label: "Switch 开关"
                                    },
                                    {
                                        value: "slider",
                                        label: "Slider 滑块"
                                    },
                                    {
                                        value: "time-picker",
                                        label: "TimePicker 时间选择器"
                                    },
                                    {
                                        value: "date-picker",
                                        label: "DatePicker 日期选择器"
                                    },
                                    {
                                        value: "datetime-picker",
                                        label: "DateTimePicker 日期时间选择器"
                                    },
                                    {
                                        value: "uploadIDcard.png",
                                        label: "Upload 上传"
                                    },
                                    {
                                        value: "rate",
                                        label: "Rate 评分"
                                    },
                                    {
                                        value: "form",
                                        label: "Form 表单"
                                    }
                                ]
                            },
                            {
                                value: "data",
                                label: "Data",
                                children: [
                                    {
                                        value: "table",
                                        label: "Table 表格"
                                    },
                                    {
                                        value: "tag",
                                        label: "Tag 标签"
                                    },
                                    {
                                        value: "progress",
                                        label: "Progress 进度条"
                                    },
                                    {
                                        value: "tree",
                                        label: "Tree 树形控件"
                                    },
                                    {
                                        value: "pagination",
                                        label: "Pagination 分页"
                                    },
                                    {
                                        value: "badge",
                                        label: "Badge 标记"
                                    }
                                ]
                            },
                            {
                                value: "notice",
                                label: "Notice",
                                children: [
                                    {
                                        value: "alert",
                                        label: "Alert 警告"
                                    },
                                    {
                                        value: "loading",
                                        label: "Loading 加载"
                                    },
                                    {
                                        value: "message",
                                        label: "Message 消息提示"
                                    },
                                    {
                                        value: "message-box",
                                        label: "MessageBox 弹框"
                                    },
                                    {
                                        value: "notification",
                                        label: "Notification 通知"
                                    }
                                ]
                            },
                            {
                                value: "navigation",
                                label: "Navigation",
                                children: [
                                    {
                                        value: "menu",
                                        label: "NavMenu 导航菜单"
                                    },
                                    {
                                        value: "tabs",
                                        label: "Tabs 标签页"
                                    },
                                    {
                                        value: "breadcrumb",
                                        label: "Breadcrumb 面包屑"
                                    },
                                    {
                                        value: "dropdown",
                                        label: "Dropdown 下拉菜单"
                                    },
                                    {
                                        value: "steps",
                                        label: "Steps 步骤条"
                                    }
                                ]
                            },
                            {
                                value: "others",
                                label: "Others",
                                children: [
                                    {
                                        value: "dialog",
                                        label: "Dialog 对话框"
                                    },
                                    {
                                        value: "tooltip",
                                        label: "Tooltip 文字提示"
                                    },
                                    {
                                        value: "popover",
                                        label: "Popover 弹出框"
                                    },
                                    {
                                        value: "card",
                                        label: "Card 卡片"
                                    },
                                    {
                                        value: "carousel",
                                        label: "Carousel 走马灯"
                                    },
                                    {
                                        value: "collapse",
                                        label: "Collapse 折叠面板"
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        value: "ziyuan",
                        label: "资源",
                        children: [
                            {
                                value: "axure",
                                label: "Axure Components"
                            },
                            {
                                value: "sketch",
                                label: "Sketch Templates"
                            },
                            {
                                value: "jiaohu",
                                label: "组件交互文档"
                            }
                        ]
                    }
                ],
                selectedOptions: [],
                selectedOptions1: []
            },
            datePicker: {
                datePicker1: ""
            },
            timePicker: {
                timePicker1: "",
                timePicker2: "",
                timePicker3: ""
            },
            /* 下拉选择器与输入框结合 */
            inputGroup: [
                {
                    num: "01",
                    value: "角色一"
                },
                {
                    num: "02",
                    value: "角色二"
                },
                {
                    num: "03",
                    value: "角色三"
                }
            ],
            rules: {
                selectedOptiona: [{ required: true, message: "模糊搜索兼下拉选择", trigger: "blur" }],
                fuzzynn: [{ required: true, message: "模糊搜索兼下拉选择", trigger: "blur" }],
                typeCodea: [
                    {
                        required: true,
                        message: "请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码",
                        trigger: "blur"
                    }
                ],
                typeCode: [{ required: true, message: "请输入类型代码", trigger: "blur" }],
                foldLinea: [{ required: true, message: "请输入带提示说明输入框请输入带提示说明输入框", trigger: "blur" }],
                foldLine: [{ required: true, message: "请输入带提示说明输入框请输入带提示说明输入框", trigger: "blur" }],
                tipInputa: [
                    { required: true, message: "请输入带提示说明输入框请输入带提示说明输入框请输入带提示说明输入框请输入带提示说明输入框", trigger: "blur" }
                ],
                tipInput: [{ required: true, message: "请输入带提示说明输入框", trigger: "blur" }],
                btnInputBox: [{ required: true, message: "点击选择产品方案", trigger: "blur" }],
                iconBtnInputBox: [{ required: true, message: "点击选择产品方案", trigger: "blur" }],
                insuranceShow: [{ required: true, message: "请选择效力状态", trigger: "blur" }],
                fuzzySearch: [{ required: true, message: "模糊搜索兼下拉选择", trigger: "blur" }],
                disabledOper: [{ required: true, message: "请选择", trigger: "blur" }],
                resource: [{ required: true, message: "请选择关系人类型", trigger: "blur" }],
                types: [{ required: true, message: "请选择", trigger: "blur" }],
                num: [{ required: true, message: "请选择", trigger: "blur" }],
                delivery: [{ required: true, message: "请选择", trigger: "blur" }],
                inputGroup: [{ required: true, message: "请输入", trigger: "blur" }],
                inputGroupzz: [{ required: true, message: "请输入", trigger: "blur" }],
                inputReq: [{ required: true, message: "请选择-下拉选择器与输入框组合", trigger: "blur" }]
            },
            /* dialog弹框数据 - start */
            gridData: [
                {
                    date: "2016-05-02",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-04",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-01",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-03",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                }
            ],
            dialogFormVisible: false,
            /* dialog弹框数据 - end */
            number: 60,
            tabs_state: [
                { size: "", title: "立案申请", type: "status", num: 0, selected: true, state: "success" },
                { size: "", title: "查勘定损", type: "status", num: 2, selected: false, state: "reject" },
                { size: "", title: "领款人登记", type: "status", num: 5, selected: false, state: "waiting" },
                { size: "", title: "理算申请", type: "status", num: 5, selected: false, state: "" },
                { size: "", title: "一站式案件处理", type: "status", num: 5, selected: false, state: "" }
            ],
            tabs_addItem: [
                { title: "第三方机构", type: "addItem" },
                { title: "关键环节管控", type: "addItem" },
                { title: "保险合同诉讼", type: "addItem" },
                { title: "预赔付", type: "addItem" },
                { title: "担保信息", type: "addItem" },
                { title: "损余录入", type: "addItem" },
                { title: "残值处理", type: "addItem" },
                { title: "追偿发起", type: "addItem" },
                { title: "查勘定损", type: "addItem", state: "reject" },
                { title: "领款人登记", type: "addItem", state: "chose" }
            ],
            add: false,
            radio1: "上海"
        };
    },
    mounted() {
        console.log(this.toolbarStatus);
    }
};
</script>
