<template>
    <div>
        <!-- PNG，GIF，CHINES，ARITHMETIC -->
        <captcha :format="'row'" :image-data="ARITHMETIC_data" @currentData="ARITHMETICcurrentData" @refresh="ARITHMETICrefresh" />
        <el-button class="item" type="primary" @click="checkResult('891281928', 'ARITHMETIC')">
            校验
        </el-button>
        <div class="item" style="line-height: 50px">
            验证结果：<i
                v-show="ARITHMETIC_flag"
                :class="ARITHMETIC_check_result ? 'el-icon-success' : 'el-icon-error'"
                :style="ARITHMETIC_check_result ? 'color: green; font-size: 30px' : 'color: red; font-size: 30px'"
            ></i>
        </div>
        <el-divider />
        <captcha :format="'column'" :image-data="PNG_data" @currentData="PNGcurrentData" @refresh="PNGrefresh" />
        <el-button class="item" type="primary" @click="checkResult('891281929', 'PNG')">
            校验
        </el-button>
        <div class="item" style="line-height: 50px">
            验证结果：<i
                v-show="PNG_flag"
                :class="PNG_check_result ? 'el-icon-success' : 'el-icon-error'"
                :style="PNG_check_result ? 'color: green; font-size: 30px' : 'color: red; font-size: 30px'"
            ></i>
        </div>
        <el-divider />
        <captcha :format="'row'" :image-data="GIF_data" @currentData="GIFcurrentData" @refresh="GIFrefresh" />
        <el-button class="item" type="primary" @click="checkResult('891281930', 'GIF')">
            校验
        </el-button>
        <div class="item" style="line-height: 50px">
            验证结果：<i
                v-show="GIF_flag"
                :class="GIF_check_result ? 'el-icon-success' : 'el-icon-error'"
                :style="GIF_check_result ? 'color: green; font-size: 30px' : 'color: red; font-size: 30px'"
            ></i>
        </div>
        <el-divider />
        <captcha :format="'column'" :image-data="CHINES_data" @currentData="CHINEScurrentData" @refresh="CHINESrefresh" />
        <el-button class="item" type="primary" @click="checkResult('891281931', 'CHINES')">
            校验
        </el-button>
        <div class="item" style="line-height: 50px">
            验证结果：<i
                v-show="CHINES_flag"
                :class="CHINES_check_result ? 'el-icon-success' : 'el-icon-error'"
                :style="CHINES_check_result ? 'color: green; font-size: 30px' : 'color: red; font-size: 30px'"
            ></i>
        </div>
        <el-divider />
        <div class="container">
            <verify ref="verifition" mode="pop" captcha-type="blockPuzzle" :img-size="{ width: '320px', height: '140px' }" @success="success" />
            <button @click="useverifition">
                风险校验滑动验证
            </button>
            <verify
                ref="verifitionDefault"
                mode="fixed"
                :default-display="false"
                captcha-type="blockPuzzle"
                :img-size="{ width: '320px', height: '140px' }"
                @success="success"
            />
        </div>
        <el-divider />
        <div class="container">
            <verify ref="verifitionclick" mode="pop" captcha-type="clickWord" :img-size="{ width: '320px', height: '140px' }" @success="success" />
            <button @click="useverifitionclick">
                文字点选验证
            </button>
        </div>
    </div>
</template>

<script>
// import verifition from "@/components/verifition/verifition.vue";
// import { verifition, captcha } from "@picc/verifition";
// import "verifition.css";
import { queryCaptchaApi, checkResultApi } from "@/api/captcha";
import axios from "axios";
// import Verify from "../../components/verifition/Verify.vue";
export default {
    // components: { Verify },
    // components: {
    //     verifition,
    //     captcha
    // },
    data() {
        return {
            ARITHMETIC_data: "",
            ARITHMETIC_result: "",
            ARITHMETIC_flag: false,
            ARITHMETIC_check_result: true,
            PNG_data: "",
            PNG_result: "",
            PNG_flag: false,
            PNG_check_result: true,
            GIF_data: "",
            GIF_result: "",
            GIF_flag: false,
            GIF_check_result: true,
            CHINES_data: "",
            CHINES_result: "",
            CHINES_flag: false,
            CHINES_check_result: true
        };
    },
    mounted() {
        this.queryCaptcha({ uuid: "891281928", type: "ARITHMETIC" });
        this.queryCaptcha({ uuid: "891281929", type: "PNG" });
        this.queryCaptcha({ uuid: "891281930", type: "GIF" });
        this.queryCaptcha({ uuid: "891281931", type: "CHINES" });
    },
    methods: {
        queryCaptcha(query) {
            queryCaptchaApi(query).then((res) => {
                console.log(res);
                let imageType = res.headers["content-type"]; //获取图片类型
                const blob = new Blob([res.data], { type: imageType });
                console.log(blob);
                const imageUrl = (window.URL || window.webkitURL).createObjectURL(blob);
                this[query.type + "_data"] = imageUrl;
            });
        },
        checkResult(uuid, resultKey) {
            checkResultApi({ uuid: uuid, captchaCode: this[resultKey + "_result"] }).then((res) => {
                this[resultKey + "_flag"] = true;
                this[resultKey + "_check_result"] = res.data.data;
            });
        },
        //刷新
        ARITHMETICrefresh() {
            this.queryCaptcha({ uuid: "891281928", type: "ARITHMETIC" });
        },
        PNGrefresh() {
            this.queryCaptcha({ uuid: "891281929", type: "PNG" });
        },
        GIFrefresh() {
            this.queryCaptcha({ uuid: "891281930", type: "GIF" });
        },
        CHINESrefresh() {
            this.queryCaptcha({ uuid: "891281931", type: "CHINES" });
        },
        //组件值获取
        ARITHMETICcurrentData(data) {
            this.ARITHMETIC_result = data;
        },
        PNGcurrentData(data) {
            this.PNG_result = data;
        },
        GIFcurrentData(data) {
            this.GIF_result = data;
        },
        CHINEScurrentData(data) {
            this.CHINES_result = data;
        },
        success(params) {
            // params 返回的二次验证参数, 和登录参数一起回传给登录接口，方便后台进行二次验证
        },
        useverifition() {
            this.$refs.verifition.show();
        },
        useverifitionclick() {
            this.$refs.verifitionclick.show();
        }
    }
};
</script>
<style>
.container {
    padding: 10px 20px;
    display: -webkit-flex; /* Safari */
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}
.item {
    margin-left: 20px;
}
</style>