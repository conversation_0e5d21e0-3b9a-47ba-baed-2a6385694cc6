<template>
    <div class="app-container enplan_l">
        <el-card shadow="always" class="bor_bon">
            <div style="width: 100%;border: 0px;">
                <div>
                    <div class="moduleContent">
                        <el-form ref="form" :model="planData[0]">
                            <el-table :data="planData[0].prpCplan" highlight-current-row style="width: 100%">
                                <el-table-column
                                    v-for="(item,index) in Data"
                                    :key="index"
                                    :label="item.label"
                                    :prop="item.prop"
                                    align="center"
                                >
                                    <template slot-scope="scope">
                                        <el-form-item 
                                            v-if="item.type==='data'"
                                            style="margin-bottom: 0px"
                                            :prop="'prpCplan.' + scope.$index + '.'+item.prop"
                                            :rules="[validator(item.label).required,{ validator: (rule,value,callback)=>planeditRules.common.changedata(rule, value,callback,planData[0].prpCplan[scope.$index],item.prop), trigger: 'blur' },{ validator: (rule,value,callback)=>planeditRules.common.changedata(rule, value,callback,planData[0].prpCplan[scope.$index],item.prop), trigger: 'change' }]"
                                        >
                                            <el-date-picker
                                                v-model="planData[0].prpCplan[scope.$index][item.prop]"
                                                value-format="yyyy-MM-dd"
                                                style="width: 100%;"
                                                :clearable="false"
                                                type="date"
                                                default-value="2010-01-01"
                                            />
                                        </el-form-item>
                                        <el-form-item v-if="item.type==='text'">
                                            <el-input v-model="planData[0].prpCplan[scope.$index][item.prop]" readonly />
                                        </el-form-item>
                                        <el-form-item v-if="item.type==='input'">
                                            <el-input v-model="planData[0].prpCplan[scope.$index][item.prop]" />
                                        </el-form-item>
                                        <el-form-item v-if="item.type==='currency'">
                                            <el-select v-model="planData[0].prpCplan[scope.$index][item.prop]" placeholder="璇烽€夋嫨">
                                                <el-option
                                                    v-for="items in optionsd"
                                                    :key="items.value"
                                                    :label="items.label"
                                                    :value="items.value"
                                                />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item
                                            v-if="item.type==='money'"
                                            style="margin-bottom: 0px"
                                            :prop="'prpCplan.' + scope.$index + '.'+item.prop"
                                            :rules="[validator(item.label).required,
                                                     { validator: (rule,value,callback)=>planeditRules.common.changeplanFee(rule, value,callback,planData[0].prpCplan[scope.$index],item.prop,$parent.planFee), trigger: 'blur' },
                                                     { validator: (rule,value,callback)=>planeditRules.common.changeplanFee(rule, value,callback,planData[0].prpCplan[scope.$index],item.prop,$parent.planFee), trigger: 'change' }
                                            ]"
                                        >
                                            <el-input v-model="planData[0].prpCplan[scope.$index][item.prop]" @change="countchangebymoney(scope.$index)">
                                                <!--                  <span slot="suffix">鍏?/span>-->
                                            </el-input>
                                        </el-form-item>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form>
                    </div>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script>
export default {
    data() {
        return {

        };
    },
    methods: {

    }
};
</script>
