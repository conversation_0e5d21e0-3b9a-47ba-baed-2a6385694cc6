<template>
    <div style="padding:30px;">
        <h1 style="color:red;font-size:16px;">
            不可用状态3种交互说明
        </h1>
        <div class="words">
            <span style="font-weight:bold;font-size:16px;color:#000;">消失规则：</span><br />
            1>简单明了的不做气泡反馈 <br />
            2>复杂的需提示的做气泡提示 <br />
            &nbsp;&nbsp;&nbsp;&nbsp;a 根据气泡文本提示进行操作，气泡消失 <br />
            &nbsp;&nbsp;&nbsp;&nbsp;b 鼠标移至气泡以外的任何区域，气泡消失 <br />
            &nbsp;&nbsp;&nbsp;&nbsp;c 对气泡不进行任何操作，3秒后气泡消失 <br />
        </div>
        <div style="margin-bottom:30px"></div>

        <div class="picc-el-form">
            <el-form ref="dialogBox" :model="selectOper" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="案例---禁用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-popover v-model="selectOper.popover" placement="top" trigger="manual" popper-class="picc-popover-custom">
                                <span><i class="picc-icon picc-icon-info"></i>禁用提示信息popover</span>
                                <div slot="reference" style="cursor: not-allowed;" @click="popoverisShow()">
                                    <el-input
                                        v-model="selectOper.demo"
                                        disabled
                                        style="pointer-events:none;"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                    />
                                </div>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="案例---禁用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-popover v-model="selectOper.popover1" placement="top" trigger="manual" popper-class="picc-popover-custom">
                                <span><i class="picc-icon picc-icon-info"></i>禁用提示信息popover1</span>
                                <div slot="reference" style="cursor: not-allowed;" @click="selectOper.popover1 = !selectOper.popover1">
                                    <el-select v-model="selectOper.demo1" disabled style="pointer-events:none;" placeholder="请选择">
                                        <el-option
                                            v-for="(item, index) in selectOper.insurance"
                                            :key="index"
                                            :label="`${item.num}-${item.value}`"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </div>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="3秒后气泡消失 - click popover" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-popover v-model="selectOper.popover2" placement="top" trigger="manual" popper-class="picc-popover-custom">
                                <span><i class="picc-icon picc-icon-info"></i>对气泡不进行任何操作，3秒后气泡消失</span>
                                <div slot="reference" style="cursor: not-allowed;" @click="popoverisShow2">
                                    <el-input
                                        v-model="selectOper.demo2"
                                        disabled
                                        style="pointer-events:none;"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                    />
                                </div>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="鼠标移至气泡外 - click popover" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-popover
                                v-model="selectOper.popover3"
                                placement="top"
                                trigger="manual"
                                popper-class="picc-popover-custom"
                                @mouseover="handelMouseover"
                                @mouseleave="handelMouseleave"
                            >
                                <span><i class="picc-icon picc-icon-info"></i>鼠标移至气泡以外的任何区域，气泡消失</span>
                                <div slot="reference" style="cursor: not-allowed;" @click="popoverisShow3">
                                    <el-input
                                        v-model="selectOper.demo3"
                                        disabled
                                        style="pointer-events:none;"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                    />
                                </div>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="鼠标移至气泡外 - click tooltip" class="picc-form-label-linefeed picc-linefeed--short">
                            <el-tooltip v-model="visible1" placement="top" trigger="manual" :manual="true">
                                <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip-info">
                                    <p>鼠标移至气泡以外的任何区域，气泡消失</p>
                                </div>
                                <div style="cursor: not-allowed;" @click="visible1 = !visible1">
                                    <el-input
                                        v-model="selectOper.demo4"
                                        disabled
                                        style="pointer-events:none;"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                    />
                                </div>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="鼠标移至气泡外 - hover tooltip" class="picc-form-label-linefeed picc-linefeed--long">
                            <el-tooltip content="鼠标移至气泡以外的任何区域，气泡消失" placement="top">
                                <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip-info">
                                    <p>鼠标移至气泡以外的任何区域，气泡消失</p>
                                </div>
                                <div style="cursor: not-allowed;">
                                    <el-input
                                        v-model="selectOper.demo5"
                                        disabled
                                        style="pointer-events:none;"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                    />
                                </div>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="根据气泡文本提示操作 - click pop" class="picc-form-label-linefeed picc-linefeed--long">
                            <el-popover v-model="selectOper.popover6" placement="top" trigger="manual" popper-class="picc-popover-custom">
                                <el-form ref="form" :model="form" :rules="rules">
                                    <el-form-item label="活动名称" :label-width="formLabelWidth" prop="name">
                                        <el-input v-model="form.name" autocomplete="off" />
                                    </el-form-item>
                                    <el-form-item label="活动区域" :label-width="formLabelWidth">
                                        <el-select v-model="form.region" placeholder="请选择活动区域">
                                            <el-option label="区域一" value="shanghai" />
                                            <el-option label="区域二" value="beijing" />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="活动名称" :label-width="formLabelWidth">
                                        <el-button @click="closeBox">
                                            点击校验表单
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                                <div slot="reference" style="cursor: not-allowed;" @click="selectOper.popover6 = !selectOper.popover6">
                                    <el-input
                                        v-model="selectOper.demo6"
                                        disabled
                                        style="pointer-events:none;"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                    />
                                </div>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="24" style="text-align:center;">
                        <el-button @click="subForm">
                            点击校验表单
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div style="margin-bottom:30px"></div>
    </div>
</template>

<script>
export default {
    name: "Index927",
    data() {
        return {
            status1: true,
            status2: false,

            visible1: false,
            /* 下拉选择器 - 禁用状态 */
            selectOper: {
                insurance: [
                    {
                        num: "01",
                        value: "普通家庭财产保险1"
                    },
                    {
                        num: "02",
                        value: "普通家庭财产保险2"
                    },
                    {
                        num: "03",
                        value: "普通家庭财产保险3"
                    },
                    {
                        num: "04",
                        value: "普通家庭财产保险财产保险4"
                    },
                    {
                        num: "05",
                        value: "普通家庭财产保险财产保险5"
                    },
                    {
                        num: "06",
                        value: "普通家庭财产保险财产保险6"
                    },
                    {
                        num: "07",
                        value: "普通家庭财产保险财产保险普通家庭财产保险财产保险7"
                    },
                    {
                        num: "08",
                        value: "普通家庭财产保险财产保险普通家庭财产保险财产保险8"
                    },
                    {
                        num: "09",
                        value: "普通家庭财产保险财产保险普通家庭财产保险财产保险9"
                    }
                ],
                demo: "",
                popover: false,
                demo1: "",
                popover1: false,
                demo2: "",
                popover2: false,
                demo3: "",
                popover3: false,
                demo4: "",
                demo5: "",
                demo6: "",
                popover6: false
            },

            dialogFormVisible: false,
            form: {
                name: "",
                region: "",
                date1: "",
                date2: "",
                delivery: false,
                type: [],
                resource: "",
                desc: ""
            },
            formLabelWidth: "100px",
            rules: {
                name: [
                    { required: true, message: "请输入活动名称", trigger: "blur" },
                    { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" }
                ]
            }
        };
    },
    watch: {
        // status1(newName, oldName) {
        //     if(this.status1 || this.status2){
        //        console.log(this.status1 +"      "+ this.status2)
        //     }else{
        //         this.selectOper.popover3 = !this.selectOper.popover3;
        //     }
        // },
        // status2(newName, oldName) {
        //     if(this.status1 || this.status2){
        //        console.log(this.status1 +"      "+ this.status2)
        //     }else{
        //         this.selectOper.popover3 = !this.selectOper.popover3;
        //     }
        // },
    },
    methods: {
        popoverisShow() {
            this.selectOper.popover = !this.selectOper.popover;
        },

        popoverisShow2() {
            const _this = this;
            this.selectOper.popover2 = !this.selectOper.popover2;
            setTimeout(function() {
                _this.selectOper.popover2 = !_this.selectOper.popover2;
                // console.log("结束");
            }, 3000);
        },

        popoverisShow3(el) {
            this.selectOper.popover3 = !this.selectOper.popover3;
        },
        handelMouseover(el) {
            el.target.addEventListener("mouseover", function(ev) {
                this.status2 = true;
            });
            alert(1);
        },
        handelMouseleave(el) {
            alert(2);
            const _this = this;
            el.target.addEventListener("mouseover", function(ev) {
                _this.status2 = false;
            });
            if (_this.status2 === false) {
                setTimeout(function() {
                    if (_this.status1 || _this.status2) {
                        // console.log(_this.status1 + "      " + _this.status2);
                    } else {
                        _this.selectOper.popover3 = !_this.selectOper.popover3;
                    }
                }, 500);
            }
        },
        ccc(el) {
            alert(3);
            el.target.addEventListener("mouseover", function(ev) {
                this.status1 = true;
            });
        },
        ddd(el) {
            alert(4);
            const _this = this;
            el.target.addEventListener("mouseover", function(ev) {
                _this.status1 = false;
            });

            if (_this.status1 === false) {
                setTimeout(function() {
                    if (_this.status1 || _this.status2) {
                        // console.log(_this.status1 + "      " + _this.status2);
                    } else {
                        _this.selectOper.popover3 = !_this.selectOper.popover3;
                    }
                }, 500);
            }
        },

        closeBox() {
            const _this = this;
            this.$refs.form.validate((valid) => {
                if (valid) {
                    alert("submit!");
                    _this.selectOper.popover6 = !_this.selectOper.popover6;
                } else {
                    // console.log("error submit!!");
                    return false;
                }
            });
        },

        subForm() {
            // console.log(this.selectOper.optionsValue1);
            this.$refs.dialogBox.validate((valid) => {
                if (valid) {
                    alert("submit!");
                } else {
                    // console.log("error submit!!");
                    return false;
                }
            });
        },

        inputFocus: function(target) {
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        },
        inputBlur: function(target) {
            // 部分表单校验
            this.$refs.dialogBox.validateField(target);
        }
    }
};
</script>

<style lang="scss" scoped>
.words {
    color: red;
    font-size: 14px;
    padding: 10px 5px;
    border: 1px solid #cdcdcd;
    margin-bottom: 30px;
}
</style>
