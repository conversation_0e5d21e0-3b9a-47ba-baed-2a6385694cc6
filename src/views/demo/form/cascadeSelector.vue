<template>
    <div style="padding:30px;">
        <!-- 级联选择器 -->
        <div class="picc-container-box">
            <div class="picc-container">
                <!-- 内页卡片内标题栏 -->
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        客户信息01
                    </div>
                    <div class="picc-operation-area">
                        <span class="picc-operation-superior"><i class="el-icon-delete-solid"></i></span>
                    </div>
                </div>

                <div class="picc-el-form">
                    <el-form :model="formBox" label-width="120px" :rules="rules">
                        <el-row type="flex" class="row-bg">
                            <el-col :span="8">
                                <el-form-item label="下拉选择器-联级选择" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-cascader
                                        v-model="cascadeSelection.selectedOptions"
                                        :options="cascadeSelection.options"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="下拉选择器-联级disabled" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-cascader
                                        v-model="cascadeSelection.selectedOptions1"
                                        disabled
                                        :options="cascadeSelection.options"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex" class="row-bg">
                            <el-col :span="24">
                                <el-form-item label="联级选择" class="picc-form-cascadeSelector">
                                    <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                                        <el-option
                                            v-for="(item,index) in selectOper.insurance"
                                            :key="index"
                                            :label="`${item.num}-${item.value}`"
                                            :value="item.value"
                                        />
                                    </el-select>
                                    <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                                        <el-option
                                            v-for="(item,index) in selectOper.insurance"
                                            :key="index"
                                            :label="`${item.num}-${item.value}`"
                                            :value="item.value"
                                        />
                                    </el-select>
                                    <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                                        <el-option
                                            v-for="(item,index) in selectOper.insurance"
                                            :key="index"
                                            :label="`${item.num}-${item.value}`"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Index927',
    data() {
        return {
            formBox: {},
            /* 下拉选择器 */
            selectOper: {
                validStatus: '',
                cities: [
                    {
                        value: 'Beijing',
                        label: '北京'
                    }, {
                        value: 'Shanghai',
                        label: '上海'
                    }, {
                        value: 'Nanjing',
                        label: '南京'
                    }, {
                        value: 'Chengdu',
                        label: '成都'
                    }, {
                        value: 'Shenzhen',
                        label: '深圳'
                    }, {
                        value: 'Guangzhou',
                        label: '广州'
                    }],
                insuranceShow: '',
                insurance: [
                    {
                        num: '01',
                        value: '普通家庭财产保险1'
                    },
                    {
                        num: '02',
                        value: '普通家庭财产保险财产保险333333333333333333333333333333333333333333332'
                    },
                    {
                        num: '03',
                        value: '普通家庭财产保险3'
                    },
                    {
                        num: '04',
                        value: '普通家庭财产保险财产保险4'
                    },
                    {
                        num: '05',
                        value: '普通家庭财产保险5'
                    },
                    {
                        num: '06',
                        value: '普通家庭财产保险财产保险6'
                    },
                    {
                        num: '07',
                        value: '普通家庭财产保险7'
                    },
                    {
                        num: '08',
                        value: '普通家庭财产保险财产保险8'
                    },
                    {
                        num: '09',
                        value: '普通家庭财产保险9'
                    },
                    {
                        num: '10',
                        value: '普通家庭财产保险财产保险10'
                    },
                    {
                        num: 'cursordisabled.jpg',
                        value: '普通家庭财产保险11'
                    },
                    {
                        num: '12',
                        value: '普通家庭财产保险财产保险12'
                    }
                ],
                insuranceShow1: '',
                insurance1: [
                    {
                        label: '热门城市',
                        options: [
                            {
                                value: 'Shanghai',
                                label: '上海'
                            }, {
                                value: 'Beijing',
                                label: '北京'
                            }]
                    }, {
                        label: '城市名',
                        options: [
                            {
                                value: 'Chengdu',
                                label: '成都'
                            }, {
                                value: 'Shenzhen',
                                label: '深圳'
                            }, {
                                value: 'Guangzhou',
                                label: '广州'
                            }, {
                                value: 'Dalian',
                                label: '大连'
                            }]
                    }]
            },
            /* 下拉选择器 - 级联选择 */
            cascadeSelection: {
                options: [{
                    value: 'zhinan',
                    label: '指南',
                    children: [{
                        value: 'shejiyuanze',
                        label: '设计原则',
                        children: [{
                            value: 'yizhi',
                            label: '一致'
                        }, {
                            value: 'fankui',
                            label: '反馈'
                        }, {
                            value: 'xiaolv',
                            label: '效率'
                        }, {
                            value: 'kekong',
                            label: '可控'
                        }]
                    }, {
                        value: 'daohang',
                        label: '导航',
                        children: [{
                            value: 'cexiangdaohang',
                            label: '侧向导航'
                        }, {
                            value: 'dingbudaohang',
                            label: '顶部导航'
                        }]
                    }]
                }, {
                    value: 'zujian',
                    label: '组件',
                    children: [{
                        value: 'basic',
                        label: 'Basic',
                        children: [{
                            value: 'layout',
                            label: 'Layout 布局'
                        }, {
                            value: 'color',
                            label: 'Color 色彩'
                        }, {
                            value: 'typography',
                            label: 'Typography 字体'
                        }, {
                            value: 'icon',
                            label: 'Icon 图标'
                        }, {
                            value: 'button',
                            label: 'Button 按钮'
                        }]
                    }, {
                        value: 'form',
                        label: 'Form',
                        children: [{
                            value: 'radio',
                            label: 'Radio 单选框'
                        }, {
                            value: 'checkbox',
                            label: 'Checkbox 多选框'
                        }, {
                            value: 'input',
                            label: 'Input 输入框'
                        }, {
                            value: 'input-number',
                            label: 'InputNumber 计数器'
                        }, {
                            value: 'select',
                            label: 'Select 选择器'
                        }, {
                            value: 'cascader',
                            label: 'Cascader 级联选择器'
                        }, {
                            value: 'switch',
                            label: 'Switch 开关'
                        }, {
                            value: 'slider',
                            label: 'Slider 滑块'
                        }, {
                            value: 'time-picker',
                            label: 'TimePicker 时间选择器'
                        }, {
                            value: 'date-picker',
                            label: 'DatePicker 日期选择器'
                        }, {
                            value: 'datetime-picker',
                            label: 'DateTimePicker 日期时间选择器'
                        }, {
                            value: 'uploadIDcard.png',
                            label: 'Upload 上传'
                        }, {
                            value: 'rate',
                            label: 'Rate 评分'
                        }, {
                            value: 'form',
                            label: 'Form 表单'
                        }]
                    }, {
                        value: 'data',
                        label: 'Data',
                        children: [{
                            value: 'table',
                            label: 'Table 表格'
                        }, {
                            value: 'tag',
                            label: 'Tag 标签'
                        }, {
                            value: 'progress',
                            label: 'Progress 进度条'
                        }, {
                            value: 'tree',
                            label: 'Tree 树形控件'
                        }, {
                            value: 'pagination',
                            label: 'Pagination 分页'
                        }, {
                            value: 'badge',
                            label: 'Badge 标记'
                        }]
                    }, {
                        value: 'notice',
                        label: 'Notice',
                        children: [{
                            value: 'alert',
                            label: 'Alert 警告'
                        }, {
                            value: 'loading',
                            label: 'Loading 加载'
                        }, {
                            value: 'message',
                            label: 'Message 消息提示'
                        }, {
                            value: 'message-box',
                            label: 'MessageBox 弹框'
                        }, {
                            value: 'notification',
                            label: 'Notification 通知'
                        }]
                    }, {
                        value: 'navigation',
                        label: 'Navigation',
                        children: [{
                            value: 'menu',
                            label: 'NavMenu 导航菜单'
                        }, {
                            value: 'tabs',
                            label: 'Tabs 标签页'
                        }, {
                            value: 'breadcrumb',
                            label: 'Breadcrumb 面包屑'
                        }, {
                            value: 'dropdown',
                            label: 'Dropdown 下拉菜单'
                        }, {
                            value: 'steps',
                            label: 'Steps 步骤条'
                        }]
                    }, {
                        value: 'others',
                        label: 'Others',
                        children: [{
                            value: 'dialog',
                            label: 'Dialog 对话框'
                        }, {
                            value: 'tooltip',
                            label: 'Tooltip 文字提示'
                        }, {
                            value: 'popover',
                            label: 'Popover 弹出框'
                        }, {
                            value: 'card',
                            label: 'Card 卡片'
                        }, {
                            value: 'carousel',
                            label: 'Carousel 走马灯'
                        }, {
                            value: 'collapse',
                            label: 'Collapse 折叠面板'
                        }]
                    }]
                }, {
                    value: 'ziyuan',
                    label: '资源',
                    children: [{
                        value: 'axure',
                        label: 'Axure Components'
                    }, {
                        value: 'sketch',
                        label: 'Sketch Templates'
                    }, {
                        value: 'jiaohu',
                        label: '组件交互文档'
                    }]
                }],
                selectedOptions: [],
                selectedOptions1: []
            },
            rules: {
                insuranceShow: [{ required: true, message: '请选择', trigger: 'blur' }]
            }
        };
    },
    created() {
        // console.log('form_created');
    },
    methods: {
    /* input 标签获取/失去焦点 - 错误信息消失/显示 */
        inputFocus(target) {
            const $parent = document.getElementById(target).parentNode.parentNode;
            // const $root = $parent.parentNode
            const $target = $parent.lastChild;
            // eslint-disable-next-line eqeqeq
            if ($target.className == 'el-form-item__error') {
                // $root.style.marginBottom = '0px'
                $target.style.display = 'none';
            }
        },
        inputBlur(target) {
            const $parent = document.getElementById(target).parentNode.parentNode;
            // const $root = $parent.parentNode
            const $target = $parent.lastChild;
            // eslint-disable-next-line eqeqeq
            if ($target.className == 'el-form-item__error') {
                // $root.style.marginBottom = '52px'
                $target.style.display = 'block';
            }
        }
    }
};
</script>

<style scoped>

</style>
