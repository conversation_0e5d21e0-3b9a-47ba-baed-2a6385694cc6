<template>
    <div>
        <H3>form表单校验规则</H3>
        <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
            <el-row type="flex" class="row-bg">
                <el-col :span="8">
                    <el-form-item v-picc-input-error prop="typeCode" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input v-model="formBox.typeCode" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode')" @blur="blurFN('typeCode')" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" />
                <el-col :span="8">
                    <el-form-item v-picc-input-error prop="typeCode7" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input v-model="formBox.typeCode7" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode7')" @blur="blurFN('typeCode7')" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row type="flex" class="row-bg">
                <el-col :span="8">
                    <el-form-item v-picc-input-error prop="typeCode9" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input v-model="formBox.typeCode9" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode9')" @blur="blurFN('typeCode9')" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item v-picc-input-error prop="typeCode8" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input v-model="formBox.typeCode8" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode8')" @blur="blurFN('typeCode8')" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" />
            </el-row>
            <el-row type="flex" class="row-bg">
                <el-col :span="8">
                    <el-form-item prop="typeCode2" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input v-model="formBox.typeCode2" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode2')" @blur="blurFN('typeCode2')" />
                        <template slot="error">
                            <div class="picc-form-tip">
                                <div class="picc-form-tipmessage picc-form-tipmessage--error picc-form-tipmessage--textellipsis">
                                    <el-tooltip placement="right">
                                        <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip_error">
                                            <p>气泡 - 错误提示的文案示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文</p>
                                        </div>
                                        <i class="el-icon-warning-outline"></i>
                                    </el-tooltip>
                                    <span>气泡 - 错误提示的文案示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文</span>
                                </div>
                            </div>
                        </template>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item prop="typeCode3" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input ref="aaa" v-model="formBox.typeCode3" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode3')" @blur="blurFN('typeCode3')" />
                        <template slot="error">
                            <div class="picc-form-tip">
                                <div class="picc-form-tipmessage picc-form-tipmessage--error picc-form-tipmessage--textellipsis">
                                    <el-tooltip placement="right">
                                        <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip_error">
                                            <p>气泡 - 错误提示的文案示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文</p>
                                        </div>
                                        <i class="el-icon-warning-outline"></i>
                                    </el-tooltip>
                                    <span>气泡 - 错误提示的文案示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文</span>
                                </div>
                            </div>
                        </template>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item prop="typeCode2" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input ref="aaa" v-model="formBox.typeCode2" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode2')" @blur="blurFN('typeCode2')" />
                        <template slot="error">
                            <div class="picc-form-tip">
                                <div class="picc-form-tipmessage picc-form-tipmessage--error picc-form-tipmessage--textellipsis">
                                    <el-tooltip placement="right">
                                        <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip_error">
                                            <p>气泡 - 错误提示的文案示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文</p>
                                        </div>
                                        <i class="el-icon-warning-outline"></i>
                                    </el-tooltip>
                                    <span>气泡 - 错误提示的文案示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文</span>
                                </div>
                            </div>
                        </template>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row type="flex" class="row-bg">
                <el-col :span="8">
                    <el-form-item prop="typeCode4" label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                        <el-input ref="aaa" v-model="formBox.typeCode4" class="el-input-style" placeholder="输入文本信息" @focus="focusFN('typeCode4')" @blur="blurFN('typeCode4')" />
                        <template slot="error">
                            <div class="picc-form-tip">
                                <div class="picc-form-tipmessage picc-form-tipmessage--error picc-form-tipmessage--textellipsis">
                                    <el-tooltip placement="right" disabled>
                                        <div slot="content" class="picc-tooltip picc-tooltip-content picc-tooltip_error">
                                            <p>气泡 - 错误提示的文案示的文示的文</p>
                                        </div>
                                        <i class="el-icon-warning-outline"></i>
                                    </el-tooltip>
                                    <span>气泡 - 错误提示的文案示的文示的文</span>
                                </div>
                            </div>
                        </template>
                    </el-form-item>
                </el-col>
                <el-col :span="8" />
            </el-row>
        </el-form>
    </div>
</template>

<script>
export default {
    name: 'Text',
    data() {
        return {
            formBox: {
                typeCode: '',
                typeCode1: '',
                typeCode2: '',
                typeCode3: '',
                typeCode4: '',
                typeCode7: '',
                typeCode8: '',
                typeCode9: ''
            },
            rules: {
                typeCode: [
                    { required: true, message: `气泡 - 错误提示的文案错误提示的文案错误提示的文案错误提示的文案误提示的文案错误提示的文案错误提示的文案错误提示的文案错误提示的文案...`, trigger: 'blur' },
                    { min: 5, message: `气泡 - 错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡 - 错误提示的文案需要小于8个字`, trigger: 'blur' }],
                typeCode7: [
                    { required: true, message: `气泡7 - 错误提示的文案示的文示的文示的文示的文错误提示的文案错误提示的文案错误提示的文案错误提示的文案~~~`, trigger: 'blur' },
                    { min: 5, message: `气泡7 - 错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡7 - 错误提示的文案需要小于8个字`, trigger: 'blur' }],
                typeCode8: [
                    { required: true, message: `气泡8 - 错误提示的文案示的文示的文示的文~~~`, trigger: 'blur' },
                    { min: 5, message: `气泡8 - 错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡8 - 错误提示的文案需要小于8个字`, trigger: 'blur' }],
                typeCode9: [
                    { required: true, message: `气泡9 - 错误提示的文案示的文示的文示的文~~~`, trigger: 'blur' },
                    { min: 5, message: `气泡9 - 错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡9 - 错误提示的文案需要小于8个字`, trigger: 'blur' }],
                typeCode1: [
                    { required: true, message: `气泡1 - 错误提示的文案示的文示的文示的文示的文示的文示的`, trigger: 'blur' },
                    { min: 5, message: `气泡1 - 错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡1 - 错误提示的文案需要小于8个字`, trigger: 'blur' }],
                typeCode2: [
                    { required: true, message: `气泡2 - 示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文示的文`, trigger: 'blur' },
                    { min: 5, message: `气泡2 - 111错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡2 - 111错误提示的文案需要小于8个字`, trigger: 'blur' }],
                typeCode3: [
                    { required: true, message: '气泡3 - 错误提示的文案示的文示的文', trigger: 'blur' },
                    { min: 5, message: `气泡3 - 错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡3 - 错误提示的文案需要小于8个字`, trigger: 'blur' }],
                typeCode4: [
                    { required: true, message: '气泡4 - 错误提示的文案示的文示的文', trigger: 'blur' },
                    { min: 5, message: `气泡4 - 错误提示的文案需要大于5个字`, trigger: 'blur' },
                    { max: 8, message: `气泡4 - 错误提示的文案需要小于8个字`, trigger: 'blur' }]
            },
            newtitle: ''
        };
    },
    created: function() {
    // window.setInterval(this.init, 10000)
    },
    methods: {
        init() {
            const $tipTagert = document.querySelectorAll('.mytooltip');
            const $body = document.querySelector('body');
            const _this = this;
            const x = 10;
            const y = 20;
            for (let i = 0; i < $tipTagert.length; i++) {
                $tipTagert[i].onmouseover = function(e) {
                    _this.newtitle = this.title;
                    this.title = '';
                    $body.append('<div id="mytitle" >' + _this.newtitle + '</div>');
                    document.querySelector('#mytitle').style.left = e.pageX + x + 'px';
                    document.querySelector('#mytitle').style.top = e.pageY + y - 80 + 'px';
                }.onmouseout = function() {
                    this.title = this.newtitle;
                    document.querySelector('#mytitle').remove();
                }.onmousemove = function(e) {
                    document.querySelector('#mytitle').style.left = e.pageX + x + 10 + 'px';
                    document.querySelector('#mytitle').style.top = e.pageY + y - 60 + 'px';
                };
            }
        },
        blurFN: function(target) {
            // this.rules[target][0].required = true
        },
        focusFN: function(target) {
            // this.rules[target][0].required = false
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        }
    }
};
</script>

<style scoped>
</style>
