<template>
    <div style="padding:30px;">
        <h1 style="color:red;font-size:16px;">
            按钮输入框：1）按钮输入框 2）带图标按钮输入框
        </h1>
        <el-input
            v-model="formBox.iconBtnInput"
            class="picc-icon-btn-input casualClass"
            suffix-icon="el-icon-search"
            placeholder="带图标按钮输入框 - 旧版本"
        />
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.iconBtnInput1"
            disabled
            class="picc-icon-btn-input casualClass"
            suffix-icon="el-icon-search"
            placeholder="带图标按钮输入框 - 旧版本"
        />
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.iconBtnInputnew"
            placeholder="带图标按钮输入框 - 新版本"
            class="picc-icon-btn-input"
        >
            <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="test"
            >
            </i>
        </el-input>
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.iconBtnInputnew1"
            disabled
            placeholder="带图标按钮输入框 - 新版本"
            class="picc-icon-btn-input"
        >
            <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="test"
            >
            </i>
        </el-input>
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.btnInput"
            readonly
            class="picc-btn-input"
            placeholder="按钮输入框"
            unselectable="on"
        />
        <div style="margin-bottom:10px"></div>
        <el-input
            v-model="formBox.btnInput1"
            readonly
            class="picc-btn-input"
            placeholder="按钮输入框"
            unselectable="on"
            disabled
        />
        <div style="margin-bottom:30px"></div>

        <div class="picc-el-form">
            <el-form
                ref="dialogBox"
                :model="formBox"
                label-width="120px"
                :rules="rules"
            >
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="iconBtnInputBox"
                            label="带图标按钮输入框 - 旧版"
                            class="picc-form-label-linefeed picc-linefeed--medium"
                        >
                            <el-popover
                                v-model="formBox.visible"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                trigger="manual"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button @click="formBox.visible = false">
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="formBox.visible = false"
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.iconBtnInputBox"
                                    class="picc-icon-btn-input casualClass"
                                    suffix-icon="el-icon-search"
                                    placeholder="点击选择关系人"
                                    @focus="inputFocus('iconBtnInputBox')"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="iconBtnInputBox1"
                            label="带图标按钮输入框 - 新版"
                            class="picc-form-label-linefeed picc-linefeed--medium"
                        >
                            <el-popover
                                v-model="formBox.visible1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                trigger="manual"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        @click="formBox.visible1 = false"
                                    >
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="formBox.visible1 = false"
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.iconBtnInputBox1"
                                    placeholder="点击选择关系人"
                                    class="picc-icon-btn-input"
                                    @focus="inputFocus('iconBtnInputBox1')"
                                >
                                    <i
                                        slot="suffix"
                                        class="el-input__icon el-icon-search"
                                        @click="
                                            formBox.visible1 = true;
                                            formBox.iconBtnInputBox1 =
                                                '带图标按钮输入框';
                                        "
                                    >
                                    </i>
                                </el-input>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            label="带图标按钮输入框 - 新版"
                            class="picc-form-label-linefeed picc-linefeed--medium"
                        >
                            <el-popover
                                v-model="formBox.visible2"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                trigger="manual"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        @click="formBox.visible2 = false"
                                    >
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="formBox.visible2 = false"
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.iconBtnInputBox2"
                                    placeholder="点击选择关系人"
                                    class="picc-icon-btn-input"
                                >
                                    <i
                                        slot="suffix"
                                        class="el-input__icon el-icon-search"
                                        @click="
                                            formBox.visible2 = true;
                                            formBox.iconBtnInputBox2 =
                                                '带图标按钮输入框';
                                        "
                                    >
                                    </i>
                                </el-input>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            label="带图标按钮输入框 - 新版"
                            class="picc-form-label-linefeed picc-linefeed--medium"
                        >
                            <el-popover
                                v-model="formBox.visible3"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                trigger="manual"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        @click="formBox.visible3 = false"
                                    >
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="formBox.visible3 = false"
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.iconBtnInputBox3"
                                    disabled
                                    placeholder="点击选择关系人"
                                    class="picc-icon-btn-input"
                                >
                                    <i
                                        slot="suffix"
                                        class="el-input__icon el-icon-search"
                                        @click="test"
                                    >
                                    </i>
                                </el-input>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="btnInputBox"
                            label="按钮输入框"
                        >
                            <el-popover
                                v-model="formBox.visibleBtn"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass', 'btnInputBox')"
                                @hide="isHide('casualClass', 'btnInputBox')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        @click="
                                            formBox.btnInputBox = '';
                                            formBox.visibleBtn = false;
                                        "
                                    >
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="
                                            formBox.btnInputBox = '按钮输入框';
                                            formBox.visibleBtn = false;
                                        "
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox"
                                    readonly
                                    class="picc-btn-input casualClass"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    @focus="inputFocus('btnInputBox')"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visibleBtn1"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                @show="isShow('casualClass1', 'btnInputBox1')"
                                @hide="isHide('casualClass1', 'btnInputBox1')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        @click="
                                            formBox.btnInputBox1 = '';
                                            formBox.visibleBtn1 = false;
                                        "
                                    >
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="
                                            formBox.btnInputBox1 = '按钮输入框';
                                            formBox.visibleBtn1 = false;
                                        "
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox1"
                                    readonly
                                    class="picc-btn-input casualClass1"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="按钮输入框">
                            <el-popover
                                v-model="formBox.visibleBtn2"
                                placement="bottom-start"
                                width="550"
                                disabled
                                :visible-arrow="false"
                                @show="isShow('casualClass2', 'btnInputBox2')"
                                @hide="isHide('casualClass2', 'btnInputBox2')"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        @click="
                                            formBox.visibleBtn2 = false;
                                            formBox.btnInputBox2 = '';
                                        "
                                    >
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="
                                            formBox.visibleBtn2 = false;
                                            formBox.btnInputBox2 = '按钮输入框';
                                        "
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.btnInputBox2"
                                    readonly
                                    class="picc-btn-input casualClass2"
                                    placeholder="按钮输入框"
                                    unselectable="on"
                                    disabled
                                />
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="iconBtnInputBox4"
                            label="带图标按钮输入框 - 新版"
                            class="picc-form-label-linefeed picc-linefeed--medium"
                        >
                            <el-popover
                                v-model="formBox.visible4"
                                placement="bottom-start"
                                width="550"
                                :visible-arrow="false"
                                trigger="manual"
                            >
                                <el-table :data="gridData">
                                    <el-table-column
                                        width="150"
                                        property="date"
                                        label="日期"
                                    />
                                    <el-table-column
                                        width="100"
                                        property="name"
                                        label="姓名"
                                    />
                                    <el-table-column
                                        width="300"
                                        property="address"
                                        label="地址"
                                    />
                                </el-table>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        @click="
                                            formBox.iconBtnInputBox4 = '';
                                            $refs.dialogBox.validateField(
                                                'iconBtnInputBox4'
                                            );
                                            formBox.visible4 = false;
                                        "
                                    >
                                        取 消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        @click="
                                            formBox.iconBtnInputBox4 =
                                                '带图标按钮输入框';
                                            $refs.dialogBox.validateField(
                                                'iconBtnInputBox4'
                                            );
                                            formBox.visible4 = false;
                                        "
                                    >
                                        确 定
                                    </el-button>
                                </div>
                                <el-input
                                    slot="reference"
                                    v-model="formBox.iconBtnInputBox4"
                                    placeholder="点击选择关系人"
                                    class="picc-icon-btn-input"
                                    @focus="inputFocus('iconBtnInputBox4')"
                                >
                                    <i
                                        slot="suffix"
                                        class="el-input__icon el-icon-search"
                                        @click="formBox.visible4 = true"
                                    >
                                    </i>
                                </el-input>
                            </el-popover>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="24" style="text-align:center;">
                        <el-button @click="subForm">
                            点击校验表单
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div style="margin-bottom:30px"></div>
    </div>
</template>

<script>
export default {
    name: "Index927",
    data() {
        return {
            formBox: {
                visible: false,
                iconBtnInputBox: "",
                visible1: false,
                iconBtnInputBox1: "",
                visible2: false,
                iconBtnInputBox2: "",
                visible3: false,
                iconBtnInputBox3: "",
                visible4: false,
                iconBtnInputBox4: "",

                visibleBtn: false,
                btnInputBox: "",
                visibleBtn1: false,
                btnInputBox1: "",
                visibleBtn2: false,
                btnInputBox2: "",

                iconBtnInput: "",
                iconBtnInput1: "",
                iconBtnInputnew: "",
                iconBtnInputnew1: "",
                btnInput: "",
                btnInput1: ""
            },
            rules: {
                iconBtnInputBox: [
                    {
                        required: true,
                        message: "点击选择产品方案",
                        trigger: "blur"
                    }
                ],
                iconBtnInputBox1: [
                    {
                        required: true,
                        message: "点击选择产品方案",
                        trigger: "blur"
                    }
                ],
                btnInputBox: [
                    {
                        required: true,
                        message: `气泡 - 错误提示的文案错误提示的文案错误提示的文案错误提示的文案误提示的文案错误提示的文案错误提示的文案错误提示的文案错误提示的文案...`,
                        trigger: "blur"
                    },
                    {
                        min: 5,
                        message: `气泡 - 错误提示的文案需要大于5个字`,
                        trigger: "blur"
                    },
                    {
                        max: 8,
                        message: `气泡 - 错误提示的文案需要小于8个字`,
                        trigger: "blur"
                    }
                ],
                iconBtnInputBox4: [
                    {
                        required: true,
                        message: "点击选择产品方案",
                        trigger: "blur"
                    }
                ],
                btnInputBox3: [
                    {
                        required: true,
                        message: `气泡 - 错误提示的文案错误提示的文案错误提示的文案错误提示的文案误提示的文案错误提示的文案错误提示的文案错误提示的文案错误提示的文案...`,
                        trigger: "blur"
                    },
                    {
                        min: 5,
                        message: `气泡 - 错误提示的文案需要大于5个字`,
                        trigger: "blur"
                    },
                    {
                        max: 8,
                        message: `气泡 - 错误提示的文案需要小于8个字`,
                        trigger: "blur"
                    }
                ]
            },
            gridData: [
                {
                    date: "2016-05-02",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-04",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-01",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                }
            ],

            Content: [
                {
                    MROCardCode: "1",
                    MROCardName: "1",
                    DZCardCode: "1",
                    DZCardName: "1",
                    MROAmount: "1",
                    DZAmount: "1",
                    popover: false
                }
            ]
        };
    },
    mounted() {
        // this.handleIconBtn() /* 带图标按钮输入框 - 打开弹窗 */
    },
    methods: {
        // 添加行的索引
        AddListRow() {
            this.Content.push({
                MROCardCode: "",
                MROCardName: "",
                DZCardCode: "",
                DZCardName: "",
                MROAmount: "",
                DZAmount: "",
                popover: false
            });
        },
        handleDelete(index) {
            this.Content.splice(index, 1);
        },
        openCardCode(name, code, index) {
            this.$message({
                message:
                    "大宗客户抬头:" +
                    name +
                    "  " +
                    "CODE:" +
                    code +
                    "  " +
                    "index:" +
                    index,
                type: "warning"
            });
        },

        subForm() {
            this.$refs.dialogBox.validate((valid) => {
                if (valid) {
                    alert("submit!");
                } else {
                    // console.log("error submit!!");
                    return false;
                }
            });
        },

        inputFocus: function(target) {
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        },

        /* 带图标按钮输入框 */
        handleIconBtn() {
            const inputBox = document.querySelector(".casualClass");
            const _this = this;
            inputBox.onclick = function(ev) {
                // eslint-disable-next-line no-redeclare
                var ev = ev || window.event;
                // console.log(ev);
                const oLi = ev.srcElement || ev.target;

                if (oLi.nodeName.toLowerCase() === "i") {
                    _this.formBox.visible = true;
                    _this.formBox.iconBtnInputBox = "带图标按钮输入框";
                }
            };
        },

        /* 按钮输入框 */
        isShow(target) {
            const $target = document.querySelector("." + target + " input");
            $target.style.borderColor = "#234dcc";
            $target.style.color = "#1C61FC";
        },
        isHide(target, data) {
            const $target = document.querySelector("." + target + " input");
            $target.style.borderColor = "#cdcdcd";

            if (this.formBox[data] === "") {
                $target.style.color = "#909199";
            } else {
                $target.style.color = "#292B34";
            }

            // 部分表单校验,否则校验结果有问题
            this.$refs.dialogBox.validateField(data);
        },
        test() {
            // console.log("样例演示");
        }
    }
};
</script>

<style scoped></style>
