<template>
    <div style="padding:30px;">
        <h1 style="color:red;font-size:16px;">
            下拉选择器：1）不可用状态 2）仅操作型有两种类型 3）模糊搜索兼下拉选择器
        </h1>
        <el-select v-model="selectOper.selectOper" placeholder="下拉选择器-仅操作型--组合">
            <el-option-group v-for="group in selectOper.options" :key="group.label" :label="group.label">
                <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
            </el-option-group>
        </el-select>
        <div style="margin-bottom:10px"></div>
        <el-select v-model="selectOper.selectOper1" placeholder="下拉选择器-仅操作型--组合" disabled>
            <el-option-group v-for="group in selectOper.options" :key="group.label" :label="group.label">
                <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
            </el-option-group>
        </el-select>
        <div style="margin-bottom:10px"></div>
        <el-select v-model="selectOper.selectOper2" placeholder="下拉选择器-仅操作型">
            <el-option v-for="(item, index) in selectOper.insurance" :key="index" :label="`${item.num}-${item.value}`" :value="item.value" />
        </el-select>
        <div style="margin-bottom:10px"></div>
        <el-select v-model="selectOper.selectOper3" placeholder="下拉选择器-仅操作型" disabled>
            <el-option v-for="(item, index) in selectOper.insurance" :key="index" :label="`${item.num}-${item.value}`" :value="item.value" />
        </el-select>
        <div style="margin-bottom:10px"></div>
        <el-select v-model="selectOper.selectOper4" filterable placeholder="模糊搜索兼下拉选择">
            <el-option v-for="item in selectOper.fuzzyOption" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div style="margin-bottom:10px"></div>
        <el-select v-model="selectOper.selectOper5" filterable placeholder="模糊搜索兼下拉选择" disabled>
            <el-option v-for="item in selectOper.fuzzyOption" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div style="margin-bottom:30px"></div>

        <div class="picc-el-form">
            <el-form ref="dialogBox" :model="selectOper" label-width="120px" :rules="rules">
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="下拉选择器-仅操作型" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-select v-model="selectOper.optionsValue" placeholder="请选择">
                                <el-option-group v-for="group in selectOper.options" :key="group.label" :label="group.label">
                                    <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
                                </el-option-group>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="optionsValue1"
                            label="下拉选择器-仅操作型"
                            class="picc-form-label-linefeed picc-linefeed--medium"
                        >
                            <el-select
                                v-model="selectOper.optionsValue1"
                                placeholder="请选择"
                                @focus="inputFocus('optionsValue1')"
                                @blur="inputBlur('optionsValue1')"
                            >
                                <el-option-group v-for="group in selectOper.options" :key="group.label" :label="group.label">
                                    <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
                                </el-option-group>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="下拉选择器-仅操作型" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-select v-model="selectOper.optionsValue2" placeholder="请选择" disabled>
                                <el-option-group v-for="group in selectOper.options" :key="group.label" :label="group.label">
                                    <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value" />
                                </el-option-group>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="下拉选择器-仅操作型" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                                <el-option
                                    v-for="(item, index) in selectOper.insurance"
                                    :key="index"
                                    :label="`${item.num}-${item.value}`"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item
                            v-picc-input-error
                            prop="insuranceShow1"
                            label="下拉选择器-仅操作型"
                            class="picc-form-label-linefeed picc-linefeed--medium"
                        >
                            <el-select
                                v-model="selectOper.insuranceShow1"
                                placeholder="请选择"
                                @focus="inputFocus('insuranceShow1')"
                                @blur="inputBlur('insuranceShow1')"
                            >
                                <el-option
                                    v-for="(item, index) in selectOper.insurance"
                                    :key="index"
                                    :label="`${item.num}-${item.value}`"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="下拉选择器-仅操作型" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-select v-model="selectOper.insuranceShow2" placeholder="请选择" disabled>
                                <el-option
                                    v-for="(item, index) in selectOper.insurance"
                                    :key="index"
                                    :label="`${item.num}-${item.value}`"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="8">
                        <el-form-item label="模糊搜索兼下拉选择" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-select v-model="selectOper.fuzzy" filterable placeholder="可直接输入名称">
                                <el-option v-for="item in selectOper.fuzzyOption" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item v-picc-input-error prop="fuzzy1" label="模糊搜索兼下拉选择" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-select
                                v-model="selectOper.fuzzy1"
                                filterable
                                placeholder="可直接输入名称"
                                @focus="inputFocus('fuzzy1')"
                                @blur="inputBlur('fuzzy1')"
                            >
                                <el-option v-for="item in selectOper.fuzzyOption" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="模糊搜索兼下拉选择" class="picc-form-label-linefeed picc-linefeed--medium">
                            <el-select v-model="selectOper.fuzzy2" filterable placeholder="可直接输入名称" disabled>
                                <el-option v-for="item in selectOper.fuzzyOption" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row type="flex">
                    <el-col :span="24" style="text-align:center;">
                        <el-button @click="ddd">
                            点击校验表单
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div style="margin-bottom:30px"></div>

        <!-- <cascade-selector class="cascade" v-model="cascadeValue" @cascadeShow="cascadeShow" :data="options"></cascade-selector> -->
        <!-- <el-popover
            ref="popover"
            placement="top"
            width="200"
            trigger="click"
            popper-class="picc-popover-custom"
            >
            <span><i class="el-icon-info" />禁用提示信息popover</span>
        </el-popover> -->
    </div>
</template>

<script>
export default {
    name: "Index927",
    data() {
        return {
            rules: {
                optionsValue1: [{ required: true, message: "请选择活动区域", trigger: "change" }],
                insuranceShow1: [
                    { required: true, message: "请选择活动区域", trigger: "change" },
                    { min: 8, message: `气泡9 - 错误提示的文案需要大于8个字`, trigger: "change" },
                    { max: 13, message: `气泡9 - 错误提示的文案需要小于13个字`, trigger: "change" }
                ],
                fuzzy1: [{ required: true, message: "请选择", trigger: "change" }]
            },
            /* 下拉选择器 - 仅操作型 */
            selectOper: {
                insuranceShow: "",
                insuranceShow1: "",
                insuranceShow2: "",
                insurance: [
                    {
                        num: "01",
                        value: "普通家庭财产保险1"
                    },
                    {
                        num: "02",
                        value: "普通家庭财产保险2"
                    },
                    {
                        num: "03",
                        value: "普通家庭财产保险3"
                    },
                    {
                        num: "04",
                        value: "普通家庭财产保险财产保险4"
                    },
                    {
                        num: "05",
                        value: "普通家庭财产保险财产保险5"
                    },
                    {
                        num: "06",
                        value: "普通家庭财产保险财产保险6"
                    },
                    {
                        num: "07",
                        value: "普通家庭财产保险财产保险普通家庭财产保险财产保险7"
                    },
                    {
                        num: "08",
                        value: "普通家庭财产保险财产保险普通家庭财产保险财产保险8"
                    },
                    {
                        num: "09",
                        value: "普通家庭财产保险财产保险普通家庭财产保险财产保险9"
                    }
                ],
                options: [
                    {
                        label: "热门城市",
                        options: [
                            {
                                value: "Shanghai",
                                label: "上海"
                            },
                            {
                                value: "Beijing",
                                label: "北京"
                            }
                        ]
                    },
                    {
                        label: "城市名",
                        options: [
                            {
                                value: "Chengdu",
                                label: "成都"
                            },
                            {
                                value: "Shenzhen",
                                label: "深圳"
                            },
                            {
                                value: "Guangzhou",
                                label: "广州"
                            },
                            {
                                value: "Dalian",
                                label: "大连"
                            }
                        ]
                    }
                ],
                optionsValue: "",
                optionsValue1: "",
                optionsValue2: "",

                selectOper: "",
                selectOper1: "",
                selectOper2: "",
                selectOper3: "",
                selectOper4: "",
                selectOper5: "",

                /* 下拉选择器 - 模糊搜索兼下拉选择 */
                fuzzy: "",
                fuzzy1: "",
                fuzzy2: "",
                fuzzyOption: [
                    {
                        value: "选项1",
                        label: "黄金糕"
                    },
                    {
                        value: "选项2",
                        label: "双皮奶"
                    },
                    {
                        value: "选项3",
                        label: "蚵仔煎"
                    },
                    {
                        value: "选项4",
                        label: "龙须面"
                    },
                    {
                        value: "选项5",
                        label: "北京烤鸭"
                    }
                ],

                demo: "",
                popover: false,
                demo1: "",
                popover1: false,
                demo2: "",
                popover2: false,
                demo3: "",
                popover3: false
            },
            /* 下拉选择器 - 不可用状态 */
            disabledOper: {
                disabledOper: "",
                validStatus: "",
                options: [
                    {
                        value: "选项1",
                        label: "黄金糕"
                    },
                    {
                        value: "选项2",
                        label: "双皮奶"
                    },
                    {
                        value: "选项3",
                        label: "蚵仔煎"
                    },
                    {
                        value: "选项4",
                        label: "龙须面"
                    },
                    {
                        value: "选项5",
                        label: "北京烤鸭"
                    }
                ]
            }
        };
    },
    methods: {
        ddd() {
            // console.log(this.selectOper.optionsValue1);
            this.$refs.dialogBox.validate((valid) => {
                if (valid) {
                    alert("submit!");
                } else {
                    // console.log("error submit!!");
                    return false;
                }
            });
        },

        inputFocus: function(target) {
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        },
        inputBlur: function(target) {
            // 部分表单校验
            this.$refs.dialogBox.validateField(target);
        }
    }
};
</script>

<style scoped></style>
