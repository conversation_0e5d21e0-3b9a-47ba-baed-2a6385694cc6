<template>
    <div>
        <button>X</button>
        <button class="c-Button c-Button--close">
            X
        </button>
        <button :class="[$style.button, $style.buttonClose]">
            X
        </button>

        <div class="picc-container-box">
            <div class="picc-container">
                <!-- 内页卡片内标题栏 -->
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        客户信息01
                    </div>
                    <div class="picc-operation-area">
                        <span class="picc-operation-superior"><i class="el-icon-delete-solid"></i></span>
                    </div>
                </div>
                <div class="picc-el-form">
                    <el-upload
                        class="upload-demo"
                        action="https://jsonplaceholder.typicode.com/posts/"
                        :on-preview="handlePreview"
                        :on-remove="handleRemove"
                        :before-remove="beforeRemove"
                        multiple
                        :limit="3"
                        :on-exceed="handleExceed"
                        :file-list="fileList"
                    >
                        <el-button size="small" type="primary">
                            点击上传
                        </el-button>
                        <div slot="tip" class="el-upload__tip">
                            只能上传jpg/png文件，且不超过500kb
                        </div>
                    </el-upload>
                    <div style="margin: 30px;"></div>

                    <!-- ****************************************************************************************************************************************** -->
                    <!-- <el-upload
            class="upload-demo"
            ref="uploadDemo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="false">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
          <div style="margin: 30px;" /> -->

                    <!-- 自定义上传 -->
                    <!-- <el-upload
              class="upload-demo"
              ref="upload"
              action=''
              :http-request="uploadSectionFile"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :file-list="fileList"
              :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>
              <div slot="tip" class="el-upload__tip">只能上传json文件，且不超过500kb</div>
          </el-upload>
          <div style="margin: 30px;" /> -->

                    <!-- 3 -->
                    <el-upload
                        ref="upload"
                        class="upload-demo"
                        action=""
                        accept="image/jpeg,image/png,image/jpg"
                        list-type="picture-card"
                        :before-upload="onBeforeUploadImage"
                        :http-request="UploadImage"
                        :on-change="fileChange"
                        :file-list="fileList"
                    >
                        <el-button size="small" type="primary">
                            点击上传
                        </el-button>
                        <div slot="tip" class="el-upload__tip">
                            只能上传jpg/jpeg/png文件，且不超过500kb
                        </div>
                    </el-upload>
                </div>
            </div>
        </div>
        <div style="margin: 30px;"></div>
    </div>
</template>

<script>
export default {
    name: "Demo",
    data() {
        return {
            fileList: [
                {
                    name: "food.jpeg",
                    url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100"
                },
                {
                    name: "food2.jpeg",
                    url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100"
                }
            ],
            logo: ""
        };
    },
    methods: {
        handleRemove(file, fileList) {
            // console.log(file, fileList);
            alert("删除");
        },
        handlePreview(file) {
            // console.log(file);
            alert("点击文件列表中已上传的文件时的钩子");
        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
        beforeRemove(file, fileList) {
            alert("是否要删除");
            return this.$confirm(`确定移除 ${file.name}？`);
        },
        submitUpload() {
            // this.$refs.uploadDemo.submit();
            this.$refs.upload.submit();
        },

        uploadSectionFile: function(param) {
            // file就是当前添加的文件
            const fileObj = param.file;
            // console.log(fileObj);
            // FormData 对象
            const form = new FormData();
            // 文件对象,key是后台接受的参数名称
            form.append("uploadFile", fileObj);
        },

        /*
      在上传方法调用后判断结果成功或者失败的时候，需要回调el-upload控件的onSuccess和onError方法，为的是能够复用el-upload原生的一些动作，
      比如如果成功了，页面上的文件列表会有一个绿勾标记上传成功的文件，如果失败则会把失败的文件从文件列表中删除，如果不回调是没有这些功能的。
        */
        // handleSuccess() {},
        // handleError() {},
        myUpload(content) {
            // console.log("myUpload...");
            this.$axios({
                method: "post",
                url: content.action,
                timeout: 20000,
                data: content.file
            })
                .then((res) => {
                    content.onSuccess("配时文件上传成功");
                })
                .catch((error) => {
                    if (error.response) {
                        // The request was made and the server responded with a status code
                        // that falls out of the range of 2xx
                        content.onError("配时文件上传失败(" + error.response.status + ")，" + error.response.data);
                    } else if (error.request) {
                        // The request was made but no response was received
                        // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
                        // http.ClientRequest in node.js
                        content.onError("配时文件上传失败，服务器端无响应");
                    } else {
                        // Something happened in setting up the request that triggered an Error
                        content.onError("配时文件上传失败，请求封装失败");
                    }
                });
        },

        // 3
        onBeforeUploadImage(file) {
            const isIMAGE = file.type === "image/jpeg" || "image/jpg" || "image/png";
            const isLt1M = file.size / 1024 / 1024 < 1;
            if (!isIMAGE) {
                this.$message.error("上传文件只能是图片格式!");
            }
            if (!isLt1M) {
                this.$message.error("上传文件大小不能超过 1MB!");
            }
            return isIMAGE && isLt1M;
        },
        UploadImage(param) {
            alert("自定义上传");
            const formData = new FormData();
            formData.append("file", param.file);
            // console.log(formData);
            this.UploadImageApi(formData)
                .then((response) => {
                    // console.log("上传图片成功");
                    param.onSuccess(); // 上传成功的图片会显示绿色的对勾
                    // 但是我们上传成功了图片， fileList 里面的值却没有改变，还好有on-change指令可以使用
                })
                .catch((response) => {
                    // throw new Error("图片上传失败");
                    param.onError();
                });
        },
        fileChange(file) {
            this.$refs.upload.clearFiles(); // 清除文件对象
            this.logo = file.raw; // 取出上传文件的对象，在其它地方也可以使用
            this.fileList = [{ name: file.name, url: file.url }]; // 重新手动赋值filstList， 免得自定义上传成功了, 而fileList并没有动态改变， 这样每次都是上传一个对象
        }
    }
};
</script>

<!-- 使用 BEM 约定 -->
<style>
.c-Button {
    border: none;
    border-radius: 2px;
}
.c-Button--close {
    background-color: red;
}
</style>

<!-- 使用 CSS Modules -->
<style module>
.button {
    border: none;
    border-radius: 2px;
}
.buttonClose {
    background-color: red;
}
</style>
