<template>
    <div style="padding:30px;">
        <!-- form 表单 -->
        <div class="picc-container-box">
            <div class="picc-container">
                <!-- div标签的存在只为padding值的添加 若form表单都存在padding的话，就去掉div，直接加到form表单就好 -->
                <div class="picc-el-form">
                    <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="时间选择器">
                                    <!--format="yyyy/MM/dd HH时mm分"-->
                                    <el-date-picker v-model="formBox.timePicker1" type="datetime" :clearable="false" placeholder="选择日期时间" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="时间选择器">
                                    <!--format="yyyy/MM/dd HH时mm分"-->
                                    <el-date-picker
                                        v-model="formBox.timePicker1_1"
                                        disabled="disabled"
                                        :clearable="false"
                                        type="datetime"
                                        placeholder="选择日期时间"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="时间选择器">
                                    <!--format="yyyy/MM/dd HH时mm分"-->
                                    <el-date-picker
                                        v-model="formBox.timePicker1_2"
                                        class="picc-isclearable"
                                        type="datetime"
                                        :clearable="true"
                                        placeholder="选择日期时间"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="时间选择器">
                                    <!--format="yyyy/MM/dd HH时mm分"-->
                                    <el-date-picker
                                        v-model="formBox.timePicker1_3"
                                        disabled="disabled"
                                        class="picc-isclearable"
                                        type="datetime"
                                        :clearable="true"
                                        placeholder="选择日期时间"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item v-picc-input-error label="时间选择器" prop="timeRules">
                                    <!--format="yyyy/MM/dd HH时mm分"-->
                                    <el-date-picker
                                        v-model="formBox.timeRules"
                                        class="picc-isclearable"
                                        type="datetime"
                                        :clearable="true"
                                        placeholder="选择日期时间"
                                        @focus="inputFocus('timeRules')"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="日期选择器">
                                    <el-date-picker v-model="formBox.datePicker1" type="date" :clearable="false" format="yyyy/MM/dd" placeholder="选择日期" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="日期选择器">
                                    <el-date-picker
                                        v-model="formBox.datePicker1_1"
                                        type="date"
                                        :clearable="false"
                                        disabled="disabled"
                                        format="yyyy/MM/dd"
                                        placeholder="选择日期"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="日期选择器">
                                    <el-date-picker
                                        v-model="formBox.datePicker1_2"
                                        type="date"
                                        class="picc-isclearable"
                                        :clearable="true"
                                        format="yyyy/MM/dd"
                                        placeholder="选择日期"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="日期选择器">
                                    <el-date-picker
                                        v-model="formBox.datePicker1_3"
                                        type="date"
                                        disabled="disabled"
                                        class="picc-isclearable"
                                        :clearable="true"
                                        format="yyyy/MM/dd"
                                        placeholder="选择日期"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item v-picc-input-error label="日期选择器" prop="timeRules1">
                                    <el-date-picker
                                        v-model="formBox.timeRules1"
                                        type="date"
                                        class="picc-isclearable"
                                        :clearable="true"
                                        format="yyyy/MM/dd"
                                        placeholder="选择日期"
                                        @focus="inputFocus('timeRules1')"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="日期范围">
                                    <el-date-picker
                                        v-model="formBox.timePicker2"
                                        type="daterange"
                                        :clearable="false"
                                        format="yyyy/MM/dd"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        @blur="datePickerArea($event, formBox.timePicker2)"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="日期范围">
                                    <el-date-picker
                                        v-model="formBox.timePicker2_2"
                                        disabled="disabled"
                                        :clearable="false"
                                        format="yyyy/MM/dd"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        @blur="datePickerArea($event, formBox.timePicker2_2)"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="日期范围">
                                    <el-date-picker
                                        v-model="formBox.timePicker2_3"
                                        type="daterange"
                                        class="picc-isclearable"
                                        format="yyyy/MM/dd"
                                        range-separator="至"
                                        :clearable="true"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        @blur="datePickerArea($event, formBox.timePicker2_3)"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="日期范围">
                                    <el-date-picker
                                        v-model="formBox.timePicker2_4"
                                        type="daterange"
                                        disabled="disabled"
                                        class="picc-isclearable"
                                        format="yyyy/MM/dd"
                                        range-separator="至"
                                        :clearable="true"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        @blur="datePickerArea($event, formBox.timePicker2_4)"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item v-picc-input-error label="日期范围" prop="timeRules2">
                                    <el-date-picker
                                        v-model="formBox.timeRules2"
                                        type="daterange"
                                        class="picc-isclearable"
                                        format="yyyy/MM/dd"
                                        range-separator="至"
                                        :clearable="true"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        @blur="datePickerArea($event, formBox.timeRules2)"
                                        @focus="inputFocus('timeRules2')"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "Picker",
    data() {
        return {
            formBox: {
                timePicker1: "",
                timePicker1_1: "",
                timePicker1_2: "",
                timePicker1_3: "",
                timePicker2: "",
                timePicker2_2: "",
                timePicker2_3: "",
                timePicker2_4: "",
                timePicker3: "",
                datePicker1: "",
                datePicker1_1: "",
                datePicker1_2: "",
                datePicker1_3: "",

                timeRules: "",
                timeRules1: "",
                timeRules2: ""
            },
            rules: {
                typeCode: [{ required: true, message: "请输入类型代码", trigger: "blur" }],
                timeRules: [{ type: "date", required: true, message: "请选择日期", trigger: "change" }],
                timeRules1: [{ type: "date", required: true, message: "请选择日期", trigger: "change" }],
                timeRules2: [{ required: true, message: "请选择日期", trigger: "change" }]
            }
        };
    },
    methods: {
        /* 日期范围 */
        datePickerArea(event, target) {
            const inputBox = event.$el.querySelectorAll("input");
            if (target !== "") {
                for (let i = 0; i < inputBox.length; i++) {
                    inputBox[i].style.color = "#292B34";
                    inputBox[i].style.fontSize = "12px";
                }
            } else {
                for (let j = 0; j < inputBox.length; j++) {
                    inputBox[j].style.color = "#909199";
                    inputBox[j].style.fontSize = "14px";
                }
            }
        },

        inputFocus: function(target) {
            // this.rules[target][0].required = false
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        }
    }
};
</script>

<style scoped></style>
