<template>
    <div>
        <!-- 提示信息 -->
        <div class="top-tip-div">
            <div class="top-tip">
                <p>表单盒子高度经过【视觉规范v1.3】版本的更新，现存在两种版本：一是【上下间距舒适版】；二是【上下间距紧凑版】；各项目组可根据实际业务需求，灵活使用；</p>
                <p style="margin-top: 6px;">
                    另外，必录标识本次也进行了更新，现视觉规范存在两种版本：一是【6px-常规状态 <span>*</span>】；二是【8px-视觉聚焦增强版
                    <span style="display:inline-block;width:8px;font-weight:bold;">*</span>】；各项目组可根据实际业务需求，灵活使用，在使用过程中，需注意，同一系统必须使用同一版本！
                </p>
            </div>
        </div>
        <!-- form 表单 -->
        <div style="margin-top: 66px; padding-bottom:100px">
            <BoxHead title="上下间距舒适版(盒子高度52px)">
                <div slot="outsideOperation">
                    <span class="span-red">在使用过程中，需注意，同一系统必须使用同一版本！</span>
                </div>
            </BoxHead>
            <div class="picc-container" style="margin:0 24px">
                <!-- div标签的存在只为padding值的添加 若form表单都存在padding的话，就去掉div，直接加到form表单就好 -->
                <div class="picc-el-form">
                    <el-form ref="dialogBox" :model="formBox" label-width="120px" :rules="rules">
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="typeCodea" label="文本输入框"
                                    class="picc-linefeed--medium"
                                >
                                    <!-- <el-tooltip :disabled="!tooltip" effect="dark" placement="top-start">
                                        <div slot="content" class="picc-tooltip picc-tooltip-content">
                                            <p>{{ formBox.typeCodea }}</p>
                                        </div> -->
                                    <el-input
                                        ref="input" v-model="formBox.typeCodea"
                                        v-input-overflow-popup="formBox.typeCodea" class="el-input-style"
                                        placeholder="输入文本信息" clearable @focus="inputFocus('typeCodea')"
                                        @blur="inputBlur('typeCodea')"
                                    />
                                    <!-- </el-tooltip> -->
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="文本输入框不可用状态" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-input
                                        v-model="formBox.typeCode" v-popover:popover2 class="el-input-style"
                                        placeholder="输入文本信息" disabled
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error v-picc-input-longtext:value="formBox.textLong"
                                    prop="textLong" label="文本输入框文本过长"
                                    class="picc-form-label-linefeed picc-linefeed--medium picc-inline-textarea"
                                >
                                    <el-input v-model="formBox.textLong" clearable type="text" class="el-input-style" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="foldLinea" label="标题过长可以折行处理处理处理处理"
                                    class="picc-form-label-linefeed picc-linefeed--long"
                                >
                                    <el-input
                                        id="foldLine" v-model="formBox.foldLinea"
                                        v-input-overflow-popup="formBox.foldLinea" class="el-input-style"
                                        placeholder="输入文本信息" clearable @focus="inputFocus('foldLinea')"
                                        @blur="inputBlur('foldLinea')"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="标题过长可以折行" class="picc-form-label-linefeed picc-linefeed--short">
                                    <el-input
                                        v-model="formBox.foldLine" v-input-overflow-popup="formBox.foldLine"
                                        class="el-input-style" placeholder="输入文本信息" clearable
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="tipInputa" label="带提示说明"
                                    class="picc-form-label-tipInfo-icon"
                                >
                                    <el-input
                                        id="tipInput" v-model="formBox.tipInputa"
                                        v-input-overflow-popup="formBox.tipInputa" class="el-input-style"
                                        placeholder="输入文本信息" clearable @focus="inputFocus('tipInputa')"
                                        @blur="inputBlur('tipInputa')"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="带提示说明输入框" class="picc-form-label-linefeed picc-linefeed--short">
                                    <el-input
                                        v-model="formBox.tipInput" v-input-overflow-popup="formBox.tipInput" clearable
                                        class="el-input-style" placeholder="输入文本信息"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item label="按钮输入框">
                                    <el-popover
                                        v-model="formBox.visible1" placement="start"
                                        popper-class="demo_search_result" width="550" title="选择产品方案" :visible-arrow="false"
                                        @show="isShow('casualClass1')" @hide="isHide('casualClass1')"
                                    >
                                        <el-input
                                            v-model="formBox.btnInputBox2" placeholder="请输入内容"
                                            prefix-icon="el-icon-search"
                                        />
                                        <el-table :data="gridData" style="margin-top: 16px;">
                                            <el-table-column width="150" property="date" label="日期" />
                                            <el-table-column width="100" property="name" label="姓名" />
                                            <el-table-column property="address" label="地址" />
                                        </el-table>
                                        <div style="text-align: right; margin: 10px auto;text-align:center">
                                            <el-button type="primary" round @click="formBox.visible1 = false">
                                                确 定
                                            </el-button>
                                        </div>
                                        <el-input
                                            slot="reference" v-model="formBox.btnInputBox" readonly
                                            class="picc-btn-input casualClass1" placeholder="按钮输入框" unselectable="on"
                                        />
                                    </el-popover>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="按钮输入框">
                                    <el-popover
                                        v-model="formBox.visible2" placement="bottom-start" width="550" disabled
                                        :visible-arrow="false" @show="isShow('casualClass2')"
                                        @hide="isHide('casualClass2')"
                                    >
                                        <el-table :data="gridData">
                                            <el-table-column width="150" property="date" label="日期" />
                                            <el-table-column width="100" property="name" label="姓名" />
                                            <el-table-column width="300" property="address" label="地址" />
                                        </el-table>
                                        <div style="text-align: right; margin: 0">
                                            <el-button @click="formBox.visible2 = false">
                                                取 消
                                            </el-button>
                                            <el-button type="primary" @click="formBox.visible2 = false">
                                                确 定
                                            </el-button>
                                        </div>
                                        <el-input
                                            slot="reference" v-model="formBox.btnInputBox" readonly
                                            class="picc-btn-input casualClass2" placeholder="按钮输入框" unselectable="on"
                                            disabled
                                        />
                                    </el-popover>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item label="下拉选择器-仅操作型" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-select v-model="selectOper.optionsValue" placeholder="请选择">
                                        <el-option-group
                                            v-for="group in selectOper.options" :key="group.label"
                                            :label="group.label"
                                        >
                                            <el-option
                                                v-for="item in group.options" :key="item.value" :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-option-group>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="insuranceShow" label="下拉选择器-仅操作型2"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                                        <el-option
                                            v-for="(item, index) in selectOper.insurance" :key="index"
                                            :label="`${item.num}-${item.value}`" :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="效力状态">
                                    <el-select v-model="selectOper.insuranceShow" placeholder="请选择效力状态">
                                        <el-option
                                            v-for="(item, index) in selectOper.insurance" :key="index"
                                            :label="`${item.num}-${item.value}`" :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="fuzzynn" label="模糊搜索兼下拉选择"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-select v-model="fuzzySearch.fuzzynn" filterable placeholder="可直接输入名称">
                                        <el-option
                                            v-for="item in fuzzySearch.options" :key="item.value" :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="disabledOper" label="下拉选择器不可用状态"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-select
                                        v-model="disabledOper.disabledOper" v-popover:popover placeholder="请选择"
                                        disabled
                                    >
                                        <el-option
                                            v-for="item in disabledOper.options" :key="item.value"
                                            :label="item.label" :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="selectedOptiona" label="下拉选择器联级选择"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-cascader
                                        v-model="cascadeSelection.selectedOptions"
                                        :options="cascadeSelection.options"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="下拉选择器联级选择" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-cascader
                                        v-model="cascadeSelection.selectedOptions"
                                        :options="cascadeSelection.options"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    label="下拉选择器联级disabled"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-cascader
                                        v-model="cascadeSelection.selectedOptions1" disabled
                                        :options="cascadeSelection.options"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item v-picc-input-error prop="resource" label="关系人类型">
                                    <el-radio-group v-model="formBox.resource">
                                        <el-radio label="是" />
                                        <el-radio label="否" />
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="选中且不可用">
                                    <el-radio-group v-model="formBox.resource1" disabled>
                                        <el-radio label="是" />
                                        <el-radio label="否" />
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="未选中且不可用" class="picc-form-label-linefeed picc-linefeed--short">
                                    <el-radio-group v-model="formBox.resource2" disabled>
                                        <el-radio label="是" />
                                        <el-radio label="否" />
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item v-picc-input-error prop="types" label="复选框">
                                    <el-checkbox-group v-model="formBox.types">
                                        <el-checkbox label="投保人" name="types" />
                                        <el-checkbox label="被保险人" name="types" />
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="选中且不可用">
                                    <el-checkbox-group v-model="formBox.types1" disabled>
                                        <el-checkbox label="投保人" name="types" />
                                        <el-checkbox label="被保险人" name="types" />
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="未选中且不可用" class="picc-form-label-linefeed picc-linefeed--short">
                                    <el-checkbox-group v-model="formBox.types2" disabled>
                                        <el-checkbox label="投保人" name="types" />
                                        <el-checkbox label="被保险人" name="types" />
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item prop="num" label="数字输入框">
                                    <el-input-number v-model="formBox.num" :min="1" :max="10" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="数字输入框">
                                    <el-input-number v-model="formBox.num1" :min="1" :max="10" disabled />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item label="时间选择器">
                                    <!--format="yyyy/MM/dd HH时mm分"-->
                                    <el-date-picker
                                        v-model="timePicker.timePicker2" type="datetime" placeholder="选择日期时间"
                                        :format="dateFormat"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item label="日期选择器1">
                                    <el-date-picker
                                        v-model="datePicker.datePicker1" :format="dateFormat" type="date"
                                        data-mode="dateFormat" placeholder="选择日期"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                            <el-col :span="8" />
                        </el-row>

                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item label="日期范围1">
                                    <el-date-picker
                                        v-model="timePicker.timePicker3" type="daterange" :format="dateFormat"
                                        data-mode="dateFormat" range-separator="至" start-placeholder="开始日期"
                                        end-placeholder="结束日期" @change="test"
                                        @blur="datePickerArea($event, timePicker.timePicker3)"
                                    />
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="8">
                                <el-form-item label="日期范围">
                                    <el-date-picker
                                        v-model="timePicker.timePicker3"
                                        disabled="disabled"
                                        format="yyyy/MM/dd"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        @blur="datePickerArea($event,timePicker.timePicker3)"
                                    />
                                </el-form-item>
                            </el-col> -->
                            <el-col :span="8">
                                <el-form-item label="日期范围2">
                                    <DatepickerRange
                                        format="YYYY/MM/DD" value-format="YYYY/MM/DD" placeholder="选择日期范围..."
                                        message="请选择结束日期"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <div class="picc-form-inputgroup">
                                    <el-form-item
                                        v-picc-input-error prop="inputReq" label="需要自己组合"
                                        class="picc-form-inputgroup-select"
                                    >
                                        <el-select v-model="selectOper.insuranceShow1" placeholder="请选择">
                                            <el-option
                                                v-for="(item, index) in inputGroup" :key="index"
                                                :label="`${item.num}-${item.value}`" :value="item.value"
                                            />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item v-picc-input-error prop="inputGroupzz" class="picc-form-inputgroup-input">
                                        <el-input
                                            v-model="formBox.inputGroupzz" class="el-input-style" placeholder="请输入姓名"
                                            @focus="inputFocus('inputGroupzz')" @blur="inputBlur('inputGroupzz')"
                                        />
                                    </el-form-item>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="picc-form-inputgroup">
                                    <el-form-item
                                        v-picc-input-error prop="inputGroup" label="下拉选择器与输入框组合"
                                        class="picc-form-inputgroup-select picc-form-label-linefeed picc-linefeed--medium"
                                    >
                                        <el-select v-model="selectOper.insuranceShow2" placeholder="请选择">
                                            <el-option
                                                v-for="(item, index) in inputGroup" :key="index"
                                                :label="`${item.num}-${item.value}`" :value="item.value"
                                            />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item class="picc-form-inputgroup-input">
                                        <el-input v-model="formBox.typeCodev" class="el-input-style" placeholder="请输入姓名" />
                                    </el-form-item>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="picc-form-inputgroup">
                                    <el-form-item
                                        label="下拉选择器与输入框组合"
                                        class="picc-form-inputgroup-select picc-form-label-linefeed picc-linefeed--medium"
                                    >
                                        <el-select v-model="selectOper.insuranceShow3" placeholder="请选择">
                                            <el-option
                                                v-for="(item, index) in inputGroup" :key="index"
                                                :label="`${item.num}-${item.value}`" :value="item.value"
                                            />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item class="picc-form-inputgroup-input">
                                        <el-input v-model="formBox.typeCodew" class="el-input-style" placeholder="请输入姓名" />
                                    </el-form-item>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <!-- <el-col :span="8">
                                <el-form-item prop="doublecheck" label="联动双选框" class="doublecheck">
                                    <el-input
                                        v-model="formBox.doublecheck"
                                        class="picc-icon-btn-input casualClass0"
                                        suffix-icon="el-icon-search"
                                        placeholder="请选择"
                                    />
                                    <el-input v-model="formBox.doublecheck1" class="picc-icon-btn-input casualClass0" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="doublecheck2" label="联动双选框二级不可编辑" class="doublecheck">
                                    <el-input
                                        v-model="formBox.doublecheck2"
                                        class="picc-icon-btn-input casualClass0"
                                        suffix-icon="el-icon-search"
                                        placeholder="请选择"
                                    />
                                    <el-input v-model="formBox.doublecheck3" disabled class="picc-icon-btn-input casualClass0" />
                                </el-form-item>
                            </el-col> -->
                            <el-col :span="8">
                                <div class="picc-form-inputgroup">
                                    <el-form-item
                                        label="带筛选条件的搜索框与输入框组合"
                                        class="picc-form-label-linefeed picc-linefeed--medium doublecheck"
                                    >
                                        <div class="picc-form-item-muti">
                                            <el-autocomplete
                                                v-model="formBox.doublecheck4" class="picc-form-item-muti-item"
                                                suffix-icon="el-icon-search" :fetch-suggestions="querySearch"
                                                placeholder="请选择" @select="handleSelect"
                                            />
                                            <el-input v-model="formBox.doublecheck3" class="picc-form-item-muti-item" />
                                            <div class="picc-form-item-muti">
                                                <el-tooltip class="item" effect="dark" content="关闭活动" placement="top-start">
                                                    <input type="checkbox" name="vehicle" value="Car" checked="checked" />
                                                </el-tooltip>
                                                <span v-if="screenWidth()" class="picc-form-item-checkbox">关闭活动</span>
                                            </div>
                                        </div>
                                    </el-form-item>
                                    <!-- <el-form-item class="picc-form-inputgroup-input">
                                        <el-input v-model="formBox.typeCodew" class="el-input-style" placeholder="请输入姓名" />
                                    </el-form-item> -->
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="picc-form-inputgroup">
                                    <el-form-item
                                        label="带筛选条件的搜索框与输入框组合"
                                        class="picc-form-label-linefeed picc-linefeed--medium doublecheck"
                                    >
                                        <div class="picc-form-item-muti">
                                            <el-input
                                                v-model="formBox.doublecheck5" class="picc-form-item-muti-item"
                                                suffix-icon="el-icon-search" placeholder="请选择"
                                            />
                                            <!-- <el-input v-model="formBox.doublecheck3" class="picc-form-item-muti-item" /> -->
                                            <div class="picc-form-item-muti">
                                                <el-tooltip class="item" effect="dark" content="关闭活动" placement="top-start">
                                                    <input type="checkbox" name="vehicle" value="Car" checked="checked" />
                                                </el-tooltip>
                                                <span v-if="screenWidth()" class="picc-form-item-checkbox">关闭活动一二三四五</span>
                                            </div>
                                        </div>
                                    </el-form-item>
                                    <!-- <el-form-item class="picc-form-inputgroup-input">
                                        <el-input v-model="formBox.typeCodew" class="el-input-style" placeholder="请输入姓名" />
                                    </el-form-item> -->
                                </div>
                            </el-col>
                            <!-- </el-row>
                        <el-row> -->
                            <el-col :span="8">
                                <div class="picc-form-inputgroup">
                                    <el-form-item
                                        label="带筛选条件的搜索框与输入框组合"
                                        class="picc-form-label-linefeed picc-linefeed--medium doublecheck"
                                    >
                                        <el-input
                                            v-model="formBox.doublecheck2" class="picc-icon-btn-input casualClass0"
                                            suffix-icon="el-icon-search" placeholder="请选择"
                                        />
                                        <el-input
                                            v-model="formBox.doublecheck6" class="picc-icon-btn-input casualClass0"
                                            placeholder="请输入姓名"
                                        />
                                    </el-form-item>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item prop="delivery" label="即时配送">
                                    <el-switch v-model="formBox.delivery" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="即时配送">
                                    <el-switch v-model="formBox.delivery1" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="12">
                                <el-form-item label="带滑杆">
                                    <Slider :number="number" @change="backFn" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="12">
                                <el-form-item label="单选按钮">
                                    <el-radio-group v-model="radio1">
                                        <el-radio-button label="上海" />
                                        <el-radio-button label="北京" />
                                        <el-radio-button label="广州" />
                                        <el-radio-button label="深圳" />
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <el-button @click="ddd">
                    点击
                </el-button>
            </div>

            <BoxHead title="上下间距紧凑版(盒子高度44px)" class="picc-box-head-compact">
                <div slot="outsideOperation">
                </div>
            </BoxHead>
            <div class="picc-container" style="margin:0 24px">
                <div class="picc-el-form">
                    <el-form ref="dialogBox1" :model="formBox" label-width="120px" :rules="rules">
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="typeCodea" label="关系人名称"
                                    class="picc-linefeed--medium picc-form-compact"
                                >
                                    <el-input
                                        ref="input" v-model="formBox.typeCodea"
                                        v-input-overflow-popup="formBox.typeCodea" class="el-input-style"
                                        placeholder="输入文本信息" clearable @focus="inputFocus('typeCodea')"
                                        @blur="inputBlur('typeCodea')"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="报案号" class="picc-linefeed--medium picc-form-compact">
                                    <el-input
                                        v-model="formBox.typeCode" v-popover:popover2 class="el-input-style"
                                        placeholder=""
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item
                                    v-picc-input-error prop="insuranceShow" label="证件类型"
                                    class="picc-linefeed--medium picc-form-compact"
                                >
                                    <el-select v-model="selectOper.insuranceShow" placeholder="身份证">
                                        <el-option
                                            v-for="(item, index) in selectOper.insurance" :key="index"
                                            :label="`${item.num}-${item.value}`" :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item v-picc-input-error prop="resource" label="关系人类型" class="picc-form-compact">
                                    <el-radio-group v-model="formBox.resource">
                                        <el-radio label="是" />
                                        <el-radio label="否" />
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item v-picc-input-error prop="types" label="复选框" class="picc-form-compact">
                                    <el-checkbox-group v-model="formBox.types">
                                        <el-checkbox label="投保人" name="types" />
                                        <el-checkbox label="被保险人" name="types" />
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="num" label="数字输入框" class="picc-form-compact">
                                    <el-input-number v-model="formBox.num" :min="1" :max="10" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="8">
                                <el-form-item label="日期范围1" class="picc-form-compact">
                                    <el-date-picker
                                        v-model="timePicker.timePicker3" type="daterange" :format="dateFormat"
                                        data-mode="dateFormat" range-separator="至" start-placeholder="开始日期"
                                        end-placeholder="结束日期" @change="test"
                                        @blur="datePickerArea($event, timePicker.timePicker3)"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="日期范围2" class="picc-form-compact">
                                    <DatepickerRange
                                        format="YYYY/MM/DD" value-format="YYYY/MM/DD" placeholder="选择日期范围..."
                                        message="请选择结束日期"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item prop="delivery" label="即时配送" class="picc-form-compact">
                                    <el-switch v-model="formBox.delivery" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>

        <el-dialog title="选择产品方案" :visible.sync="dialogFormVisible">
            <el-table :data="gridData">
                <el-table-column property="date" label="日期" width="150" />
                <el-table-column property="name" label="姓名" width="200" />
                <el-table-column property="address" label="地址" />
            </el-table>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">
                    取 消
                </el-button>
                <el-button type="primary" @click="dialogFormVisible = false">
                    确 定
                </el-button>
            </div>
        </el-dialog>
        <el-popover ref="popover" placement="top" width="200" trigger="focus" popper-class="picc-popover-custom">
            <!--content="禁用提示信息"-->
            <span><i class="el-icon-info"></i> 禁用提示信息popover</span>
        </el-popover>
        <el-popover ref="popover2" placement="top" width="200" trigger="focus" popper-class="picc-popover-custom">
            <!--content="禁用提示信息"-->
            <span><i class="el-icon-info"></i> 禁用提示信息popover2</span>
        </el-popover>
    </div>
</template>

<script>
import { dateFormat } from "@/mixins/index";
export default {
    name: "Form",
    mixins: [dateFormat],
    data() {
        return {
            formBox: {
                inputGroupzz: "",
                textLong: "",
                typeCodev: "",
                typeCodew: "",
                typeCodea: "",
                typeCode: "",
                foldLinea: "",
                foldLine: "",
                tipInputa: "",
                tipInput: "",
                visible: false,
                visible1: false,
                visible2: false,
                visible3: false,
                visible4: false,
                btnInputBox: "",
                btnInputBox2: "",
                iconBtnInputBox: "",
                resource: "",
                resource1: "是",
                resource2: "",
                types: [],
                types1: ["投保人"],
                types2: [],
                num: "",
                num1: "",
                delivery: true,
                delivery1: false,
                doublecheck: "",
                doublecheck1: "",
                doublecheck2: "",
                doublecheck3: "",
                doublecheck4: "",
                doublecheck5: "",
                doublecheck6: ""
            },
            // 文字气泡提示
            tooltip: false,
            /* 下拉选择器 */
            selectOper: {
                validStatus: "",
                insuranceShow: "",
                insurance: [{ num: "01", value: "普通家庭财产保险1" },
                    { num: "02", value: "普通家庭财产保险2" },
                    { num: "03", value: "普通家庭财产保险3" },
                    { num: "04", value: "普通家庭财产保险财产保险1" },
                    { num: "05", value: "普通家庭财产保险财产保险2" },
                    { num: "06", value: "普通家庭财产保险财产保险3" },
                    { num: "07", value: "普通家庭财产保险4" },
                    { num: "08", value: "普通家庭财产保险财产保险4" },
                    { num: "09", value: "普通家庭财产保险5" },
                    { num: "10", value: "普通家庭财产保险财产保险5" }],
                insuranceShow1: "",
                insuranceShow2: "",
                insuranceShow3: "",
                insurance1: [{ num: "01", value: "普通家庭财产保险1" },
                    { num: "02", value: "普通家庭财产保险财产保险3普通家庭财产保险财产保险" },
                    { num: "03", value: "普通家庭财产保险3" }],
                options: [{
                    label: "热门城市", options: [{ value: "Shanghai", label: "上海" },
                        { value: "Beijing", label: "北京" }]
                },
                {
                    label: "城市名", options: [{ value: "Chengdu", label: "成都" },
                        { value: "Shenzhen", label: "深圳" },
                        { value: "Guangzhou", label: "广州" },
                        { value: "Dalian", label: "大连" }]
                }],
                optionsValue: ""
            },
            /* 下拉选择器 - 模糊搜索兼下拉选择 */
            fuzzySearch: {
                fuzzynn: "",
                fuzzy: "",
                options: [{ value: "选项1", label: "黄金糕" },
                    { value: "选项2", label: "双皮奶" },
                    { value: "选项3", label: "蚵仔煎" },
                    { value: "选项4", label: "龙须面" },
                    { value: "选项5", label: "北京烤鸭" }]
            },
            /* 下拉选择器 - 不可用状态 */
            disabledOper: {
                disabledOper: "",
                validStatus: "",
                options: [{ value: "选项1", label: "黄金糕" },
                    { value: "选项2", label: "双皮奶" },
                    { value: "选项3", label: "蚵仔煎" },
                    { value: "选项4", label: "龙须面" },
                    { value: "选项5", label: "北京烤鸭" }]
            },
            /* 下拉选择器 - 级联选择 */
            cascadeSelection: {
                options: [{
                    value: "zhinan", label: "指南", children: [
                        {
                            value: "shejiyuanze", label: "设计原则", children: [
                                { value: "yizhi", label: "一致" },
                                { value: "fankui", label: "反馈" },
                                { value: "xiaolv", label: "效率" },
                                { value: "kekong", label: "可控" }]
                        },
                        {
                            value: "daohang", label: "导航", children: [
                                { value: "cexiangdaohang", label: "侧向导航" },
                                { value: "dingbudaohang", label: "顶部导航" }]
                        }]
                },
                {
                    value: "zujian", label: "组件", children: [
                        {
                            value: "basic", label: "Basic", children: [
                                { value: "layout", label: "Layout 布局" },
                                { value: "color", label: "Color 色彩" },
                                { value: "typography", label: "Typography 字体" },
                                { value: "icon", label: "Icon 图标" },
                                { value: "button", label: "Button 按钮" }]
                        },
                        {
                            value: "form", label: "Form", children: [
                                { value: "radio", label: "Radio 单选框" },
                                { value: "checkbox", label: "Checkbox 多选框" },
                                { value: "input", label: "Input 输入框" },
                                { value: "input-number", label: "InputNumber 计数器" },
                                { value: "select", label: "Select 选择器" },
                                { value: "cascader", label: "Cascader 级联选择器" },
                                { value: "switch", label: "Switch 开关" },
                                { value: "slider", label: "Slider 滑块" },
                                { value: "time-picker", label: "TimePicker 时间选择器" },
                                { value: "date-picker", label: "DatePicker 日期选择器" },
                                { value: "datetime-picker", label: "DateTimePicker 日期时间选择器" },
                                { value: "uploadIDcard.png", label: "Upload 上传" },
                                { value: "rate", label: "Rate 评分" },
                                { value: "form", label: "Form 表单" }]
                        },
                        {
                            value: "data", label: "Data", children: [
                                { value: "table", label: "Table 表格" },
                                { value: "tag", label: "Tag 标签" },
                                { value: "progress", label: "Progress 进度条" },
                                { value: "tree", label: "Tree 树形控件" },
                                { value: "pagination", label: "Pagination 分页" },
                                { value: "badge", label: "Badge 标记" }]
                        },
                        {
                            value: "notice", label: "Notice", children: [
                                { value: "alert", label: "Alert 警告" },
                                { value: "loading", label: "Loading 加载" },
                                { value: "message", label: "Message 消息提示" },
                                { value: "message-box", label: "MessageBox 弹框" },
                                { value: "notification", label: "Notification 通知" }]
                        },
                        {
                            value: "navigation", label: "Navigation", children: [
                                { value: "menu", label: "NavMenu 导航菜单" },
                                { value: "tabs", label: "Tabs 标签页" },
                                { value: "breadcrumb", label: "Breadcrumb 面包屑" },
                                { value: "dropdown", label: "Dropdown 下拉菜单" },
                                { value: "steps", label: "Steps 步骤条" }]
                        },
                        {
                            value: "others", label: "Others", children: [
                                { value: "dialog", label: "Dialog 对话框" },
                                { value: "tooltip", label: "Tooltip 文字提示" },
                                { value: "popover", label: "Popover 弹出框" },
                                { value: "card", label: "Card 卡片" },
                                { value: "carousel", label: "Carousel 走马灯" },
                                { value: "collapse", label: "Collapse 折叠面板" }]
                        }]
                },
                {
                    value: "ziyuan", label: "资源", children: [
                        { value: "axure", label: "Axure Components" },
                        { value: "sketch", label: "Sketch Templates" },
                        { value: "jiaohu", label: "组件交互文档" }
                    ]
                }
                ],
                selectedOptions: [],
                selectedOptions1: []
            },
            datePicker: {
                datePicker1: ""
            },
            timePicker: {
                timePicker1: "",
                timePicker2: "",
                timePicker3: ""
            },
            /* 下拉选择器与输入框结合 */
            inputGroup: [{ num: "01", value: "角色一" },
                { num: "02", value: "角色二" },
                { num: "03", value: "角色三" }
            ],
            rules: {
                selectedOptiona: [{ required: true, message: "模糊搜索兼下拉选择", trigger: "blur" }],
                fuzzynn: [{ required: true, message: "模糊搜索兼下拉选择", trigger: "blur" }],
                typeCodea: [{ required: true, message: "请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码请输入类型代码", trigger: "blur" }],
                typeCode: [{ required: true, message: "请输入类型代码", trigger: "blur" }],
                foldLinea: [{ required: true, message: "请输入带提示说明输入框请输入带提示说明输入框", trigger: "blur" }],
                foldLine: [{ required: true, message: "请输入带提示说明输入框请输入带提示说明输入框", trigger: "blur" }],
                tipInputa: [{ required: true, message: "请输入带提示说明输入框请输入带提示说明输入框请输入带提示说明输入框请输入带提示说明输入框", trigger: "blur" }],
                tipInput: [{ required: true, message: "请输入带提示说明输入框", trigger: "blur" }],
                btnInputBox: [{ required: true, message: "点击选择产品方案", trigger: "blur" }],
                iconBtnInputBox: [{ required: true, message: "点击选择产品方案", trigger: "blur" }],
                insuranceShow: [{ required: true, message: "请选择效力状态", trigger: "blur" }],
                fuzzySearch: [{ required: true, message: "模糊搜索兼下拉选择", trigger: "blur" }],
                disabledOper: [{ required: true, message: "请选择", trigger: "blur" }],
                resource: [{ required: true, message: "请选择关系人类型", trigger: "blur" }],
                types: [{ required: true, message: "请选择", trigger: "blur" }],
                num: [{ required: true, message: "请选择", trigger: "blur" }],
                delivery: [{ required: true, message: "请选择", trigger: "blur" }],
                inputGroup: [{ required: true, message: "请输入", trigger: "blur" }],
                inputGroupzz: [{ required: true, message: "请输入", trigger: "blur" }],
                inputReq: [{ required: true, message: "请选择-下拉选择器与输入框组合", trigger: "blur" }]
            },
            /* dialog弹框数据 - start */
            gridData: [
                {
                    date: "2016-05-02",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-04",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-01",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-03",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                }
            ],
            dialogFormVisible: false,
            /* dialog弹框数据 - end */
            number: 60,
            tabs_state: [
                { size: "", title: "立案申请", type: "status", num: 0, selected: true, state: "success" },
                { size: "", title: "查勘定损", type: "status", num: 2, selected: false, state: "reject" },
                { size: "", title: "领款人登记", type: "status", num: 5, selected: false, state: "waiting" },
                { size: "", title: "理算申请", type: "status", num: 5, selected: false, state: "" },
                { size: "", title: "一站式案件处理", type: "status", num: 5, selected: false, state: "" }
            ],
            restaurants: [],
            tabs_addItem: [
                { title: "第三方机构", type: "addItem" },
                { title: "关键环节管控", type: "addItem" },
                { title: "保险合同诉讼", type: "addItem" },
                { title: "预赔付", type: "addItem" },
                { title: "担保信息", type: "addItem" },
                { title: "损余录入", type: "addItem" },
                { title: "残值处理", type: "addItem" },
                { title: "追偿发起", type: "addItem" },
                { title: "查勘定损", type: "addItem", state: "reject" },
                { title: "领款人登记", type: "addItem", state: "chose" }
            ],
            add: false,
            radio1: "上海"
        };
    },

    mounted() {
        document.body.addEventListener("mouseover", function (e) {
            // eslint-disable-next-line eqeqeq
            if (e.target.className == "el-form-item__error") {
                // console.log(e)
            }
        });
        this.restaurants = this.loadAll();

        // this.handleIconBtn(); /* 带图标按钮输入框 - 打开弹窗 */
    },
    methods: {
        querySearch(queryString, cb) {
            var restaurants = this.restaurants;
            var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        createFilter(queryString) {
            return (restaurant) => {
                return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
            };
        },
        loadAll() {
            return [
                { value: "三全鲜食（北新泾店）", address: "长宁区新渔路144号" },
                { value: "Hot honey 首尔炸鸡（仙霞路）", address: "上海市长宁区淞虹路661号" },
                { value: "新旺角茶餐厅", address: "上海市普陀区真北路988号创邑金沙谷6号楼113" },
                { value: "泷千家(天山西路店)", address: "天山西路438号" },
                { value: "胖仙女纸杯蛋糕（上海凌空店）", address: "上海市长宁区金钟路968号1幢18号楼一层商铺18-101" },
                { value: "贡茶", address: "上海市长宁区金钟路633号" },
                { value: "豪大大香鸡排超级奶爸", address: "上海市嘉定区曹安公路曹安路1685号" },
                { value: "茶芝兰（奶茶，手抓饼）", address: "上海市普陀区同普路1435号" },
                { value: "十二泷町", address: "上海市北翟路1444弄81号B幢-107" },
                { value: "星移浓缩咖啡", address: "上海市嘉定区新郁路817号" },
                { value: "阿姨奶茶/豪大大", address: "嘉定区曹安路1611号" },
                { value: "新麦甜四季甜品炸鸡", address: "嘉定区曹安公路2383弄55号" },
                { value: "Monica摩托主题咖啡店", address: "嘉定区江桥镇曹安公路2409号1F，2383弄62号1F" },
                { value: "浮生若茶（凌空soho店）", address: "上海长宁区金钟路968号9号楼地下一层" },
                { value: "NONO JUICE  鲜榨果汁", address: "上海市长宁区天山西路119号" },
                { value: "CoCo都可(北新泾店）", address: "上海市长宁区仙霞西路" },
                { value: "快乐柠檬（神州智慧店）", address: "上海市长宁区天山西路567号1层R117号店铺" },
                { value: "Merci Paul cafe", address: "上海市普陀区光复西路丹巴路28弄6号楼819" },
                { value: "猫山王（西郊百联店）", address: "上海市长宁区仙霞西路88号第一层G05-F01-1-306" },
                { value: "枪会山", address: "上海市普陀区棕榈路" },
                { value: "纵食", address: "元丰天山花园(东门) 双流路267号" },
                { value: "钱记", address: "上海市长宁区天山西路" },
                { value: "壹杯加", address: "上海市长宁区通协路" },
                { value: "唦哇嘀咖", address: "上海市长宁区新泾镇金钟路999号2幢（B幢）第01层第1-02A单元" },
                { value: "爱茜茜里(西郊百联)", address: "长宁区仙霞西路88号1305室" },
                { value: "爱茜茜里(近铁广场)", address: "上海市普陀区真北路818号近铁城市广场北区地下二楼N-B2-O2-C商铺" },
                { value: "鲜果榨汁（金沙江路和美广店）", address: "普陀区金沙江路2239号金沙和美广场B1-10-6" },
                { value: "开心丽果（缤谷店）", address: "上海市长宁区威宁路天山路341号" },
                { value: "超级鸡车（丰庄路店）", address: "上海市嘉定区丰庄路240号" },
                { value: "妙生活果园（北新泾店）", address: "长宁区新渔路144号" },
                { value: "香宜度麻辣香锅", address: "长宁区淞虹路148号" },
                { value: "凡仔汉堡（老真北路店）", address: "上海市普陀区老真北路160号" },
                { value: "港式小铺", address: "上海市长宁区金钟路968号15楼15-105室" },
                { value: "蜀香源麻辣香锅（剑河路店）", address: "剑河路443-1" },
                { value: "北京饺子馆", address: "长宁区北新泾街道天山西路490-1号" },
                { value: "饭典*新简餐（凌空SOHO店）", address: "上海市长宁区金钟路968号9号楼地下一层9-83室" },
                { value: "焦耳·川式快餐（金钟路店）", address: "上海市金钟路633号地下一层甲部" },
                { value: "动力鸡车", address: "长宁区仙霞西路299弄3号101B" },
                { value: "浏阳蒸菜", address: "天山西路430号" },
                { value: "四海游龙（天山西路店）", address: "上海市长宁区天山西路" },
                { value: "樱花食堂（凌空店）", address: "上海市长宁区金钟路968号15楼15-105室" },
                { value: "壹分米客家传统调制米粉(天山店)", address: "天山西路428号" },
                { value: "福荣祥烧腊（平溪路店）", address: "上海市长宁区协和路福泉路255弄57-73号" },
                { value: "速记黄焖鸡米饭", address: "上海市长宁区北新泾街道金钟路180号1层01号摊位" },
                { value: "红辣椒麻辣烫", address: "上海市长宁区天山西路492号" },
                { value: "(小杨生煎)西郊百联餐厅", address: "长宁区仙霞西路88号百联2楼" },
                { value: "阳阳麻辣烫", address: "天山西路389号" },
                { value: "南拳妈妈龙虾盖浇饭", address: "普陀区金沙江路1699号鑫乐惠美食广场A13" }
            ];
        },
        handleSelect(item) {
            console.log(item);
        },
        // 显示气泡
        // showTooltip() {
        //     const input = this.$refs.input.getInput();
        //     this.tooltip = input.offsetWidth < input.scrollWidth;
        // },
        screenWidth() {
            return document.body.clientWidth >= 1600;
        },
        //
        test(e) {
            // console.log(e);
        },
        ddd() {
            this.$refs.dialogBox.validate((valid) => {
                if (valid) {
                    alert("submit!");
                } else {
                    // console.log("error submit!!");
                    return false;
                }
            });
        },

        /* 日期范围 */
        datePickerArea(event, target) {
            const inputBox = event.$el.querySelectorAll("input");
            if (target !== "") {
                for (let i = 0; i < inputBox.length; i++) {
                    inputBox[i].style.color = "#292B34";
                    inputBox[i].style.fontSize = "12px";
                }
            } else {
                for (let j = 0; j < inputBox.length; j++) {
                    inputBox[j].style.color = "#909199";
                    inputBox[j].style.fontSize = "14px";
                }
            }
        },

        /* 按钮输入框 */
        isShow(target) {
            const $target = document.querySelector("." + target + " input");
            $target.style.borderColor = "#234dcc";
            $target.style.color = "#1C61FC";
        },
        isHide(target) {
            const $target = document.querySelector("." + target + " input");
            $target.style.borderColor = "#cdcdcd";
            $target.style.color = "#909199";
        },

        /* input 标签获取/失去焦点 - 错误信息消失/显示 */
        // inputFocus(target) {
        //   console.log(target)
        //   const $parent = document.getElementById(target).parentNode.parentNode
        //   const $root = $parent.parentNode
        //   const $target = $parent.lastChild
        //   // eslint-disable-next-line eqeqeq
        //   if ($target.className == 'el-form-item__error') {
        //     $root.style.marginBottom = '0px'
        //     $target.style.display = 'none'
        //   }
        // },
        // inputBlur(target) {
        //   const $parent = document.getElementById(target).parentNode.parentNode
        //   const $root = $parent.parentNode
        //   const $target = $parent.lastChild
        //   // eslint-disable-next-line eqeqeq
        //   if ($target.className == 'el-form-item__error') {
        //     $root.style.marginBottom = '52px'
        //     $target.style.display = 'block'
        //
        //     // document.querySelector('.el-form-item__error:before').addEventListener('onmouseover', function(e) {
        //     //   console.log(e)
        //     // })
        //   }
        // },
        inputBlur: function (target) {
            // this.rules[target][0].required = true
        },
        inputFocus: function (target) {
            // this.rules[target][0].required = false
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        },

        /* 带图标按钮输入框 */
        handleIconBtn() {
            const inputBox = document.querySelector(".casualClass");
            const _this = this;
            inputBox.onclick = function (ev) {
                // eslint-disable-next-line no-redeclare
                var ev = ev || window.event;
                // console.log(ev)
                const oLi = ev.srcElement || ev.target;

                if (oLi.nodeName.toLowerCase() === "i") {
                    _this.formBox.visible3 = true;
                    _this.formBox.iconBtnInputBox = "带图标按钮输入框";
                }
            };
        },
        // 带滑杆
        backFn(data) {
            this.number = data;
        },
        // 切换标签
        choose(data, index) {
            this[data] = this[data].map((v, i) => {
                v.selected = i === index;
                return v;
            });
        },
        addItem() {
            this.add = !this.add;
        },
        pushItem(value) {
            if (value.state === "reject" || value.state === "chose") return;
            const addOne = Object.assign({}, value, { type: "status" });
            this.tabs_state.push(addOne);
            value.state = "chose";
        },
        changeSize() {
            if (this.tabs_state[0].size) {
                this.tabs_state = this.tabs_state.map(function (v) {
                    v.size = "";
                    return v;
                });
            } else {
                this.tabs_state = this.tabs_state.map(function (v) {
                    v.size = "mini";
                    return v;
                });
            }
            // this.$nextTick();
        }
        /*
handleIconBtn() {
  const inputBox = document.querySelectorAll('.casualClass')
  var _this = this
  for (var i = 0; i < inputBox.length; i++) {
    inputBox[i].onclick = function(ev) {
      // eslint-disable-next-line no-redeclare
      var ev = ev || window.event
      var oLi = ev.srcElement || ev.target
      var targetPopover = oLi.getAttribute('picc-data-popover')
      console.log(targetPopover)
      if (oLi.nodeName.toLowerCase() === 'i') {
        _this.formBox.visible4 = true
        _this.formBox.iconBtnInputBox = '带图标按钮输入框'
      }
    }
  }
}
* */
    }
};
</script>
<style lang="scss" scoped>
::v-deep .demo_search_result {

    // & #placeholder {
    //     margin-right: 20px;
    // }
    // & .el-popover__title {
    //     color: #292b34 !important;
    //     // background: #edeef0 !important;
    // }
    .el-input__inner {
        padding-left: 30px !important;
    }

    // .el-table th.el-table__cell {
    //     background-color: #edeef0;
    // }
}

.demo_state {
    padding: 20px 0 0 0;

    .addButton {
        float: right;
        margin-right: 16px;
        margin-top: 6px;
    }

    .add_layer {
        font-size: 14px;
        margin-bottom: 29px;
        padding-top: 32px;

        span {
            display: inline-block;
            margin-left: 18px;
            color: #001aff;
        }
    }
}

.items {
    margin-bottom: 20px;
}

.demo_mini {
    padding-top: 10px;
}

::v-deep .el-radio-button__inner:hover {
    color: #606266 !important;
}

// 联动双选框
::v-deep .el-form-item.doublecheck {
    .el-form-item__content {
        display: flex;

        .picc-icon-btn-input {
            &:first-child {
                margin-right: 8px;
                width: 90px;
            }

            &:last-child {
                width: 100px;
            }
        }
    }
}

//  联动双选框适配规则
@media screen and (min-width: 1919px) {
    .el-form-item.doublecheck .el-form-item__content .picc-icon-btn-input:first-child {
        width: 124px !important;
    }

    .el-form-item.doublecheck .el-form-item__content .picc-icon-btn-input:last-child {
        width: 258px !important;
    }
}

.picc-form-item-muti {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.picc-form-item-muti-item {
    flex: 1;
    padding-right: 8px;
    min-width: 90px;
}

.picc-form-item-checkbox {
    overflow: hidden;
    word-break: break-all;
    width: 58px;
    line-height: 12px;
    font-size: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

::v-deep .picc-form-item-muti-item .el-input__suffix {
    right: 13px !important;
}

// 顶部提示信息样式
.top-tip-div {
    width: 100%;
    background-color: #fff;
    position: fixed;
    top: 92px;
    z-index: 5;

    .top-tip {
        width: 100%;
        background-color: rgba($color: #ffe4e0, $alpha: 0.8);
        padding: 14px 0 14px 24px;
        p {
            font-size: 14px;
            color: #292b34;
            line-height: 16px;
            margin: 0;
        }

        span {
            color: red;
        }
    }
}

::v-deep .picc-box-head {
    justify-content: left;

    .span-red {
        margin-left: 6px;
        color: red;
        font-size: 14px;
    }
}
</style>
