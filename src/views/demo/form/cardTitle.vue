<template>
    <div style="padding:30px;">
        <h1>主要为支付方式样例，其中内页卡片内/外标题栏以及动态增减表单项/显示隐藏输入框已抽为组件</h1>

        <!-- 内页卡片内标题栏 -->
        <div class="picc-card-inside-titleBar">
            <div class="picc-title-area">
                客户信息01
            </div>
            <div class="picc-operation-area">
                <span>重置</span>
                <span class="picc-operation-primary">编辑</span>
                <span class="picc-operation-primary">OCR识别</span>
                <span class="picc-operation-superior">
                    <i class="el-icon-delete-solid"></i>
                </span>
            </div>
        </div>
        <!-- <div style="margin: 30px;" /> -->

        <!-- 内页卡片外标题栏 changeBg-颜色切换-->
        <div class="picc-card-outside-titleBar ">
            <div class="picc-title-area">
                <i></i><span>客户信息</span>
            </div>
            <div class="picc-operation-area">
                <span class="picc-operation-primary">操作02</span>
                <span class="picc-operation-primary">操作01</span>
            </div>
        </div>

        <!-- 内页卡片外标题栏 changeBg-颜色切换-->
        <div class="picc-card-outside-titleBar ">
            <div class="picc-title-area">
                <i></i><span>客户信息</span>
            </div>
            <div class="picc-operation-area">
                <span class="picc-operation-primary">操作02</span>
                <span class="picc-operation-primary">操作01</span>
            </div>
        </div>

        <!-- 选择支付方式 -->
        <div class="picc-container-box">
            <div class="picc-container">
                <ul ref="payWay" class="picc-choose-pay-way">
                    <li v-for="(item, index) of payway" :key="index" @click="payWay(index)">
                        <i :class="'picc-icon picc-icon-' + item.img"></i>
                        <el-radio v-model="radio" :label="item.label">
                            {{ item.value }}
                        </el-radio>
                    </li>
                </ul>
            </div>
        </div>
        <div style="margin: 30px;"></div>

        <!--3-7-21 动态增减表单项1：添加卡片-->
        <div class="picc-card-outside-titleBar ">
            <div class="picc-title-area">
                <i></i><span>客户信息 --- 方案A</span>
            </div>
            <div class="picc-operation-area">
                <span class="picc-operation-primary">添加客户</span>
            </div>
        </div>
        <div class="picc-container-box">
            <div class="picc-container">
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        客户信息01
                    </div>
                    <div class="picc-operation-area">
                        <span>重置</span>
                        <span class="picc-operation-primary">编辑</span>
                        <span class="picc-operation-primary">OCR识别</span>
                        <span class="picc-operation-superior"><i class="el-icon-delete-solid"></i></span>
                    </div>
                </div>
                <div class="picc-el-form">
                    <el-form :model="formBox" label-width="120px" :rules="rules">
                        <el-row type="flex" class="row-bg">
                            <el-col :span="8">
                                <el-form-item label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-input v-model="formBox.typeCode" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="类型代码">
                                    <el-input v-model="formBox.typeCode" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                    </el-form>
                </div>
                <div class="picc-form-area-unfold">
                    <div v-show="!isOpen" class="picc-form-area-unfold--border"></div>
                    <div v-show="!isOpen" class="picc-form-area-unfold--formbox">
                        <el-form :model="formBox" label-width="120px" :rules="rules">
                            <el-row type="flex" class="row-bg">
                                <el-col :span="8">
                                    <el-form-item label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                                        <el-input v-model="formBox.typeCode" class="el-input-style" placeholder="输入文本信息" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="类型代码">
                                        <el-input v-model="formBox.typeCode" class="el-input-style" placeholder="输入文本信息" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8" />
                            </el-row>
                        </el-form>
                    </div>
                    <div class="picc-form-area-unfold--switch">
                        <el-button v-if="isOpen" type="text" @click="isOpen = !isOpen">
                            展开<i class="el-icon-caret-bottom"></i>
                        </el-button>
                        <el-button v-else type="text" @click="isOpen = !isOpen">
                            收起<i class="el-icon-caret-top"></i>
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        <div style="margin: 30px;"></div>
        <div class="picc-card-outside-titleBar ">
            <div class="picc-title-area">
                <i></i><span>客户信息 --- 方案B</span>
            </div>
        </div>
        <div class="picc-container-box">
            <div class="picc-container">
                <div class="picc-card-inside-titleBar">
                    <div class="picc-title-area">
                        客户信息01
                    </div>
                    <div class="picc-operation-area">
                        <span>重置</span>
                        <span class="picc-operation-primary">编辑</span>
                        <span class="picc-operation-primary">OCR识别</span>
                        <span class="picc-operation-superior"><i class="el-icon-delete-solid"></i></span>
                    </div>
                </div>
                <div class="picc-el-form">
                    <el-form :model="formBox" label-width="120px" :rules="rules">
                        <el-row type="flex" class="row-bg">
                            <el-col :span="8">
                                <el-form-item label="文本输入框错误状态" class="picc-form-label-linefeed picc-linefeed--medium">
                                    <el-input v-model="formBox.typeCode" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="类型代码">
                                    <el-input v-model="formBox.typeCode" class="el-input-style" placeholder="输入文本信息" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" />
                        </el-row>
                    </el-form>
                </div>
            </div>
            <div class="picc-form-card--add">
                <i class="el-icon-plus"></i><span>添加客户</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "CardTitle",
    data() {
        return {
            radio: "",
            formBox: {
                typeCode: "",
                foldLine: "",
                tipInput: "",
                visible: false,
                visible1: false,
                visible2: false,
                visible3: false,
                visible4: false,
                btnInputBox: "",
                iconBtnInputBox: "",
                resource: "",
                resource1: "是",
                resource2: "",
                types: [],
                types1: ["投保人"],
                types2: [],
                num: "",
                num1: "",
                delivery: true,
                delivery1: false
            },
            rules: {
                typeCode: [{ required: true, message: "请输入类型代码", trigger: "blur" }],
                foldLine: [{ required: true, message: "请输入带提示说明输入框请输入带提示说明输入框", trigger: "blur" }],
                tipInput: [{ required: true, message: "请输入带提示说明输入框", trigger: "blur" }],
                btnInputBox: [{ required: true, message: "点击选择产品方案", trigger: "blur" }],
                iconBtnInputBox: [{ required: true, message: "点击选择产品方案", trigger: "blur" }],
                insuranceShow: [{ required: true, message: "请选择效力状态", trigger: "blur" }],
                fuzzySearch: [{ required: true, message: "模糊搜索兼下拉选择", trigger: "blur" }],
                disabledOper: [{ required: true, message: "请选择", trigger: "blur" }],
                resource: [{ required: true, message: "请选择关系人类型", trigger: "blur" }],
                types: [{ required: true, message: "请选择", trigger: "blur" }],
                num: [{ required: true, message: "请选择", trigger: "blur" }],
                delivery: [{ required: true, message: "请选择", trigger: "blur" }],
                inputGroup: [{ required: true, message: "请选择-下拉选择器与输入框组合", trigger: "blur" }]
            },
            unflod: false,
            isOpen: true,
            payway: [
                { img: "wechat", label: 0, value: "微信" },
                { img: "alipay", label: 1, value: "支付宝" },
                { img: "cards", label: 2, value: "钱包" }
            ]
        };
    },
    methods: {
        payWay: function(index) {
            this.radio = index;
        }
    }
};
</script>

<style scoped></style>
