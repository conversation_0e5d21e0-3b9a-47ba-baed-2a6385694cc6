<template>
    <div>
        =<el-row type="flex">
            <el-col :span="2" />
            <el-col :span="20">
                <el-table
                    ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    class="picc-table picc-table-card-mode"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="日期" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.date }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="姓名" width="120" />
                    <el-table-column prop="address" label="地址" show-overflow-tooltip />
                </el-table>
            </el-col>
            <el-col :span="2" />
        </el-row>
        <div style="margin-bottom:30px;"></div>
        <el-row type="flex">
            <el-col :span="2" />
            <el-col :span="20">
                <el-table
                    ref="multipleTable1"
                    border
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    class="picc-table picc-table-card-mode"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="日期" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.date }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="姓名" width="120" />
                    <el-table-column prop="address" label="地址" show-overflow-tooltip />
                </el-table>
            </el-col>
            <el-col :span="2" />
        </el-row>
        <div style="margin-bottom:30px;"></div>
        <el-row type="flex">
            <el-col :span="2" />
            <el-col :span="20">
                <el-table
                    ref="multipleTable2"
                    border
                    :data="tableData0000"
                    tooltip-effect="dark"
                    style="width: 100%"
                    class="picc-table picc-table-card-mode"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="日期" width="120">
                        <template slot-scope="scope">
                            {{ scope.row.date }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="姓名" width="120" />
                    <el-table-column prop="address" label="地址" show-overflow-tooltip>
                        /
                    </el-table-column>
                </el-table>
            </el-col>
            <el-col :span="2" />
        </el-row>
        <div style="margin-bottom:30px;"></div>
    </div>
</template>

<script>
export default {
    name: "Tablebox",
    data() {
        return {
            forms: {
                id: 1,
                documentNo: null,
                buyerName: "琦迹技术",
                buyerDp: "42118XXXXXXXXXX72X",
                buyerBankAccount: "招商银行4890284",
                buyerAddressPhone: "深圳市宝安区110112",
                buyerEmail: null,
                buyerPhone: null,
                buyerType: "01",
                remarks: "这是备注",
                total: 350.9,
                voList: [
                    {
                        giftType: "上海",
                        childGiftTypeName: "上海市普陀区金沙江路", // 是否有效
                        giftStatus: "1518", // 产品名臣
                        merchantName: "王小虎",
                        orgName: "总公司",
                        created: "上海",
                        priorityCity: "1",
                        expireDate: "1",
                        createdBy: "123"
                    },
                    {
                        giftType: "上海",
                        childGiftTypeName: "上海市普陀区金沙江路", // 是否有效
                        giftStatus: "1518", // 产品名臣
                        merchantName: "王小虎",
                        orgName: "总公司",
                        created: "上海",
                        priorityCity: "1",
                        expireDate: "1",
                        createdBy: "123"
                    }
                ],
                checkList: [
                    {
                        checkName: "一级产品类别",
                        checked: true,
                        name: "giftType"
                    },
                    {
                        checkName: "二级产品类别",
                        checked: true,
                        name: "childGiftTypeName"
                    }
                ]
            },
            methods: {
                toggleSelection(rows) {
                    if (rows) {
                        rows.forEach((row) => {
                            this.$refs.multipleTable.toggleRowSelection(row);
                        });
                    } else {
                        this.$refs.multipleTable.clearSelection();
                    }
                },
                handleSelectionChange(val) {
                    this.multipleSelection = val;
                },
                save() {
                    this.$refs.forms.validate((valid) => {
                        // if (valid) {
                        //     console.log(1);
                        // }
                    });
                },
                focusFN: function(target) {
                    this.$refs.forms.clearValidate(target);
                },
                indexMethod(index) {
                    return index * 2;
                }
            }
        };
    }
};
</script>
<style lang="scss" scoped>
tr.el-table__row {
    ::v-deep &:not(.hover-row) {
        &:hover {
            td {
                background: yellow !important;
            }
        }
    }
    ::v-deep &.hover-row td {
        background-color: yellow !important;
    }
}
</style>
