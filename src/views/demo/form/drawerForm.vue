<template>
    <div style="padding:30px;">
        <el-radio-group v-model="direction">
            <el-radio label="ltr">
                从左往右开
            </el-radio>
            <el-radio label="rtl">
                从右往左开
            </el-radio>
            <el-radio label="ttb">
                从上往下开
            </el-radio>
            <el-radio label="btt">
                从下往上开
            </el-radio>
        </el-radio-group>

        <el-button
            type="primary"
            style="margin-left: 16px;"
            @click="drawer = true"
        >
            点我打开
        </el-button>

        <el-drawer
            ref="drawer"
            title="我是标题"
            :custom-class="'drawerClass'"
            :visible.sync="drawer"
            :direction="direction"
            :destroy-on-close="true"
            size="700px"
            :before-close="handleClose"
            @opened="changeZIndex"
        >
            <div class="picc-el-form">
                <el-form
                    ref="dialogBox"
                    :model="formBox"
                    label-width="120px"
                    :rules="rules"
                >
                    <el-row type="flex">
                        <el-col :span="12">
                            <el-form-item
                                v-picc-input-error
                                prop="iconBtnInputBox1"
                                label="带图标按钮输入框 - 新版"
                                class="picc-form-label-linefeed picc-linefeed--medium"
                            >
                                <el-popover
                                    v-model="formBox.visible1"
                                    placement="bottom-start"
                                    width="550"
                                    :visible-arrow="false"
                                    trigger="manual"
                                >
                                    <el-table :data="gridData">
                                        <el-table-column
                                            width="150"
                                            property="date"
                                            label="日期"
                                        />
                                        <el-table-column
                                            width="100"
                                            property="name"
                                            label="姓名"
                                        />
                                        <el-table-column
                                            width="300"
                                            property="address"
                                            label="地址"
                                        />
                                    </el-table>
                                    <div style="text-align: right; margin: 0">
                                        <el-button
                                            @click="formBox.visible1 = false"
                                        >
                                            取 消
                                        </el-button>
                                        <el-button
                                            type="primary"
                                            @click="formBox.visible1 = false"
                                        >
                                            确 定
                                        </el-button>
                                    </div>
                                    <el-input
                                        slot="reference"
                                        v-model="formBox.iconBtnInputBox1"
                                        :clearable="true"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                        @focus="inputFocus('iconBtnInputBox1')"
                                    >
                                        <i
                                            slot="suffix"
                                            class="el-input__icon el-icon-search"
                                            @click="
                                                formBox.visible1 = true;
                                                formBox.iconBtnInputBox1 =
                                                    '带图标按钮输入框';
                                            "
                                        >
                                        </i>
                                    </el-input>
                                </el-popover>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item
                                label="带图标按钮输入框 - 新版"
                                class="picc-form-label-linefeed picc-linefeed--medium"
                            >
                                <el-popover
                                    v-model="formBox.visible2"
                                    placement="bottom-start"
                                    width="550"
                                    :visible-arrow="false"
                                    trigger="manual"
                                >
                                    <el-table :data="gridData">
                                        <el-table-column
                                            width="150"
                                            property="date"
                                            label="日期"
                                        />
                                        <el-table-column
                                            width="100"
                                            property="name"
                                            label="姓名"
                                        />
                                        <el-table-column
                                            width="300"
                                            property="address"
                                            label="地址"
                                        />
                                    </el-table>
                                    <div style="text-align: right; margin: 0">
                                        <el-button
                                            @click="formBox.visible2 = false"
                                        >
                                            取 消
                                        </el-button>
                                        <el-button
                                            type="primary"
                                            @click="formBox.visible2 = false"
                                        >
                                            确 定
                                        </el-button>
                                    </div>
                                    <el-input
                                        slot="reference"
                                        v-model="formBox.iconBtnInputBox2"
                                        placeholder="点击选择关系人"
                                        class="picc-icon-btn-input"
                                    >
                                        <i
                                            slot="suffix"
                                            class="el-input__icon el-icon-search"
                                            @click="
                                                formBox.visible2 = true;
                                                formBox.iconBtnInputBox2 =
                                                    '带图标按钮输入框';
                                            "
                                        >
                                        </i>
                                    </el-input>
                                </el-popover>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row type="flex">
                        <el-col :span="12">
                            <el-form-item
                                label="下拉选择器-仅操作型"
                                class="picc-form-label-linefeed picc-linefeed--medium"
                            >
                                <el-select
                                    v-model="selectOper.optionsValue"
                                    placeholder="请选择"
                                >
                                    <el-option-group
                                        v-for="group in selectOper.options"
                                        :key="group.label"
                                        :label="group.label"
                                    >
                                        <el-option
                                            v-for="item in group.options"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-option-group>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item
                                v-picc-input-error
                                prop="optionsValue1"
                                label="下拉选择器-仅操作型"
                                class="picc-form-label-linefeed picc-linefeed--medium"
                            >
                                <el-select
                                    v-model="selectOper.optionsValue1"
                                    placeholder="请选择"
                                    @focus="inputFocus('optionsValue1')"
                                    @blur="inputBlur('optionsValue1')"
                                >
                                    <el-option-group
                                        v-for="group in selectOper.options"
                                        :key="group.label"
                                        :label="group.label"
                                    >
                                        <el-option
                                            v-for="item in group.options"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-option-group>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-button @click="innerDrawer = true">
                打开里面的!
            </el-button>
            <el-drawer
                title="我是里面的"
                :append-to-body="true"
                :custom-class="'drawerClass1'"
                :destroy-on-close="true"
                :visible.sync="innerDrawer"
                @opened="changeZIndex1"
                @closed="changeZIndexClose"
            >
                <div class="picc-el-form">
                    <el-form
                        ref="dialogBox"
                        :model="formBox"
                        label-width="120px"
                        :rules="rules"
                    >
                        <el-row type="flex">
                            <el-col :span="12">
                                <el-form-item
                                    v-picc-input-error
                                    prop="iconBtnInputBox1"
                                    label="带图标按钮输入框 - 新版"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-popover
                                        v-model="formBox.visible1"
                                        placement="bottom-start"
                                        width="550"
                                        :visible-arrow="false"
                                        trigger="manual"
                                    >
                                        <el-table :data="gridData">
                                            <el-table-column
                                                width="150"
                                                property="date"
                                                label="日期"
                                            />
                                            <el-table-column
                                                width="100"
                                                property="name"
                                                label="姓名"
                                            />
                                            <el-table-column
                                                width="300"
                                                property="address"
                                                label="地址"
                                            />
                                        </el-table>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                @click="
                                                    formBox.visible1 = false
                                                "
                                            >
                                                取 消
                                            </el-button>
                                            <el-button
                                                type="primary"
                                                @click="
                                                    formBox.visible1 = false
                                                "
                                            >
                                                确 定
                                            </el-button>
                                        </div>
                                        <el-input
                                            slot="reference"
                                            v-model="formBox.iconBtnInputBox1"
                                            :clearable="true"
                                            placeholder="点击选择关系人"
                                            class="picc-icon-btn-input"
                                            @focus="
                                                inputFocus('iconBtnInputBox1')
                                            "
                                        >
                                            <i
                                                slot="suffix"
                                                class="el-input__icon el-icon-search"
                                                @click="
                                                    formBox.visible1 = true;
                                                    formBox.iconBtnInputBox1 =
                                                        '带图标按钮输入框';
                                                "
                                            >
                                            </i>
                                        </el-input>
                                    </el-popover>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    label="带图标按钮输入框 - 新版"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-popover
                                        v-model="formBox.visible2"
                                        placement="bottom-start"
                                        width="550"
                                        :visible-arrow="false"
                                        trigger="manual"
                                    >
                                        <el-table :data="gridData">
                                            <el-table-column
                                                width="150"
                                                property="date"
                                                label="日期"
                                            />
                                            <el-table-column
                                                width="100"
                                                property="name"
                                                label="姓名"
                                            />
                                            <el-table-column
                                                width="300"
                                                property="address"
                                                label="地址"
                                            />
                                        </el-table>
                                        <div
                                            style="text-align: right; margin: 0"
                                        >
                                            <el-button
                                                @click="
                                                    formBox.visible2 = false
                                                "
                                            >
                                                取 消
                                            </el-button>
                                            <el-button
                                                type="primary"
                                                @click="
                                                    formBox.visible2 = false
                                                "
                                            >
                                                确 定
                                            </el-button>
                                        </div>
                                        <el-input
                                            slot="reference"
                                            v-model="formBox.iconBtnInputBox2"
                                            placeholder="点击选择关系人"
                                            class="picc-icon-btn-input"
                                        >
                                            <i
                                                slot="suffix"
                                                class="el-input__icon el-icon-search"
                                                @click="
                                                    formBox.visible2 = true;
                                                    formBox.iconBtnInputBox2 =
                                                        '带图标按钮输入框';
                                                "
                                            >
                                            </i>
                                        </el-input>
                                    </el-popover>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row type="flex">
                            <el-col :span="12">
                                <el-form-item
                                    label="下拉选择器-仅操作型"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-select
                                        v-model="selectOper.optionsValue"
                                        placeholder="请选择"
                                    >
                                        <el-option-group
                                            v-for="group in selectOper.options"
                                            :key="group.label"
                                            :label="group.label"
                                        >
                                            <el-option
                                                v-for="item in group.options"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-option-group>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item
                                    v-picc-input-error
                                    prop="optionsValue1"
                                    label="下拉选择器-仅操作型"
                                    class="picc-form-label-linefeed picc-linefeed--medium"
                                >
                                    <el-select
                                        v-model="selectOper.optionsValue1"
                                        placeholder="请选择"
                                        @focus="inputFocus('optionsValue1')"
                                        @blur="inputBlur('optionsValue1')"
                                    >
                                        <el-option-group
                                            v-for="group in selectOper.options"
                                            :key="group.label"
                                            :label="group.label"
                                        >
                                            <el-option
                                                v-for="item in group.options"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-option-group>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <p>_(:зゝ∠)_</p>
            </el-drawer>
        </el-drawer>
    </div>
</template>

<script>
export default {
    name: "",
    data() {
        return {
            innerDrawer: false,
            drawer: false,
            direction: "rtl",

            formBox: {
                visible: false,
                iconBtnInputBox: "",
                visible1: false,
                iconBtnInputBox1: "",
                visible2: false,
                iconBtnInputBox2: "",
                visible3: false,
                iconBtnInputBox3: "",
                visible4: false,
                iconBtnInputBox4: "",

                visibleBtn: false,
                btnInputBox: "",
                visibleBtn1: false,
                btnInputBox1: "",
                visibleBtn2: false,
                btnInputBox2: "",

                iconBtnInput: "",
                iconBtnInput1: "",
                iconBtnInputnew: "",
                iconBtnInputnew1: "",
                btnInput: "",
                btnInput1: ""
            },
            rules: {
                iconBtnInputBox: [
                    {
                        required: true,
                        message:
                            "点击选择产品方案点击选择产品方案点击选择产品方案点击选择产品方案",
                        trigger: "blur"
                    }
                ],
                iconBtnInputBox1: [
                    {
                        required: true,
                        message:
                            "点击选择产品方案点击选择产品方案点击选择产品方案点击选择产品方案点击选择产品方案",
                        trigger: "blur"
                    }
                ],
                btnInputBox: [
                    {
                        required: true,
                        message: `气泡 - 错误提示的文案错误提示的文案错误提示的文案错误提示的文案误提示的文案错误提示的文案错误提示的文案错误提示的文案错误提示的文案...`,
                        trigger: "blur"
                    },
                    {
                        min: 5,
                        message: `气泡 - 错误提示的文案需要大于5个字`,
                        trigger: "blur"
                    },
                    {
                        max: 8,
                        message: `气泡 - 错误提示的文案需要小于8个字`,
                        trigger: "blur"
                    }
                ],
                iconBtnInputBox4: [
                    {
                        required: true,
                        message:
                            "点击选择产品方案点击选择产品方案点击选择产品方案点击选择产品方案点击选择产品方案",
                        trigger: "blur"
                    }
                ],
                btnInputBox3: [
                    {
                        required: true,
                        message: `气泡 - 错误提示的文案错误提示的文案错误提示的文案错误提示的文案误提示的文案错误提示的文案错误提示的文案错误提示的文案错误提示的文案...`,
                        trigger: "blur"
                    },
                    {
                        min: 5,
                        message: `气泡 - 错误提示的文案需要大于5个字`,
                        trigger: "blur"
                    },
                    {
                        max: 8,
                        message: `气泡 - 错误提示的文案需要小于8个字`,
                        trigger: "blur"
                    }
                ],
                optionsValue1: [
                    {
                        required: true,
                        message:
                            "请选择活动区域请选择活动区域请选择活动区域请选择活动区域",
                        trigger: "change"
                    }
                ],
                insuranceShow1: [
                    {
                        required: true,
                        message:
                            "请选择活动区域请选择活动区域请选择活动区域请选择活动区域",
                        trigger: "change"
                    },
                    {
                        min: 8,
                        message: `气泡9 - 错误提示的文案需要大于8个字`,
                        trigger: "change"
                    },
                    {
                        max: 13,
                        message: `气泡9 - 错误提示的文案需要小于13个字`,
                        trigger: "change"
                    }
                ],
                fuzzy1: [
                    {
                        required: true,
                        message:
                            "请选择请选择请选择请选择请选择请选择请选择请选择",
                        trigger: "change"
                    }
                ]
            },
            /* 下拉选择器 - 仅操作型 */
            selectOper: {
                insuranceShow: "",
                insuranceShow1: "",
                insuranceShow2: "",
                insurance: [
                    {
                        num: "01",
                        value: "普通家庭财产保险1"
                    },
                    {
                        num: "02",
                        value: "普通家庭财产保险2"
                    },
                    {
                        num: "03",
                        value: "普通家庭财产保险3"
                    },
                    {
                        num: "04",
                        value: "普通家庭财产保险财产保险4"
                    },
                    {
                        num: "05",
                        value: "普通家庭财产保险财产保险5"
                    },
                    {
                        num: "06",
                        value: "普通家庭财产保险财产保险6"
                    },
                    {
                        num: "07",
                        value:
                            "普通家庭财产保险财产保险普通家庭财产保险财产保险7"
                    },
                    {
                        num: "08",
                        value:
                            "普通家庭财产保险财产保险普通家庭财产保险财产保险8"
                    },
                    {
                        num: "09",
                        value:
                            "普通家庭财产保险财产保险普通家庭财产保险财产保险9"
                    }
                ],
                options: [
                    {
                        label: "热门城市",
                        options: [
                            {
                                value: "Shanghai",
                                label: "上海"
                            },
                            {
                                value: "Beijing",
                                label: "北京"
                            }
                        ]
                    },
                    {
                        label: "城市名",
                        options: [
                            {
                                value: "Chengdu",
                                label: "成都"
                            },
                            {
                                value: "Shenzhen",
                                label: "深圳"
                            },
                            {
                                value: "Guangzhou",
                                label: "广州"
                            },
                            {
                                value: "Dalian",
                                label: "大连"
                            }
                        ]
                    }
                ],
                optionsValue: "",
                optionsValue1: "",
                optionsValue2: "",

                selectOper: "",
                selectOper1: "",
                selectOper2: "",
                selectOper3: "",
                selectOper4: "",
                selectOper5: "",

                /* 下拉选择器 - 模糊搜索兼下拉选择 */
                fuzzy: "",
                fuzzy1: "",
                fuzzy2: "",
                fuzzyOption: [
                    {
                        value: "选项1",
                        label: "黄金糕"
                    },
                    {
                        value: "选项2",
                        label: "双皮奶"
                    },
                    {
                        value: "选项3",
                        label: "蚵仔煎"
                    },
                    {
                        value: "选项4",
                        label: "龙须面"
                    },
                    {
                        value: "选项5",
                        label: "北京烤鸭"
                    }
                ],

                demo: "",
                popover: false,
                demo1: "",
                popover1: false,
                demo2: "",
                popover2: false,
                demo3: "",
                popover3: false
            },
            /* 下拉选择器 - 不可用状态 */
            disabledOper: {
                disabledOper: "",
                validStatus: "",
                options: [
                    {
                        value: "选项1",
                        label: "黄金糕"
                    },
                    {
                        value: "选项2",
                        label: "双皮奶"
                    },
                    {
                        value: "选项3",
                        label: "蚵仔煎"
                    },
                    {
                        value: "选项4",
                        label: "龙须面"
                    },
                    {
                        value: "选项5",
                        label: "北京烤鸭"
                    }
                ]
            },
            gridData: [
                {
                    date: "2016-05-02",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-04",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    date: "2016-05-01",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                }
            ]
        };
    },
    methods: {
        changeZIndexClose() {
            const target1 = document.querySelector(".drawerClass1");
            target1.parentNode.parentNode.nextSibling.style.zIndex = "2000";
        },
        changeZIndex1() {
            const target1 = document.querySelector(".drawerClass1");
            target1.parentNode.parentNode.style.zIndex = "2003";
            target1.parentNode.parentNode.nextSibling.style.zIndex = "2002";
        },
        changeZIndex() {
            // const $target = this.$refs.drawer;
            // console.log($target);

            const target1 = document.querySelector(".drawerClass");
            target1.parentNode.parentNode.style.zIndex = "2001";
            target1.parentNode.parentNode.nextSibling.style.zIndex = "2000";
            // document.querySelector('.v-modal').style.zIndex = '2000'
        },
        handleClose(done) {
            this.$confirm("确认关闭？")
                .then((_) => {
                    done();
                    // this.$refs.dialogBox.resetFields()
                    // this.$refs.dialogBox.$destroy();
                })
                .catch((_) => {});
        },

        inputFocus: function(target) {
            // 部分表单校验
            this.$refs.dialogBox.clearValidate(target);
        },
        inputBlur: function(target) {
            // 部分表单校验
            this.$refs.dialogBox.validateField(target);
        },

        /* 按钮输入框 */
        isShow(target) {
            const $target = document.querySelector("." + target + " input");
            $target.style.borderColor = "#234dcc";
            $target.style.color = "#1C61FC";
        },
        isHide(target, data) {
            const $target = document.querySelector("." + target + " input");
            $target.style.borderColor = "#cdcdcd";

            if (this.formBox[data] === "") {
                $target.style.color = "#909199";
            } else {
                $target.style.color = "#292B34";
            }

            // 部分表单校验,否则校验结果有问题
            this.$refs.dialogBox.validateField(data);
        }
    }
};
</script>

<style scoped></style>
