<template>
    <div style="padding:30px;">
        <!--    <el-alert :closable="false" title="form" />-->
        <div class="box">
            <h3>radio 单选框</h3>

            <el-radio-group v-model="radio.radio2">
                <el-radio label="3">
                    选中
                </el-radio>
                <el-radio label="6">
                    未选中
                </el-radio>
                <el-radio label="9">
                    鼠标滑过
                </el-radio>
            </el-radio-group><br /><br />

            <el-radio v-model="radio.radio11" label="1" disabled>
                选中且不可用
            </el-radio>
            <el-radio v-model="radio.radio22" label="2" disabled>
                未选中且不可用
            </el-radio><br /><br />

            <el-radio-group v-model="radio.radio3" text-color="red" fill="pink">
                <el-radio-button label="上海" />
                <el-radio-button label="北京" />
                <el-radio-button label="广州" />
                <el-radio-button label="深圳" />
            </el-radio-group><br /><br />

            <el-radio-group v-model="radio.radio4" size="small" text-color="red" fill="green">
                <el-radio label="1" border>
                    选中
                </el-radio>
                <el-radio label="2" border>
                    未选中
                </el-radio>
                <el-radio label="3" border disabled>
                    未选中且不可用
                </el-radio>
            </el-radio-group><br /><br />
        </div>
        <div class="box">
            <h3>checkbox 复选框</h3>

            <!-- Checkbox Attributes
           label: 选中状态的值（只有在checkbox-group或者绑定对象类型为array时有效） No
           true-label,false-label规定v-modle的值
                如 true-label="a",false-label="b" v-modle="data" 当选中时data为a，当没选中时data为b
           indeterminate：设置 indeterminate 状态，只负责样式控制，默认false
      -->
            <el-checkbox v-model="checkbox.check3" label="选中" />
            <el-checkbox v-model="checkbox.check4">
                未选中
            </el-checkbox><!-- :true-label="a" :false-label="b"-->
            <el-checkbox v-model="checkbox.check">
                鼠标滑过
            </el-checkbox><br /><br />

            <el-checkbox v-model="checkbox.check1" label="选中且不可用" disabled />
            <el-checkbox v-model="checkbox.check2" label="未选中且不可用" disabled /><br /><br />
        </div>
        <div class="box">
            <h3>input 输入框</h3>

            <el-input v-model="input.input" placeholder="请输入内容" autocomplete="on" autofocus /><br /><br />
            <el-input v-model="input.input4" placeholder="请输入内容" show-password /><br /><br />

            <el-input v-model="input.input2" placeholder="请输入内容" :disabled="true" size="small" /><br /><br />
            <el-input v-model="input.input3" placeholder="请输入内容" disabled /><br /><br />

            <el-input v-model="input.textarea" placeholder="多行文本域" type="textarea" :rows="2" /><br /><br />
            <el-input v-model="input.textarea1" placeholder="多行文本域 无size:small" type="textarea" :rows="2" /><br /><br />

            <el-input v-model="input.textarea2" placeholder="可自适应文本高度的文本域" type="textarea" autosize /><br /><br />
            <el-input v-model="input.textarea3" placeholder="可自适应文本高度的文本域" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" /><br /><br /><br />

            <!--      <div class="demo-input-suffix">-->
            <!--        带有图标标记输入类型 - 属性方式：-->
            <!--        <el-input v-model="input.input5" placeholder="请选择日期" suffix-icon="el-icon-date" /><br><br>-->
            <!--        <el-input v-model="input.input6" placeholder="请输入内容" prefix-icon="el-icon-search" />-->
            <!--      </div>-->
            <!--      <div style="margin: 30px 0;" />-->
            <!--      <div class="demo-input-suffix">-->
            <!--        带有图标标记输入类型 - slot 方式：-->
            <!--        <el-input v-model="input.input7" placeholder="请选择日期">-->
            <!--          <i slot="suffix" class="el-input__icon el-icon-date" />-->
            <!--        </el-input><br><br>-->
            <!--        <el-input v-model="input.input8" placeholder="请输入内容">-->
            <!--          <i slot="prefix" class="el-input__icon el-icon-search" />-->
            <!--        </el-input>-->
            <!--      </div><br><br>-->

            <!--      复合型输入框: 可前置或后置元素，一般为标签或按钮<br>-->
            <!--      <div style="margin-top: 15px;">-->
            <!--        <el-input v-model="input.input9" placeholder="请输入内容">-->
            <!--          <template slot="prepend">Http://</template>-->
            <!--        </el-input><br><br>-->
            <!--        <el-input v-model="input.input10" placeholder="请输入内容">-->
            <!--          <template slot="append">.com</template>-->
            <!--        </el-input><br><br>-->
            <!--        <el-input v-model="input.input11" placeholder="请输入内容" class="input-with-select">-->
            <!--          <el-select slot="prepend" v-model="select" placeholder="请选择">-->
            <!--            <el-option label="餐厅名" value="1" />-->
            <!--            <el-option label="订单号" value="2" />-->
            <!--            <el-option label="用户电话" value="3" />-->
            <!--          </el-select>-->
            <!--          <el-button slot="append" icon="el-icon-search" />-->
            <!--        </el-input>-->
            <!--      </div><br>-->
        </div>
        <div class="box">
            <h3>按钮输入框</h3>
            <el-form :model="btnInput" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item prop="btn" label="产品方案">
                    <el-input v-model="btnInput.btn" class="el-input-style" placeholder="点击选择产品方案" readonly @click.native="dialogFormVisible = true" />
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>带图标输入框</h3>
            <el-form :model="iconInput" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item prop="icon" label="选择关系人">
                    <el-input id="iconId" v-model="iconInput.icon" class="el-input-style" placeholder="点击选择关系人" readonly suffix-icon="el-icon-search" @click.native="iconClick('iconId')" />
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>下拉选择器：仅操作型</h3>

            <el-form :model="selectOper" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item label="财险" prop="validStatus">
                    <el-select v-model="selectOper.insuranceShow" placeholder="请选择">
                        <el-option
                            v-for="(item,index) in selectOper.insurance"
                            :key="index"
                            :label="`${item.num}-${item.value}`"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item><br />
                注：option选项被选中时，需要将input输入框的焦点去掉<br /><br />

                <!--        <el-form-item label="财险分组" prop="validStatus">-->
                <!--          <el-select v-model="selectOper.insuranceShow1" placeholder="请选择">-->
                <!--            <el-option-group-->
                <!--              v-for="group in selectOper.insurance1"-->
                <!--              :key="group.label"-->
                <!--              :label="group.label">-->
                <!--              <el-option-->
                <!--                v-for="item in group.options"-->
                <!--                :key="item.value"-->
                <!--                :label="item.label"-->
                <!--                :value="item.value">-->
                <!--              </el-option>-->
                <!--            </el-option-group>-->
                <!--          </el-select>-->
                <!--        </el-form-item><br>-->
                <input id="hidden" type="hidden" onclick="" value="" />
                <el-form-item label="测试" prop="validStatus">
                    <el-select v-model="selectOper.validStatus" placeholder="请选择" @blur="selectOperBlur">
                        <el-option
                            v-for="(item,index) in selectOper.cities"
                            :key="index"
                            :label="`${index} - ${item.label}`"
                            :value="index"
                        >
                            <span style="float: left">{{ item.label }}</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>下拉选择器：模糊搜索兼下拉选择</h3>

            <el-form :model="fuzzySearch" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item label="模糊搜索" prop="fuzzy">
                    <el-select v-model="fuzzySearch.fuzzy" filterable placeholder="可直接输入名称">
                        <el-option
                            v-for="item in fuzzySearch.options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>下拉选择器：不可用状态</h3>

            <el-form :model="disabledOper" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item label="不可用状态" prop="validStatus">
                    <el-select v-model="disabledOper.validStatus" v-popover:popover placeholder="请选择" disabled>
                        <el-option
                            v-for="item in disabledOper.options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>下拉选择器：联级选择</h3>

            <el-form :model="cascadeSelection " label-width="120px" :rules="rules" :inline="true">
                <el-form-item label="联级选择">
                    <el-cascader
                        v-model="cascadeSelection.selectedOptions"
                        :options="cascadeSelection.options"
                    />
                </el-form-item><br />
                <el-form-item label="联级disabled">
                    <el-cascader
                        v-model="cascadeSelection.selectedOptions1"
                        disabled
                        :options="cascadeSelection.options"
                    />
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>数字输入框：带进步器</h3>
            <!-- <template> -->
            <el-input-number v-model="inputNumber.num2" :min="1" :max="10" label="描述文字" />
            <!-- </template> -->
            <!-- <template> -->
            <el-input-number v-model="inputNumber.num2" :disabled="true" />
            <!-- </template> -->
            <!-- <template> -->
            <el-input-number v-model="inputNumber.num2" size="small" :min="1" :max="10" />
            <!-- </template> -->
            <!-- <template> -->
            <el-input-number v-model="inputNumber.num2" :disabled="true" :min="1" :max="10" size="small" />
            <!-- </template> -->
            <el-form :model="inputNumber" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item label="计数器">
                    <el-input-number v-model="inputNumber.num1" :min="1" :max="10" label="描述文字" />
                </el-form-item><br />
                <el-form-item label="计数器disabled">
                    <el-input-number v-model="inputNumber.num1" :min="1" :max="10" label="描述文字" :disabled="true" />
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>选择器：日期选择器</h3>
            <el-form :model="datePicker" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item label="日期选择器">
                    <el-date-picker
                        v-model="datePicker.datePicker1"
                        type="date"
                        format="yyyy/MM/dd"
                        placeholder="选择日期"
                    />
                </el-form-item>
            </el-form>
        </div>
        <div class="box">
            <h3>选择器：时间选择器</h3>
            <el-form :model="timePicker" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item label="时间选择器">
                    <!--format="yyyy/MM/dd HH时mm分"-->
                    <el-date-picker
                        v-model="timePicker.timePicker2"
                        type="datetime"
                        placeholder="选择日期时间"
                    />
                </el-form-item>
                <span style="color: red">{{ timePicker.timePicker2 | dateFormat }}</span>
            </el-form>
        </div>
        <div class="box">
            <h3>选择器：日期范围选择器</h3>
            <el-date-picker
                v-model="timePicker.timePicker3"
                disabled="disabled"
                type="daterange"
                format="yyyy/MM/dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                placeholder="选择日期"
            />
            <el-form :model="timePicker" label-width="120px" :rules="rules" :inline="true" class="demo-form-inline" size="small">
                <el-form-item label="日期范围">
                    <el-date-picker
                        v-model="timePicker.timePicker3"
                        type="daterange"
                        format="yyyy/MM/dd"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        placeholder="选择日期"
                    />
                </el-form-item><br />
                <el-form-item label="日期范围">
                    <el-date-picker
                        v-model="timePicker.timePicker3"
                        disabled="disabled"
                        format="yyyy/MM/dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        placeholder="选择日期"
                    />
                </el-form-item>
            </el-form>
        </div>

        <div class="box">
            <h3>form 表单 size:small</h3>
            <!--class="demo-form-inline" -->
            <el-form :model="dialog1" label-width="120px" :rules="rules" :inline="true" size="small">
                <el-form-item prop="type" label="类型代码">
                    <el-input v-model="dialog1.type" class="el-input-style" placeholder="输入文本信息" />
                </el-form-item>
                <el-form-item prop="type1" label="标题">
                    <el-input id="hideError" v-model="dialog1.type1" class="el-input-style" @focus="inputFocus('hideError')" @blur="inputBlur('hideError')" />
                </el-form-item>
                <el-form-item prop="type2" label="类型代码">
                    <el-input v-model="dialog1.type2" class="el-input-style" placeholder="输入文本信息" />
                </el-form-item>
                <el-form-item label="标题">
                    <el-input v-model="dialog1.type1" class="el-input-style" />
                </el-form-item>
                <el-form-item label="标题过长可以折行处理" class="picc_long_label">
                    <el-input v-model="dialog1.type3" class="el-input-style" placeholder="未实现" />
                </el-form-item>
                <el-form-item prop="typeDesc" label="未输入不可用">
                    <el-input v-model="dialog1.typeDesc" v-popover:popover2 class="el-input-style" disabled />
                </el-form-item>
                <el-form-item prop="type3" label="标题过长可以折行处理" class="picc_long_label">
                    <el-input v-model="dialog1.type3" class="el-input-style" placeholder="未实现" />
                </el-form-item>
                <el-form-item prop="typeDesc" label="已输入不可用">
                    <el-tooltip class="item" effect="dark" content="禁用提示文字" placement="top-start">
                        <el-input v-model="dialog1.typeDescs" class="el-input-style" placeholder="请输入..." disabled />
                    </el-tooltip>
                </el-form-item>
                <el-form-item label="关系人类型" prop="resource">
                    <el-radio-group v-model="dialog1.resource">
                        <el-radio label="是" />
                        <el-radio label="否" />
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="角色" prop="types">
                    <el-checkbox-group v-model="dialog1.types">
                        <el-checkbox label="投保人" name="types" />
                        <el-checkbox label="被保险人" name="types" />
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="效力状态" prop="validStatus">
                    <el-select v-model="dialog1.validStatus">
                        <el-option label="区域一" value="shanghai" />
                        <el-option label="区域二" value="beijing" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="type1" label="投保份数">
                    <el-input-number v-model="dialog1.num1" :min="1" :max="10" label="描述文字" />
                </el-form-item>
                <el-form-item label="活动时间">
                    <el-date-picker v-model="dialog1.date1" type="date" placeholder="选择日期" />
                </el-form-item>
                <el-form-item label="活动时间" prop="time1">
                    <el-date-picker v-model="dialog1.time1" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm" />
                </el-form-item>
                <el-form-item label="类型代码">
                    <el-input
                        v-model="dialog1.textarea2"
                        type="textarea"
                        autosize
                        placeholder="请输入内容"
                    />
                </el-form-item>
            </el-form>
        </div><br /><br />

        <div class="box">
            <el-form :model="dialog1" label-width="120px" :rules="rules">
                <!-- :inline="true" class="demo-form-inline"-->
                <el-row type="flex" class="row-bg">
                    <el-col :span="8">
                        <el-form-item prop="type" label="类型代码">
                            <el-input v-model="dialog1.type" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item prop="type" label="类型代码">
                            <el-input v-model="dialog1.type" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item prop="type" label="类型代码">
                            <el-input v-model="dialog1.type" class="el-input-style" placeholder="输入文本信息" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div><br /><br /><br /><br /><br /><br />

        <el-dialog title="选择产品方案" :visible.sync="dialogFormVisible">
            <el-table :data="gridData">
                <el-table-column property="date" label="日期" width="150" />
                <el-table-column property="name" label="姓名" width="200" />
                <el-table-column property="address" label="地址" />
            </el-table>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">
                    取 消
                </el-button>
                <el-button type="primary" @click="dialogFormVisible = false">
                    确 定
                </el-button>
            </div>
        </el-dialog>
        <!--气泡-->
        <el-popover
            ref="popover2"
            placement="top"
            width="200"
            trigger="click"
            popper-class="picc-popover-custom"
        >
            <!--content="禁用提示信息"-->
            <span><i class="el-icon-info"></i> 禁用提示信息</span>
        </el-popover>
        <el-popover
            ref="popover"
            placement="top"
            width="200"
            trigger="click"
            popper-class="picc-popover-custom"
        >
            <!--content="禁用提示信息"-->
            <span><i class="el-icon-info"></i> 禁用提示信息</span>
        </el-popover>
    </div>
</template>
<script>
export default {
    name: 'Demo',
    filters: {
        dateFormat(dataStr) {
            const time = new Date(dataStr);
            function timeAdd0(str) {
                if (str < 10) {
                    str = '0' + str;
                }
                return str;
            }
            const y = time.getFullYear();
            const m = time.getMonth() + 1;
            const d = time.getDate();
            const h = time.getHours();
            const mm = time.getMinutes();
            return y + '/' + timeAdd0(m) + '/' + timeAdd0(d) + ' ' + timeAdd0(h) + '时' + timeAdd0(mm) + '分';
        }
    },
    data() {
        return {
            radio: {
                radio11: '1',
                radio22: '',
                radio2: '3',
                radio3: '上海',
                radio4: '1'
            },
            checkbox: {
                check: false,
                check1: true,
                check2: false,
                check3: true,

                a: '选中状态',
                b: '未选中状态',
                check4: '选中状态',

                isIndeterminate: true,
                check5: 'false',
                check6: ['上海', '北京'],
                cities: ['上海', '北京', '广州', '深圳']
            },
            input: {
                input: '',
                input1: '',
                input2: '',
                input3: '',
                input4: '',
                textarea: '',
                textarea1: '',
                textarea2: '',
                textarea3: '',
                input5: '',
                input6: '',
                input7: '',
                input8: '',
                input9: '',
                input10: '',
                input11: '',
                select: ''
            },
            btnInput: {
                btn: ''
            },
            iconInput: {
                icon: '请选择相关查询条件'
            },
            selectOper: {
                validStatus: '',
                cities: [
                    {
                        value: 'Beijing',
                        label: '北京'
                    }, {
                        value: 'Shanghai',
                        label: '上海'
                    }, {
                        value: 'Nanjing',
                        label: '南京'
                    }, {
                        value: 'Chengdu',
                        label: '成都'
                    }, {
                        value: 'Shenzhen',
                        label: '深圳'
                    }, {
                        value: 'Guangzhou',
                        label: '广州'
                    }],
                insuranceShow: '',
                insurance: [
                    {
                        num: '01',
                        value: '普通家庭财产保险1'
                    },
                    {
                        num: '02',
                        value: '普通家庭财产保险财产保险333333333333333333333333333333333333333333332'
                    },
                    {
                        num: '03',
                        value: '普通家庭财产保险3'
                    },
                    {
                        num: '04',
                        value: '普通家庭财产保险财产保险4'
                    },
                    {
                        num: '05',
                        value: '普通家庭财产保险5'
                    },
                    {
                        num: '06',
                        value: '普通家庭财产保险财产保险6'
                    },
                    {
                        num: '07',
                        value: '普通家庭财产保险7'
                    },
                    {
                        num: '08',
                        value: '普通家庭财产保险财产保险8'
                    },
                    {
                        num: '09',
                        value: '普通家庭财产保险9'
                    },
                    {
                        num: '10',
                        value: '普通家庭财产保险财产保险10'
                    },
                    {
                        num: 'cursordisabled.jpg',
                        value: '普通家庭财产保险11'
                    },
                    {
                        num: '12',
                        value: '普通家庭财产保险财产保险12'
                    }
                ],
                insuranceShow1: '',
                insurance1: [
                    {
                        label: '热门城市',
                        options: [
                            {
                                value: 'Shanghai',
                                label: '上海'
                            }, {
                                value: 'Beijing',
                                label: '北京'
                            }]
                    }, {
                        label: '城市名',
                        options: [
                            {
                                value: 'Chengdu',
                                label: '成都'
                            }, {
                                value: 'Shenzhen',
                                label: '深圳'
                            }, {
                                value: 'Guangzhou',
                                label: '广州'
                            }, {
                                value: 'Dalian',
                                label: '大连'
                            }]
                    }]
            },
            fuzzySearch: {
                fuzzy: '',
                options: [
                    {
                        value: '选项1',
                        label: '黄金糕'
                    }, {
                        value: '选项2',
                        label: '双皮奶'
                    }, {
                        value: '选项3',
                        label: '蚵仔煎'
                    }, {
                        value: '选项4',
                        label: '龙须面'
                    }, {
                        value: '选项5',
                        label: '北京烤鸭'
                    }]
            },
            disabledOper: {
                validStatus: '',
                options: [
                    {
                        value: '选项1',
                        label: '黄金糕'
                    }, {
                        value: '选项2',
                        label: '双皮奶'
                    }, {
                        value: '选项3',
                        label: '蚵仔煎'
                    }, {
                        value: '选项4',
                        label: '龙须面'
                    }, {
                        value: '选项5',
                        label: '北京烤鸭'
                    }]
            },
            cascadeSelection: {
                options: [{
                    value: 'zhinan',
                    label: '指南',
                    children: [{
                        value: 'shejiyuanze',
                        label: '设计原则',
                        children: [{
                            value: 'yizhi',
                            label: '一致'
                        }, {
                            value: 'fankui',
                            label: '反馈'
                        }, {
                            value: 'xiaolv',
                            label: '效率'
                        }, {
                            value: 'kekong',
                            label: '可控'
                        }]
                    }, {
                        value: 'daohang',
                        label: '导航',
                        children: [{
                            value: 'cexiangdaohang',
                            label: '侧向导航'
                        }, {
                            value: 'dingbudaohang',
                            label: '顶部导航'
                        }]
                    }]
                }, {
                    value: 'zujian',
                    label: '组件',
                    children: [{
                        value: 'basic',
                        label: 'Basic',
                        children: [{
                            value: 'layout',
                            label: 'Layout 布局'
                        }, {
                            value: 'color',
                            label: 'Color 色彩'
                        }, {
                            value: 'typography',
                            label: 'Typography 字体'
                        }, {
                            value: 'icon',
                            label: 'Icon 图标'
                        }, {
                            value: 'button',
                            label: 'Button 按钮'
                        }]
                    }, {
                        value: 'form',
                        label: 'Form',
                        children: [{
                            value: 'radio',
                            label: 'Radio 单选框'
                        }, {
                            value: 'checkbox',
                            label: 'Checkbox 多选框'
                        }, {
                            value: 'input',
                            label: 'Input 输入框'
                        }, {
                            value: 'input-number',
                            label: 'InputNumber 计数器'
                        }, {
                            value: 'select',
                            label: 'Select 选择器'
                        }, {
                            value: 'cascader',
                            label: 'Cascader 级联选择器'
                        }, {
                            value: 'switch',
                            label: 'Switch 开关'
                        }, {
                            value: 'slider',
                            label: 'Slider 滑块'
                        }, {
                            value: 'time-picker',
                            label: 'TimePicker 时间选择器'
                        }, {
                            value: 'date-picker',
                            label: 'DatePicker 日期选择器'
                        }, {
                            value: 'datetime-picker',
                            label: 'DateTimePicker 日期时间选择器'
                        }, {
                            value: 'uploadIDcard.png',
                            label: 'Upload 上传'
                        }, {
                            value: 'rate',
                            label: 'Rate 评分'
                        }, {
                            value: 'form',
                            label: 'Form 表单'
                        }]
                    }, {
                        value: 'data',
                        label: 'Data',
                        children: [{
                            value: 'table',
                            label: 'Table 表格'
                        }, {
                            value: 'tag',
                            label: 'Tag 标签'
                        }, {
                            value: 'progress',
                            label: 'Progress 进度条'
                        }, {
                            value: 'tree',
                            label: 'Tree 树形控件'
                        }, {
                            value: 'pagination',
                            label: 'Pagination 分页'
                        }, {
                            value: 'badge',
                            label: 'Badge 标记'
                        }]
                    }, {
                        value: 'notice',
                        label: 'Notice',
                        children: [{
                            value: 'alert',
                            label: 'Alert 警告'
                        }, {
                            value: 'loading',
                            label: 'Loading 加载'
                        }, {
                            value: 'message',
                            label: 'Message 消息提示'
                        }, {
                            value: 'message-box',
                            label: 'MessageBox 弹框'
                        }, {
                            value: 'notification',
                            label: 'Notification 通知'
                        }]
                    }, {
                        value: 'navigation',
                        label: 'Navigation',
                        children: [{
                            value: 'menu',
                            label: 'NavMenu 导航菜单'
                        }, {
                            value: 'tabs',
                            label: 'Tabs 标签页'
                        }, {
                            value: 'breadcrumb',
                            label: 'Breadcrumb 面包屑'
                        }, {
                            value: 'dropdown',
                            label: 'Dropdown 下拉菜单'
                        }, {
                            value: 'steps',
                            label: 'Steps 步骤条'
                        }]
                    }, {
                        value: 'others',
                        label: 'Others',
                        children: [{
                            value: 'dialog',
                            label: 'Dialog 对话框'
                        }, {
                            value: 'tooltip',
                            label: 'Tooltip 文字提示'
                        }, {
                            value: 'popover',
                            label: 'Popover 弹出框'
                        }, {
                            value: 'card',
                            label: 'Card 卡片'
                        }, {
                            value: 'carousel',
                            label: 'Carousel 走马灯'
                        }, {
                            value: 'collapse',
                            label: 'Collapse 折叠面板'
                        }]
                    }]
                }, {
                    value: 'ziyuan',
                    label: '资源',
                    children: [{
                        value: 'axure',
                        label: 'Axure Components'
                    }, {
                        value: 'sketch',
                        label: 'Sketch Templates'
                    }, {
                        value: 'jiaohu',
                        label: '组件交互文档'
                    }]
                }],
                selectedOptions: [],
                selectedOptions1: []
            },
            inputNumber: {
                num1: '',
                num2: ''
            },
            datePicker: {
                datePicker1: ''
            },
            timePicker: {
                timePicker1: '',
                timePicker2: '',
                timePicker3: ''
            },

            rules: {
                fuzzy: [{ required: true, message: '请选择', trigger: 'blur' }],
                icon: [{ required: true, message: '请选择相关查询条件', trigger: 'blur' }],
                type: [{ required: true, message: '请输入类型代码请输入类型代十五多余的', trigger: 'blur' }],
                type1: [{ required: true, message: '请输入类型代码请输入类型代十五', trigger: 'blur' }],
                type3: [{ required: true, message: '请输入类型代码', trigger: 'blur' }],
                typeDesc: [{ required: true, message: '请输入类型描述', trigger: 'blur' }],
                validStatus: [{ required: true, message: '请选择效力状态', trigger: 'blur' }],
                typeId: [{ required: true, message: '请输入类型代码ID', trigger: 'blur' }],
                value: [{ required: true, message: '请输入数据描述', trigger: 'blur' }],
                time: [
                    { type: 'date', required: true, message: '请选择日期', trigger: 'change' }
                ],
                time1: [
                    { type: 'date', required: true, message: '请选择日期', trigger: 'change' }
                ],
                resource: [
                    { required: true, message: '请选择活动资源', trigger: 'change' }
                ],
                types: [
                    { type: 'array', required: true, message: '请至少选择一个角色', trigger: 'change' }
                ]

            },
            dialog1: {
                typeDesc11: '输入',
                type: '',
                type1: '',
                type2: '',
                type3: '',
                typeDesc: '',
                radio3: '上海',
                typeDescs: '已输入数据',
                validStatus: '',
                date1: '',
                date2: '',
                date3: '',
                time: '',
                time1: '',
                hh: '',
                mm: '',
                date31: '',
                hh1: '',
                mm1: '',
                num1: '',
                resource: '',
                types: [],
                relationName: ''
            },

            gridData: [
                {
                    date: '2016-05-02',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄'
                }, {
                    date: '2016-05-04',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄'
                }, {
                    date: '2016-05-01',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄'
                }, {
                    date: '2016-05-03',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄'
                }],
            dialogTableVisible: false,
            dialogFormVisible: false,
            form: {
                name: '',
                region: '',
                date1: '',
                date2: '',
                delivery: false,
                type: [],
                resource: '',
                desc: ''
            },
            formLabelWidth: '120px'
        };
    },
    mounted() {
        // console.log('form_mounted');
        this.isErrorHover(); // 文本输入框错误信息 hover

    // 文字换行
    // const oList = document.querySelectorAll('.el-form-item--small .el-form-item__label')
    // const oList = document.querySelectorAll('.el-form-item__label')
    // // alert(oList.length)
    // for (var i = 0; i < oList.length; i++) {
    //   // eslint-disable-next-line no-empty
    //   if (oList[i].innerHTML.length <= 6) {
    //   } else if (oList[i].innerHTML.length > 6 && oList[i].innerHTML.length <= 8) {
    //     oList[i].style.fontSize = '12px'
    //     oList[i].style.lineHeight = '16px'
    //     oList[i].style.paddingLeft = '20px'
    //   } else if (oList[i].innerHTML.length > 8 && oList[i].innerHTML.length <= 16) {
    //     oList[i].style.fontSize = '12px'
    //     oList[i].style.lineHeight = '16px'
    //     oList[i].style.paddingLeft = '20px'
    //   }
    // }
    },
    methods: {
        isErrorHover() {
            this.$nextTick(() => {
                // const _this = this
                const oLi = document.querySelectorAll('.el-input .el-form-item__error');
                // console.log(oLi);
                // eslint-disable-next-line no-empty
                for (let i = 0; i < oLi.length; i++) {}
            });
        },
        selectOperBlur(event) {
            document.getElementById('hidden').click();
            const e = document.createEvent('MouseEvents');
            e.initEvent('click', true, true);
            document.getElementById('hidden').dispatchEvent(e);
        },

        // 带图标输入框 - 图标颜色
        iconClick(target) {
            const _this = this;
            const $parent = document.getElementById(target).parentNode;
            const $target = $parent.getElementsByClassName('el-input__suffix')[0];
            $target.style.color = '#1c61fc';
            _this.dialogFormVisible = true;
        },

        /* input 标签获取/失去焦点 - 错误信息消失/显示 */
        inputFocus(target) {
            const $parent = document.getElementById(target).parentNode.parentNode;
            const $target = $parent.lastChild;
            // eslint-disable-next-line eqeqeq
            if ($target.className == 'el-form-item__error') {
                $target.style.display = 'none';
            }
        },
        inputBlur(target) {
            const $parent = document.getElementById(target).parentNode.parentNode;
            const $target = $parent.lastChild;
            // eslint-disable-next-line eqeqeq
            if ($target.className == 'el-form-item__error') {
                $target.style.display = 'block';
            }
        }
    }
};
</script>
<style scoped>
  /*@import "./font/iconfont.css";*/
  .box{
    border: 1px solid blue;
    margin: 0 0 30px 0;
    padding: 5px 10px;
    width:900px;
  }
</style>
