<template>
    <div class="show-region">
        <div>
            <Card :card-info="cardInfo">
                <el-button type="primary" round class="task-button">
                    分配任务
                </el-button>
            </Card>
        </div>
        <div>
            <Card :card-info="cardInfoTwo">
                <div class="unallot-task picc-card-content-line">
                    <div class="unallot-task-info picc-card-content-label">
                        <span>未分配任务</span>100
                    </div>
                    <el-button round type="primary" size="small">
                        分配
                    </el-button>
                </div>
                <div class="allot-task-unfinished picc-card-content-line">
                    <div class="allot-task-unfinished-info picc-card-content-label">
                        <span>已分配未完成</span>200
                    </div>
                    <el-button type="text">
                        重新分配
                    </el-button>
                </div>
                <div class="allot-task-finished picc-card-content-line">
                    <div class="allot-task-finished-info picc-card-content-label">
                        <span>已完成任务</span>1700
                    </div>
                </div>
            </Card>
        </div>
        <div>
            <Card :card-info="cardInfo">
                <div class="allot-task-unfinished picc-card-content-line">
                    <div class="allot-task-unfinished-info picc-card-content-label">
                        <span>已分配未完成</span>200
                    </div>
                    <el-button type="text">
                        重新分配
                    </el-button>
                </div>
                <div class="allot-task-finished picc-card-content-line">
                    <div class="allot-task-finished-info picc-card-content-label">
                        <span>已完成任务</span>1700
                    </div>
                </div>
            </Card>
        </div>
    </div>
</template>

<script>

export default {
    name: "CardDemo",
    data() {
        return {
            cardInfo: {
                firstLevelTitle: "6月无锡续保活动2",
                secondLevelTitle: "总任务2000",
                number: 90,
                finish: 100,
                newTask: true
            },
            cardInfoTwo: {
                firstLevelTitle: "6月无锡续保活动2超过长度",
                secondLevelTitle: "总任务2000",
                number: 90,
                finish: 30
            }
        };
    }
};
</script>

<style scoped lang="scss">
    .show-region{
        margin-top: 50px;

        >div{
            float: left;
            width: 350px;
        }

        .unallot-task{
            overflow: hidden;
            height: 60px;
            line-height: 60px;

            .unallot-task-info{
                float: left;

                span{
                    display: inline-block;
                    margin-right: 4px;
                }
            }
            .el-button{
                float: right;
                min-width: 60px;
                text-align: center;
                padding: 0px;
                height: 28px;
                margin-top: 16px;
            }
        }
        .allot-task-unfinished{
            overflow: hidden;
            padding-top: 19.5px;
            padding-bottom: 19.5px;
            .allot-task-unfinished-info{
                float: left;
                text-align: left;
                span{
                    margin-right: 8px;
                }
            }
            .el-button{
                float: right;
                font-size: 12px;
            }
        }
        .allot-task-finished{
            overflow: hidden;
            padding-top: 7.5px;
            padding-bottom: 28px;

            .allot-task-finished-info{
                float: left;
                text-align: left;
                span{
                    margin-right: 8px;
                }
            }
        }
    }
</style>
