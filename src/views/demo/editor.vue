<template>
    <div class="show-region">
        <h2>富文本编辑器</h2>
        <editor-item :content="content" :editor-option="editorOption" />
    </div>
</template>

<script>
import EditorItem from '@/components/Editor/index';
export default {
    name: "Editor",
    components: {
        EditorItem
    },
    data() {
        return {
            content: '<h2>I am Example</h2>',
            editorOption: {
                // Some Quill options...
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline', 'strike'], // toggled buttons
                        ['blockquote', 'code-block'],

                        ['clean']
                    ]
                }
            }
        };
    },
    methods: {
        onEditorBlur(quill) {
            // console.log('editor blur!', quill);
        },
        onEditorFocus(quill) {
            // console.log('editor focus!', quill);
        },
        onEditorReady(quill) {
            // console.log('editor ready!', quill);
        },
        onEditorChange(quill, html, text) {
            // console.log('editor change!', quill, html, text);
        }
    }
};
</script>

<style scoped lang="scss">

</style>
