<template>
    <div class="block">
        <div style="height:50px"></div>
        <el-divider content-position="left">
            基础分页
        </el-divider>
        <el-pagination
            popper-class="picc-pagination-dropdown"
            background
            :current-page.sync="currentPage3"
            :pager-count="7"
            layout="prev, pager, next, jumper"
            :page-size="10"
            :total="100"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <el-divider content-position="left">
            分页：改变每页显示条目
        </el-divider>
        <el-pagination
            popper-class="picc-pagination-dropdown"
            style="margin:20px 0"
            background
            layout=" sizes, prev, pager, next, jumper"
            :current-page="1"
            :page-sizes="[5, 10, 20]"
            :total="99"
            :page-size="10"
        />
        <el-divider content-position="left">
            分页：显示总数
        </el-divider>
        <el-pagination
            popper-class="picc-pagination-dropdown"
            style="margin:20px 0"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="1"
            :page-sizes="[10, 20, 30]"
            :total="100"
            :page-size="10"
        />
    </div>
</template>

<script>
export default {
    name:"Pagination",
    data() {
        return {
            currentPage3: 5
        };
    },
    methods: {
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`);
        }
    }
};
</script>
<style lang="scss"></style>
