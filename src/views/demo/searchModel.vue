<template>
    <div>
        <el-form label-width="120px" :rules="rules" :model="form">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="我是搜索标题" prop="searchValue">
                        <SearchModel
                            ref="searchmodel"
                            v-model="form.searchValue"
                            class="dialog-table"
                            title="弹窗标题"
                            placeholder="请输入搜索内容"
                            clearable
                            width="100px"
                            :prop-data="propData"
                            :search-data="searchData"
                            :total="total"
                            @searchInfoFn="searchInfoFn"
                            @close="close"
                            @search="search()"
                            @selectValue="selectValue"
                        >
                            <template slot="table">
                                <div class="searchInfo">
                                    <el-input ref="searchInfo" v-model="searchvalue" clearable placeholder="搜索内容" @keyup.enter.native="searchQuery" />
                                    <i class="el-input__icon el-icon-search"></i>
                                </div>
                                <el-scrollbar>
                                    <el-table
                                        class="picc-table picc-table-readonly"
                                        :data="searchData"
                                        @row-click="changeRadio"
                                        @current-change="handleCurrentRowChange"
                                        @row-dblclick="dbclick"
                                    >
                                        <el-table-column label="选择" align="center" width="80" prop="radio">
                                            <template slot-scope="scope">
                                                <el-radio v-model="radioId" :label="scope.$index" class="textRadio">
                                                    <input type="hidden" />
                                                </el-radio>
                                            </template>
                                        </el-table-column>
                                        <el-table-column v-for="(item, index) in propData" :key="index" :prop="item.key" :label="item.value" align="center">
                                            <template slot-scope="scope">
                                                <div>{{ searchData[scope.$index][item.key] }}</div>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-scrollbar>
                                <el-pagination
                                    popper-class="picc-pagination-dropdown"
                                    background
                                    layout="total, sizes, prev, pager, next, jumper"
                                    :current-page="1"
                                    :page-sizes="[10, 20, 30]"
                                    :total="100"
                                    :page-size="10"
                                />
                            </template>
                        </SearchModel>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="联动双选框" prop="search" class="doublecheck">
                        <SearchDouble
                            ref="searchmodel1"
                            v-model="form.searchValue"
                            class="dialog-table doublesearch"
                            title="弹窗标题1"
                            placeholder="请输入搜索内容"
                            width="90px"
                            clearable
                            double
                            :prop-data="propData1"
                            :search-data="searchData1"
                            first-width="10%"
                            second-width="90%"
                            :total="total1"
                            @search="search1()"
                            @searchInfoFn="searchInfoFn1"
                            @close="close1"
                            @selectValue="selectValue1"
                            @clearSelect="clear"
                        >
                            <template slot="table">
                                <div class="searchInfo">
                                    <el-input ref="searchInfo1" v-model="searchvalue" clearable placeholder="搜索内容" @keyup.native="searchQuery" />
                                    <i class="el-input__icon el-icon-search"></i>
                                </div>
                                <el-scrollbar>
                                    <el-table
                                        class="picc-table picc-table-readonly"
                                        :data="searchData1"
                                        @row-click="changeRadio1"
                                        @current-change="handleCurrentRowChange1"
                                        @row-dblclick="dbclick1"
                                    >
                                        <el-table-column label="选择" align="center" width="80" prop="radio">
                                            <template slot-scope="scope">
                                                <el-radio v-model="radioId1" :label="scope.$index" class="textRadio">
                                                    <input type="hidden" />
                                                </el-radio>
                                            </template>
                                        </el-table-column>
                                        <el-table-column v-for="(item, index) in propData1" :key="index" :prop="item.key" :label="item.value" align="center">
                                            <template slot-scope="scope">
                                                <div>{{ searchData1[scope.$index][item.key] }}</div>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-scrollbar>
                                <el-pagination
                                    :style="'margin-bottom: 10px'"
                                    background
                                    layout="total, sizes, prev, pager, next, jumper"
                                    :current-page="1"
                                    :page-sizes="[10, 20, 30]"
                                    :total="100"
                                    :page-size="10"
                                />
                            </template>
                        </SearchDouble>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script>
import { getBatch, getBatch1 } from "@/api/robotDemo.js";
export default {
    name: "Searchmodel",
    data() {
        return {
            form: {
                searchValue: "",
                search111: "",
                search: ""
            },
            radioId: "1",
            radioId1: "1",
            searchInfo: {},
            searchvalue: "",
            rules: {
                searchValue: [
                    // { required: true, message: "请输入要搜索的内容", trigger: "blur" }
                    // { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" }
                ],
                search111: [{ required: true, message: "请输入要搜索的内容", trigger: "blur" }]
            },
            // table数据
            searchData: [],
            total: 0,
            total1: 0,
            searchData1: [],
            planBatchOptions: [
                // { id: 1, name: "测试1", batch: "code1" },
                // { id: 2, name: "测试2", batch: "code2" },
                // { id: 3, name: "测试3", batch: "code3" },
                // { id: 4, name: "测试4", batch: "code4" },
                // { id: 5, name: "测试5", batch: "code5" },
                // { id: 6, name: "测试1", batch: "code1" },
                // { id: 7, name: "测试2", batch: "code2" },
                // { id: 8, name: "测试3", batch: "code3" },
                // { id: 9, name: "测试4", batch: "code4" },
                // { id: 10, name: "测试5", batch: "code5" },
                // { id: 11, name: "测试1", batch: "code1" },
                // { id: 12, name: "测试2", batch: "code2" },
                // { id: 13, name: "测试3", batch: "code3" }
                // { id: 1, name: "测试1", batch: "code1", number: 1 },
                // { id: 2, name: "测试2", batch: "code2", number: 2 },
                // { id: 3, name: "测试3", batch: "code3", number: 3 },
                // { id: 4, name: "测试4", batch: "code4", number: 4 },
                // { id: 5, name: "测试5", batch: "code5", number: 5 }
            ],
            // table对应列内容的字段名[{key:'name',value:'名字'}]
            propData: [{ key: "name", value: "name" }],
            propData1: [
                { key: "code", value: "编码" },
                { key: "name", value: "名字" }
            ],
            // 是否显示气泡
            isTip: false
        };
    },
    methods: {
        // 选择表格每行数据
        changeRadio(index) {
            this.radioId = this.searchData.indexOf(index);
        },
        changeRadio1(index) {
            this.radioId1 = this.searchData1.indexOf(index);
        },
        // 选择每行数据
        handleCurrentRowChange(row) {
            if (!row) {
                this.radioId = "";
                return;
            }
            this.searchInfo = row;
            this.changeRadio(this.searchData.indexOf(row));
        },
        handleCurrentRowChange1(row) {
            if (!row) {
                this.radioId1 = "";
                return;
            }
            this.searchInfo = row;
            this.changeRadio1(this.searchData1.indexOf(row));
        },
        // 内层搜索方法
        searchQuery() {
            if (this.searchvalue) {
                if (this.searchData.length > 0) {
                    this.searchData.map((item, index) => {
                        item.radio = index;
                    });
                }
            } else {
                this.$message.error("请输入要搜索的数据！");
            }
        },
        // 点击确定时执行的方法
        searchInfoFn() {
            this.$refs.searchmodel.searchValue = this.searchInfo.name;
        },
        searchInfoFn1() {
            // this.form.search111 = this.searchInfo.name;
            this.$refs.searchmodel1.searchValue = this.searchInfo.code;
            this.$refs.searchmodel1.searchValue1 = this.searchInfo.name;
        },
        // 双击table某一行时执行的事件
        dbclick(row) {
            this.$refs.searchmodel.searchValue = row.name;
            this.$refs.searchmodel.modelVisible = false;
        },
        dbclick1(row) {
            this.$refs.searchmodel1.searchValue = row.code;
            this.$refs.searchmodel1.searchValue1 = row.name;
            // this.form.search111 = row.name;
            this.$refs.searchmodel1.modelVisible = false;
        },
        // 点击下拉数据时执行的方法
        selectValue(e) {
            this.$refs.searchmodel.searchValue = e.name;
        },
        selectValue1(e) {
            // this.form.search111 = e[1];
            this.$refs.searchmodel1.searchValue = e[0];
            this.$refs.searchmodel1.searchValue1 = e[1];
        },
        // 外层搜索方法
        search() {
            // this.searchData = this.planBatchOptions;
            // getBatch().then((response) => {
            //     this.searchData = response.PostCode.result;
            //     this.total = response.PostCode.totalCount;
            // });
            this.searchData = [
                {
                    code: 1,
                    date: "2016-05-03",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 2,
                    date: "2016-05-02",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 3,
                    date: "2016-05-04",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 4,
                    date: "2016-05-01",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 5,
                    date: "2016-05-08",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 6,
                    date: "2016-05-06",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 7,
                    date: "2016-05-07",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                }
            ];
            this.total = 7;
        },
        search1() {
            // this.searchData = this.planBatchOptions;
            // getBatch1().then((response) => {
            //     this.searchData1 = response.PostCode.result;
            //     this.total1 = response.PostCode.totalCount;
            // });
            this.searchData1 = [
                {
                    code: 1,
                    date: "2016-05-03",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 2,
                    date: "2016-05-02",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 3,
                    date: "2016-05-04",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 4,
                    date: "2016-05-01",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 5,
                    date: "2016-05-08",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 6,
                    date: "2016-05-06",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                },
                {
                    code: 7,
                    date: "2016-05-07",
                    name: "王小虎",
                    address: "上海市普陀区金沙江路 1518 弄"
                }
            ];
            this.total1 = 7;
        },
        // 关闭弹窗执行的回调
        close() {
            this.$refs.searchInfo.clear();
        },
        close1() {
            this.$refs.searchInfo1.clear();
        },
        // 点击清空按钮要执行的回调
        clear() {
            // 联动双选框清空第二个input内容
            this.form.search111 = "";
        },
        //  点击清空按钮要执行的回调
        clear1() {
            // 联动双选框清空第一个input内容
            this.$refs.searchmodel1.searchValue = "";
        },
        // 显示气泡
        showTooltip() {
            const input = this.$refs.searchInput.getInput();
            this.isTip = input.offsetWidth < input.scrollWidth;
        }
    }
};
</script>
<style lang="scss" scoped>
// 搜索弹窗
::v-deep .dialog-table {
    .el-dialog__body {
        margin-bottom: 50px;
        max-height: 390px;
        position: relative;
    }
    .el-scrollbar__wrap {
        max-height: 356px;
    }
    .el-pagination {
        margin-bottom: 10px;
        position: absolute;
        bottom: -50px;
        left: 50%;
        transform: translateX(-50%);
    }
    .el-scrollbar {
        width: 100%;
        height: 100%;
        .el-scrollbar__view {
            padding-right: 16px;
        }
        .el-scrollbar__bar.is-vertical {
            width: 4px;
        }
    }
    .searchInfo {
        width: 300px;
        position: absolute;
        top: -48px;
        right: 60px;
        .el-input__inner {
            padding-left: 30px !important;
        }
        .el-input__icon.el-icon-search {
            position: absolute;
            left: 5px;
        }
    }
    .el-table__header,
    .el-table__body {
        width: 100% !important;
    }
    .el-table {
        th {
            // height: 40px;
            // box-sizing: border-box;
            background: #f2f2f3;
            color: #000;
            line-height: 14px;
            // font-weight: bold;
            // padding: 0;
        }
        td {
            border-bottom-style: dashed;
        }
    }
}
// 联动双选框
::v-deep .doublecheck {
    .doublecheck-input {
        margin-left: 8px;
        .el-input__inner {
            padding-right: 12px !important;
        }
    }
}
</style>
