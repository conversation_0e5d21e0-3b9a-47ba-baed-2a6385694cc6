<template>
    <div>
        <el-row>
            <el-col :span="18" :offset="3">
                <el-divider content-position="left">
                    系统信息
                </el-divider>
                <el-alert :closable="true" show-icon type="info" class="picc-alert">
                    <slot name="description" class="">
                        <span class="description-span">你有条来自案件处理员你有条来自案件处理员你有条来自案件处</span>
                    </slot>
                </el-alert>
                <br />
                <el-alert :closable="true" show-icon type="success" class="picc-alert">
                    <slot name="description" class="">
                        <span class="description-span">你有条来自案件处理员你有条来自案件处理员你有条来自案件处</span>
                    </slot>
                </el-alert>
                <br />
                <el-alert :closable="true" show-icon type="error" class="picc-alert">
                    <slot name="description" class="">
                        <span class="description-span">你有条来自案件处理员你有条来</span>
                    </slot>
                </el-alert>
                <br />
                <el-alert :closable="true" show-icon type="warning" class="picc-alert">
                    <slot name="description" style="padding-right:0">
                        <span class="description-span"> 你有条来自案件处理员你有条来自案件处理员 </span>
                        <el-link type="primary" :underline="false">
                            查询
                        </el-link>
                    </slot>
                </el-alert>
                <br />
                <el-alert :closable="false" show-icon class="picc-alert picc-alert-loading">
                    <slot name="description" class="">
                        <i class="el-icon-loading"></i>
                        <span class="description-span"> 加载中... </span>
                    </slot>
                </el-alert>
            </el-col>
        </el-row>
    </div>
</template>

<script>
export default {
    name: "Message",
    data() {
        return {
            value: 4
        };
    }
};
</script>

<style lang="scss" scoped>
.picc-alert {
    margin-bottom: 20px;
}
::v-deep .picc-alert span.el-link--inner {
    color: #1c61fc !important;
    padding-right: 8px;
}
</style>
