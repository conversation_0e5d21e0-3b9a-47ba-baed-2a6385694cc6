<template>
    <div>
        <!-- 提示信息 -->
        <div class="top-tip-div">
            <div class="top-tip">
                <p>列表经过【视觉规范v1.3】版本的更新，现存在两种版本：一是【上下间距舒适版】；二是【上下间距紧凑版】；各项目组可根据实际业务需求，灵活使用；</p>
            </div>
        </div>
        <div style="margin-top: 44px;">
            <BoxHead title="上下间距舒适版(内容高度44px，输入框高度32px)">
                <div slot="outsideOperation">
                    <span class="span-red">在使用过程中，需注意，同一系统必须使用同一版本！</span>
                </div>
            </BoxHead>
        </div>
        <el-row :gutter="20">
            <el-col :span="24">
                <el-divider content-position="left">
                    列表：可操作
                </el-divider>
                <el-table
                    :data="tableData" class="picc-table picc-table-readonly picc-table-1st-col-icon"
                    style="width: 100%"
                >
                    <el-table-column prop="taskCode" label="任务号" width="240">
                        <template slot-scope="scope">
                            <!-- <i v-if="scope.row.warningShow" class="el-icon-warning-outline" style="color: #ffc528"></i> -->
                            {{ scope.row.taskCode }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="任务名" width="104" />
                    <el-table-column prop="date" label="任务创建时间" width="120" />
                    <el-table-column prop="person" label="业务员" width="80" />
                    <el-table-column prop="" label="当前任务情况" width="112">
                        <el-button type="text">
                            待出单
                        </el-button>
                    </el-table-column>
                    <el-table-column prop="date" label="核保时间" width="120" />
                    <el-table-column label="操作" width="104">
                        <el-button type="text">
                            其他
                        </el-button>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <!--  -->
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：可选择、多条滑动列表
                </el-divider>
                <el-table
                    :data="tableSelectData" class="picc-table picc-table-normal picc-table-selectable"
                    style="width: 100%" max-height="250"
                >
                    <el-table-column type="selection" width="56" />
                    <el-table-column prop="title" label="活动名称" width="240" />
                    <el-table-column prop="name" label="客户姓名" width="120" />
                    <el-table-column prop="rate" label="客户评级" width="160" />
                    <el-table-column prop="target" label="标的/车牌号" width="160" />
                    <el-table-column prop="date" label="终保日期" width="160" />
                </el-table>
            </el-col>
        </el-row>
        <!--  -->
        <!--  -->
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：列数超过页面最大数量
                </el-divider>
                <el-table
                    :data="tableSelectData" class="picc-table picc-table-normal picc-table-selectable"
                    max-height="200" style="width: 100%"
                >
                    <el-table-column type="selection" width="56" />
                    <el-table-column prop="title" label="活动名称" width="240" />
                    <el-table-column prop="name" label="客户姓名" width="120" />
                    <el-table-column prop="rate" label="客户评级" width="160" />
                    <el-table-column prop="target" label="标的/车牌号" width="160" />
                    <el-table-column prop="date" label="终保日期" width="160" />
                    <el-table-column prop="title" label="活动名称" width="240" />
                    <el-table-column prop="name" label="客户姓名" width="120" />
                    <el-table-column prop="rate" label="客户评级" width="160" />
                    <el-table-column prop="target" label="标的/车牌号" width="160" />
                    <el-table-column prop="date" label="终保日期" width="160" />
                </el-table>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：列表超出页面最大数量固定列
                </el-divider>
                <el-table :data="tableSelectData" class="picc-table picc-table-normal" max-height="260" style="width: 100%">
                    <el-table-column prop="title" label="活动名称" width="240" fixed="left" />
                    <el-table-column prop="name" label="客户姓名" width="120" />
                    <el-table-column prop="rate" label="客户评级" width="160" />
                    <el-table-column prop="target" label="标的/车牌号" width="160" />
                    <el-table-column prop="name" label="客户姓名" width="120" />
                    <el-table-column prop="rate" label="客户评级" width="160" />
                    <el-table-column prop="target" label="标的/车牌号" width="160" />
                    <el-table-column prop="date" label="终保日期" width="160" fixed="right" />
                </el-table>
            </el-col>
        </el-row>
        <!--  -->
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：仅阅读
                </el-divider>
                <el-table :data="tableReadData" class="picc-table picc-table-readonly" style="width: 100%">
                    <el-table-column prop="taskCode" label="风险单位编号" width="" />
                    <el-table-column prop="taskCode" label="保单风险单位编号" width="" />
                    <el-table-column prop="taskCode" label="主风险单位编号" width="" />
                    <el-table-column prop="amount" label="保险金额" width="104" />
                    <el-table-column prop="insurance" label="保费" width="88" />
                    <el-table-column prop="scale" label="分出比例" width="80" />
                    <el-table-column prop="money" label="分出金额" width="88" />
                    <el-table-column prop="date" label="起保/终保日期" width="" />
                </el-table>
            </el-col>
        </el-row>
        <!--  -->
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：纯数字
                </el-divider>
                <el-table :data="numData" class="picc-table picc-table-readonly" style="width: 100%" :cell-style="cellStyle" :header-cell-style="cellStyle">
                    <el-table-column prop="no" label="序号" width="80" />
                    <el-table-column prop="coin" label="币别" width="80" />
                    <el-table-column prop="money" label="总保费" width="104" />
                    <el-table-column prop="taxation" label="保费税合计" width="" />
                    <el-table-column prop="money" label="我方总额" width="" />
                    <el-table-column prop="other" label="代共保缴税总额" width="" />
                    <el-table-column prop="taxation" label="代联保缴税总额" width="" />
                </el-table>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：多条翻页列表
                </el-divider>
                <el-table
                    :data="tableDataPage" class="picc-table picc-table-readonly picc-table-operate"
                    style="width: 100%"
                >
                    <el-table-column prop="taskCode" label="任务号" width="" />
                    <el-table-column prop="name" label="任务号" width="" />
                    <el-table-column prop="number" label="任务号" width="" />
                    <el-table-column prop="date" label="任务号" width="" />
                    <el-table-column prop="other" label="任务号" width="" />
                </el-table>

                <el-pagination
                    popper-class="picc-pagination-dropdown" style="margin:20px 0; text-align:center" background
                    layout="total, sizes, prev, pager, next, jumper" :current-page="listPageNo" :page-sizes="[3, 5, 10, 20]"
                    :total="totel" :page-size="listPageSize" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </el-col>
        </el-row>
        <!--  -->
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：多行表头
                </el-divider>
                <el-table :data="numData" border style="width: 100%" class="picc-table picc-table-more">
                    <el-table-column prop="date" label="日期" width="150" />
                    <el-table-column label="配送信息">
                        <el-table-column prop="name" label="姓名" />
                        <el-table-column label="地址">
                            <el-table-column prop="other" label="省份" />
                            <el-table-column prop="taskCode" label="市区" />
                            <el-table-column prop="number" label="地址" />
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <!--  -->
        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：可填写
                </el-divider>
                <!-- {{ numData }} -->
                <el-table :data="numData" class="picc-table picc-table-write" style="width: 100%">
                    <el-table-column prop="name" label="姓名" width="240">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.name" placeholder="请输入" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="date" label="名称" width="240">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.date" placeholder="请选择">
                                <el-option key="选项1" value="黄金糕" />
                                <el-option key="选项2" value="黄金糕222" />
                                <el-option key="选项3" value="黄金糕3333333" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="number" label="名称" width="240">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.number" placeholder="请选择">
                                <el-option key="选项1" value="黄金糕" />
                                <el-option key="选项2" value="黄金糕2222" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="taskCode" label="姓名">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.taskCode" placeholder="请输入" />
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>

        <el-row :gutter="20">
            <el-col :span="24" style="margin-top: 20px;">
                <el-divider content-position="left">
                    列表：操作单元功能入口
                </el-divider>
                <el-table :data="tableData" class="picc-table picc-table-readonly picc-table-operate" style="width: 100%; ">
                    <el-table-column prop="taskCode" label="任务号" width="" />
                    <el-table-column prop="name" label="任务号" width="" />
                    <el-table-column prop="number" label="任务号" width="" />
                    <el-table-column prop="date" label="任务号" width="" />
                    <el-table-column prop="other" label="操作" width="">
                        <!-- <template> -->
                        <DropList :data-list="dataList" @pushPage="onDpChange($event)" />
                        <!-- </template> -->
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>

        <BoxHead title="上下间距紧凑版(内容高度40px，输入框高度28px)" class="picc-box-head-compact">
            <div slot="outsideOperation">
            </div>
        </BoxHead>

        <el-row :gutter="20">
            <el-col :span="24">
                <el-divider content-position="left">
                    列表：上下间距紧凑
                </el-divider>
                <el-table :data="tableData" class="picc-table picc-table-compact" style="width: 100%; margin-bottom:100px">
                    <el-table-column prop="taskCode" label="任务号" width="200">
                        <template slot-scope="scope">
                            {{ scope.row.taskCode }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="任务名" width="104" />
                    <el-table-column prop="date" label="任务创建时间" width="120" />
                    <el-table-column prop="taskCode" label="姓名" width="240" class-name="picc-table-cell-compact">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.taskCode" placeholder="请输入" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="person" label="业务员" width="80" />
                    <el-table-column prop="" label="当前任务情况" width="112">
                        <el-button type="text">
                            待出单
                        </el-button>
                    </el-table-column>
                    <el-table-column prop="date" label="核保时间" width="120" />
                    <el-table-column label="操作" width="104">
                        <el-button type="text">
                            其他
                        </el-button>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
    name: "Table",
    data() {
        return {
            tableData: [],
            tableSelectData: [],
            tableReadData: [],
            tableDataPage: [],
            numData: [],
            listPageSize: 3,
            listPageNo: 1,
            total: 0,
            // 列表操作框
            dataList: ["详情", "编辑编辑", "删除"]
        };
    },
    created() {
        for (let i = 0; i < 5; i++) {
            this.tableData.push({
                taskCode: "YGD0201911010000000854",
                date: "2019/08/25",
                other: "",
                warningShow: true,
                name: "川AA780D",
                number: Math.random(),
                person: "张惠红"
            });
            this.tableSelectData.push({
                title: "中秋佳节车险促销活动",
                name: "李真真",
                rate: "★★★★★",
                target: "张惠红",
                date: "2019/08/23"
            });
            this.tableReadData.push({
                taskCode: "PYEJ20193200AC00013511-0001",
                amount: "20033.09",
                insurance: "9109.00",
                scale: "20%",
                money: "1809.78",
                date: "起保2019-04-17终保2020-04-16"
            });
        }
        for (let i = 0; i < 2; i++) {
            this.tableDataPage.push({
                taskCode: "100000",
                date: new Date().getTime(),
                other: "",
                warningShow: true,
                name: "Test",
                number: Math.random()
            });
        }
        this.totel = this.tableData.length;
        for (let i = 0; i < 5; i++) {
            this.numData.push({
                no: i + 1,
                coin: "CNY",
                money: "123456.78",
                taxation: "234.76",
                other: "24.65",
                taskCode: "100002",
                date: new Date().getTime(),
                warningShow: true,
                name: "1000004",
                number: Math.random()
            });
        }
    },
    methods: {
        handleSizeChange(val) {
            this.listPageSize = val;
        },
        handleCurrentChange(val) {
            this.listPageNo = val - 1;
            this.tableDataPage = [
                { taskCode: "11111", date: 123423142314, other: "是的", name: "name" },
                { taskCode: "222222", date: 4543254325423, other: "是的", name: "name" }
            ];
        },
        test({ row, column, rowIndex, columnIndex }) {
            if (rowIndex === 1) {
                return { display: "none" };
            }
        },
        // 下拉组件
        onDpChange(e) {
            if (e.index === 0) {
                this.$router.push("/demo/form");
            } else if (e.index === 1) {
                this.$router.push("/demo/labels");
            } else if (e.index === 2) {
                this.$message.success("点击这里可以操作");
            }
        },
        cellStyle(column) {
            if (column.columnIndex >= 2) {
                return {'text-align': 'end'};
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.triangle {
    border-top: 50px solid #1ac88e;
    border-right: 50px solid transparent;

    span {
        position: absolute;
        transform: rotate(-45deg);
        top: 20px;
        left: 20px;
        color: #fff;
    }
}

.picc-table-write {

    td,
    th {}
}

::v-deep .el-table__header,
::v-deep .el-table__body {
    width: 100% !important;
    overflow: scroll;
}

.picc-table-operate.el-table ::v-deep {
    overflow: visible !important;

    .el-table__body-wrapper,
    .cell {
        overflow: visible !important;
    }
}

// 滚动条样式
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: rgba($color: #292b34, $alpha: 0.1);
    border-radius: 4px;
}

::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    border-radius: 4px;
    background-color: transparent;
}

// 顶部提示信息样式
.top-tip-div {
    width: 100%;
    background-color: #fff;
    position: fixed;
    top: 92px;
    z-index: 5;

    .top-tip {
        width: 100%;
        background-color: rgba($color: #ffe4e0, $alpha: 0.8);
        padding: 14px 0 14px 24px;

        p {
            font-size: 14px;
            color: #292b34;
            line-height: 16px;
            margin: 0;
        }

        span {
            color: red;
        }
    }
}

::v-deep .picc-box-head {
    justify-content: left;

    .span-red {
        margin-left: 6px;
        color: red;
        font-size: 14px;
    }
}
</style>
