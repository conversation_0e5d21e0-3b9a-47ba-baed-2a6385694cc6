<template>
    <div class="error-page-container">
        <div class="error-page">
            <div class="pic-404">
                <img class="pic-404__parent" src="@/assets/500.png" alt="404" />
            </div>
            <div class="main">
                <div class="main__title">
                    抱歉，服务器出错
                </div>
                <div class="main__info">
                    请不要惊慌，请检查网络是否正常连接
                </div>
                <el-button round type="primary" @click="refresh">
                    刷新一下
                </el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "Page500",
    methods: {
        refresh() {
            this.$router.go(0);
        }
    }
};
</script>

<style lang="scss" scoped>
.error-page-container {
    padding-top: 80px;
}
.error-page {
    position: relative;
    width: 1200px;
    margin: 0 auto;
    overflow: hidden;
    .pic-404 {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 320px;
        height: 200px;
        margin: 0 auto;
        &__parent {
            position: relative;
            width: 219px;
            height: 168px;
            left: 24px;
        }
    }
    .main {
        position: relative;
        margin: 0 auto;
        overflow: hidden;
        text-align: center;
        &__title {
            font-size: 20px;
            font-weight: bold;
            line-height: 20px;
            color: #292b34;
            opacity: 0;
            margin: 16px 0;
            animation-name: slideUp;
            animation-duration: 0.5s;
            animation-fill-mode: forwards;
        }
        &__info {
            font-size: 14px;
            line-height: 14px;
            color: #65677a;
            opacity: 0;
            margin-bottom: 32px;
            animation-name: slideUp;
            animation-duration: 0.5s;
            animation-delay: 0.2s;
            animation-fill-mode: forwards;
        }
        &__return-home {
            display: block;
            width: 110px;
            height: 36px;
            background: #1482f0;
            border-radius: 100px;
            text-align: center;
            color: #ffffff;
            opacity: 0;
            margin: 0 auto;
            font-size: 14px;
            line-height: 36px;
            cursor: pointer;
            animation-name: slideUp;
            animation-duration: 0.5s;
            animation-delay: 0.3s;
            animation-fill-mode: forwards;
        }
        @keyframes slideUp {
            0% {
                transform: translateY(60px);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }
    }
}
</style>
