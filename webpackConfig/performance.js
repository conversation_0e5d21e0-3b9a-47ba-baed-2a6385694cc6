const { merge } = require('webpack-merge');
const webpackProConfig = require('./production.js');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const smp = new SpeedMeasurePlugin();
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
// vue-cli3 自带打包文件大小分析工具，所以该部分可以省略
// webpackProConfig.configureWebpack.plugins.push(new BundleAnalyzerPlugin())
let performanceConfig = merge(webpackProConfig, {
    outputDir: `dist-performance`
})
performanceConfig.configureWebpack = smp.wrap(performanceConfig.configureWebpack)

module.exports = performanceConfig