module.exports = {
    '/api/misc': {
        target: 'http://localhost:18014',
        changeOrigin: true, // 是否跨域
        pathRewrite: {
            '^/api/misc': '' // 重写接口
        }
    },
    '/apicaptcha': {
        target: 'http://localhost:8990',
        // target: 'http://*************:8990',
        changeOrigin: true, // 是否跨域
        pathRewrite: {
            '^/apicaptcha': '/apicaptcha' // 重写接口
        }
    },
    // 'docs': {
    //     target: 'http://**************',
    //     changeOrigin: true, // 是否跨域
    // },
    // 'open': {
    //     target: 'http://**************',
    //     changeOrigin: true, // 是否跨域
    // },
    '/sub1': {
        target: 'http://************:9528',
        changeOrigin: true, // 是否跨域
        pathRewrite: {
            '^/sub1': '/sub1' // 重写接口
        }
    },
    '/sub2': {
        target: 'http://************:9529',
        changeOrigin: true, // 是否跨域
        pathRewrite: {
            '^/sub2': '/sub2' // 重写接口
        }
    }
};