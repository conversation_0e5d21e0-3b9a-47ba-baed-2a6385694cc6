const path = require("path");
const defaultSettings = require("../src/settings.js");
// const HardSourceWebpackPlugin = require("hard-source-webpack-plugin");//启动报错
const externals = require("./externals.js");
const CopyPlugin = require("copy-webpack-plugin");

function resolve(dir) {
    return path.join(__dirname, dir);
}
// default value is dist
const outputDir = "dist";
//
const name = defaultSettings.title || "vue Element Admin"; // page title

const webpackConfig = {
    /**
     * You will need to set publicPath if you plan to deploy your site under a sub path,
     * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
     * then publicPath should be set to "/bar/".
     * In most cases please use '/' !!!
     * Detail: https://cli.vuejs.org/config/#publicpath
     */
    //publicPath: "/",
    outputDir: outputDir,
    assetsDir: "static",
    lintOnSave: process.env.NODE_ENV === "development",
    transpileDependencies: true,
    configureWebpack: {
        // provide the app's title in webpack's name field, so that
        // it can be accessed in index.html to inject the correct title.
        name: name,
        resolve: {
            alias: {
                "@": resolve("../src")
            }
        },
        /**
         * ================ 开发环境优化、生产打包环境优化 ==============
         */
        externals: externals.target === "externals" ? externals.externals : {},

        /**
         * qiankunjs--子应用--打包后的文件的使用方式配置---开始
         */
        devServer: {
            headers: process.env.VUE_APP_USE_TYPE !== "MicroApp" ? {} : { "Access-Control-Allow-Origin": "*" }
        },
        output:
            process.env.VUE_APP_USE_TYPE !== "MicroApp" ? {} : { library: `${name}-[name]`, libraryTarget: "umd", chunkLoadingGlobal: `webpackJsonp_${name}` },

        /**
      * qiankunjs--子应用--打包后的文件的使用方式配置---结束
      */
        plugins: [
            /**
             * ================ 开发环境优化、生产打包环境优化 ==============
             */
            // new HardSourceWebpackPlugin() // 使用磁盘缓存进行打包，第一次加载会慢，之后会很快，流水线打包没啥用，可能还会更慢，配合流水线的缓存操作
            //
            new CopyPlugin({
                patterns: [
                    { from: "./node_modules/web-plugin/common/theme", to: "./static/mutiTheme/" }
                ],
            }),
        ],
        resolve: {
            // 手动引入在webpack5中没有引入的node的核心模块，在tagviews组件中使用
            fallback: {
                path: require.resolve("path-browserify")
            }
        },
    },
    chainWebpack(config) {
        /**
         * 通过链接引入第三方资源库
         *  - 需要在.cdn位置定义相应的js文件地址及css地址
         * 如果开发代码模块导入第三方资源库
         *  - 则可以删除或者注释掉
         *
         */
        config.plugin("html").tap((args) => {
            args[0].cdn = externals.target === "externals" ? externals.cdn : "";
            return args;
        });
        config.plugins.delete("preload"); // TODO: need test
        config.plugins.delete("prefetch"); // TODO: need test
        // xss 处理
        // config.module
        //     .rule("vue")
        //     .use("vue-loader")
        //     .loader("vue-loader")
        //     .tap(options => {
        //         options.compilerOptions.directives = {
        //             html(node, directiveMeta) {
        //                 (node.props || (node.props = [])).push({
        //                     name: "innerHTML",
        //                     value: `$xss(_s(${directiveMeta.value}))`
        //                 });
        //             }
        //         };
        //         return options;
        //     });
        /**
     * 该部分为web-template手动添加的配置项，实际项目中可以按照具体要求进行删改
     */
        // set svg-sprite-loader
        config.module.rule("svg").exclude.add(resolve("../src/icons")).end();
        config.module
            .rule("icons")
            .test(/\.svg$/)
            .include.add(resolve("../src/icons"))
            .end()
            .use("svg-sprite-loader")
            .loader("svg-sprite-loader")
            .options({
                symbolId: "icon-[name]"
            })
            .end();
    }
};

module.exports = webpackConfig;
