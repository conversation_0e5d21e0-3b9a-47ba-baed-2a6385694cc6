module.exports = {
    /**
     * target
     * default value is external,
     * external | split
     */
    target: "externals",

    /**
     * target = externals
     * 必填项
     * 如果target为externals，则需要配置cdn中相应的css和js文件地址，可以是cdn地址，也可以是相对路径，将静态文件放置public下
     */
    cdn: {
        css: [],
        js: [
            // 'https://cdn.bootcdn.net/ajax/libs/vue/2.6.11/vue.js',
            // "https://cdn.bootcdn.net/ajax/libs/vue-router/3.0.2/vue-router.js",
            // "https://cdn.bootcdn.net/ajax/libs/vuex/3.1.0/vuex.js",
            // "https://cdn.bootcdn.net/ajax/libs/element-ui/2.12.0/index.js"
            // 'http://10.10.4.66/theme/3.2.0/picc.umd.js',
        ]
    },
    /**
     * target = externals
     * 必填项
     * target为externals状态下externals为必填项
     */
    externals: {
        // "element-ui": "Element"
        // vue: "Vue",
        // 'web-plugin': 'picc',
        // "vue-router": "VueRouter",
        // vuex: "Vuex"
    },
    //
    cacheGroups: {
        elementUI: {
            name: "chunk-elementUI", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
            maxSize: 240000
        }
    }
};
