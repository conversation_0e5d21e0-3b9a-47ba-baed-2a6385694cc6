const { merge } = require("webpack-merge");
const webpackBaseConfig = require("./base.js");
const port = 9527;

module.exports = merge(webpackBaseConfig, {
    productionSourceMap: false,
    devServer: {
        host: "0.0.0.0",
        port: port,
        open: false,
        client: {
            overlay: {
                warnings: false,
                errors: true
            }
        },

        /**
         * ================ 开发环境优化 1 ==============
         */
        hot: true, //启用js HMR 热模块替换
        compress: true, // 启用gzip压缩
        //
        proxy: require("./proxy.js")
    },
    configureWebpack: {
        plugins: []

    },
    chainWebpack(config) {
        /**
         * ================ 开发环境优化 2 ==============
         * 可选配置  [inline-|hidden-|eval-][nosources-][cheap-[module-]]source-map
         * CSDN解释 https://blog.csdn.net/qq_46312220/article/details/107822924
         */
        config.devtool("cheap-source-map"); // 启用Source-map，能在开发过程中快速定位js代码报错的具体为止

        config.optimization.splitChunks({
            chunks: "all"
        });
    }
});
