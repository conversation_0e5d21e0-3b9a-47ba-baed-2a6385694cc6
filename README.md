# pdfc3-web-template 使用手册

## 内网 npm 仓库配置（4.0.0以前版本）

```shell
//内网npm仓库配置
npm config set sass_binary_site=http://10.9.18.171:10001/sass/node-sass/releases/download
npm config set registry=http://10.10.1.68:8082/repository/npm-all/
npm install
```

## 内网 npm 仓库配置（4.0.0以后的版本）

```shell
//内网npm仓库配置
npm config set registry=http://10.10.1.68:8082/repository/npm-all/
npm install
```

## 依赖安装

依赖文件中的web-plugin和img-async-load为自己开发的包，只存在于内网仓库，其他的包都可以从外网下载下来

常用命令：

```shell
npm i（或install）               依赖安装命令
npm run dev                     开发模式启动命令
npm run build:prod              生产发布打包命令
```

## 常见配置项

