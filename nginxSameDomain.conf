#
user root;
worker_processes  4;

error_log /data/log/nginx/error.log  error;


events {
    worker_connections  65535;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 200m;
    server_tokens  off; # 隐藏Nginx的版本信息
    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';
    log_format main 'server="$server_name" host="$host” dest_port="$server_port"'               
                    #'src="$remote_addr" ip="$realip_remote_addr" user="$remote_user" '
                    'time_local="$time_local" http_status="$status" '
                    'http_referer="$http_referer" http_user_agent="$http_user_agent" '
                    'http_x_forwarded_for="$http_x_forwarded_for" '
                    'http_x_header="$http_x_header" uri_query="$query_string" uri_path="$uri" '
                    'request=$request http_method="$request_method" '; 
    access_log /data/log/nginx/access.log main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    # 打开gzip
    gzip  on;
    gzip_min_length 1k;
    gzip_buffers    32 4k;
    gzip_http_version 1.0;
    gzip_comp_level 5;
    gzip_types text/plain text/css application/json application/javascript application/xml;
    gzip_vary on;
    gzip_proxied off;

    #gzip  on;

	server {
        listen  9527;
        server_name  localhost;

        # 禁止访问隐藏文件
        location ~ /\. {deny all; return 404; }

        location /status {
            stub_status on;
        }
		
		root  /demo/mainDemo/dist; # 项目组修改为自己实际地址
		try_files $uri $uri/ /index.html; # 项目组修改为自己实际地址
		etag off; # 集群部署状态下建议关闭，单机部署建议开启
		charset utf-8; #编码格式
		#set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中
		
		#转发子应用1
		location ^~/sub1/ {
			proxy_pass  http://localhost:9528/sub1/;
		}
		#转发子应用2
		location ^~/sub2/ {
			proxy_pass  http://localhost:9529/sub2/;
		}
		
		location ~* .*\.(js|css)$ {
			add_header Cache-Control max-age=3600;
			expires 1h;
        }
		location ~* .*\.(ttf|woff)$ {
			add_header Cache-Control max-age=2592000;
			expires 30d;
        }
		location ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$ {
			add_header Cache-Control max-age=86400;
			expires 1d;
        }
        location ~* .*\.(htm|html)$ {
			expires 0;
			add_header Cache-Control no-store;
			add_header Pragma  no-store;
        }


        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
	server {
        listen  9528;
        server_name  localhost;

		# 禁止访问隐藏文件
        location ~ /\. {deny all; return 404; }

        location /status {
            stub_status on;
        }
		
		root  /demo/mainDemoSub1/dist; # 项目组修改为自己实际地址
		try_files $uri /sub1/$uri/ /sub1/index.html; # 项目组修改为自己实际地址
		etag off; # 集群部署状态下建议关闭，单机部署建议开启
		charset utf-8; #编码格式
		#set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中
		location ~* .*\.(js|css)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			add_header Cache-Control max-age=3600;
			expires 1h;
        }
		location ~* .*\.(ttf|woff)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			add_header Cache-Control max-age=2592000;
			expires 30d;
        }
		location ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			add_header Cache-Control max-age=86400;
			expires 1d;
        }
        location ~* .*\.(htm|html)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			expires 0;
			add_header Cache-Control no-store;
			add_header Pragma  no-store;
        }
		# location /front/0501020202/ {
		# 	proxy_pass  http://*************:80/zt/p050102/0501020202;
		# }
        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
	server {
        listen  9529;
        server_name  localhost;
        
        # 禁止访问隐藏文件
        location ~ /\. {deny all; return 404; }

        location /status {
            stub_status on;
        }
		
		root  /demo/mainDemoSub2/dist; # 项目组修改为自己实际地址
		try_files $uri /sub2/$uri/ /sub2/index.html; # 项目组修改为自己实际地址
		etag off; # 集群部署状态下建议关闭，单机部署建议开启
		charset utf-8; #编码格式
		#set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中
		location ~* .*\.(js|css)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			add_header Cache-Control max-age=3600;
			expires 1h;
        }
		location ~* .*\.(ttf|woff)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			add_header Cache-Control max-age=2592000;
			expires 30d;
        }
		location ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			add_header Cache-Control max-age=86400;
			expires 1d;
        }
        location ~* .*\.(htm|html)$ {
			add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
			expires 0;
			add_header Cache-Control no-store;
			add_header Pragma  no-store;
        }
		# location /front/0501020202/ {
		# 	proxy_pass  http://*************:80/zt/p050102/0501020202;
		# }
        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
	
	
	server {
		listen       5000;
		server_name  localhost;

		if ( $time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})" ) {
			set $year $1;
			set $month $2;
			set $day $3;
		}
		location / { 
			proxy_set_header X-Real-IP $remote_addr; 
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; 
			proxy_hide_header X-Powered-By;
			proxy_hide_header Server;
			proxy_pass http://************:8888;
		}
		  
		#统一门户应用 0201070201
		location /front/0201070201/ {
				proxy_set_header X-Real-IP $remote_addr; 
				proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; 
				proxy_hide_header X-Powered-By;
				proxy_hide_header Server; 
				#proxy_pass http://**********:10100/qt/p020107/0201070201/;
				proxy_pass http://*************:10000/;
		}
		location ~ /\. {deny all; return 404; }

		error_page   500 502 503 504  /50x.html;
		location = /50x.html {
			root   html;
		}
		  

	}
	server {
        listen  9627;
        server_name  localhost;
        charset utf-8;

        location / {
            root  /root/printtest/dist;
			try_files  $uri $uri/ /index.html;

        }
        
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
	


}
