const imgAsyncCall = {

    headers: [], // request header sent, if not empty
    /**
     * 
     * @param {*} url the image url 
     * @param {*} ele each dom element
     */
    imageRequest(url, ele) {
        const xhr = new XMLHttpRequest();
        xhr.open('get', url, true);
        if (this.headers.length) {
            this.headers.forEach(item => {
                xhr.setRequestHeader(`${item.header}`, item.content);
            })
        }
        xhr.responseType = 'blob'
        xhr.onreadystatechange = function(e) {
            if (xhr.status === 200 && xhr.readyState === XMLHttpRequest.DONE) {
                ele.src = URL.createObjectURL(xhr.response)
                ele.onload = function() {
                    URL.revokeObjectURL(ele.src)
                }
            }
        }
        xhr.send()
    },
    /**
     * for async image load in IE, except Edge
     */
    asyncImgLoadForIE() {
        var imgNodes = document.querySelectorAll('img')
        this.convertToArray(imgNodes).forEach((item, index) => {
            var url = item.dataset.src;
            if (url) {
                this.imageRequest(url, item)
            }
        })
    },
    /**
     * use better method window.customElement to make the image async load
     */
    asyncImgLoad() {
        // 
        const that = this;
        class asyncImage extends HTMLImageElement {
            constructor() {
                super();
            }
            connectedCallback() {
                that.imageRequest(this.getAttribute('data-src'), this)
            }
        }
        customElements.define('async-img', asyncImage, {
            extends: 'img'
        });
    },
    /**
     * transfer the Dom fake array to real array type
     * @param {Array} nodes is fake array type, it's Dom list.
     */
    convertToArray(nodes) {
        var array = null;
        try {
            array = Array.prototype.slice.call(nodes, 0); // 针对非IE浏览器
        } catch (ex) {
            array = new Array();
            for (var i = 0, len = nodes.length; i < len; i++) {
                array.push(nodes[i]);
            }
        }
        return array;
    },
    /**
     * init the async image load
     * To set the headers
     * check the IE or not
     * @param {Array} headers
     */
    init(headers) {
        this.headers = headers;
        // 
        /**
         * Vue template attribute keyword 'is' is conflict with the window.customElements
         */
        // if (window.Vue) {
        //     this.asyncImgLoadForIE()
        // } else {
        //     if (!this.IECheck()) {
        //         this.asyncImgLoadForIE()
        //     } else {
        //         this.asyncImgLoad()
        //     }
        // }
        this.asyncImgLoadForIE()

    },
    /**
     * IE browsers check, except the edge, because the edage is nearly from chrome.
     */
    IECheck() {
        if (!!window.ActiveXObject || "ActiveXObject" in window || window.navigator.userAgent.indexOf("MSIE") >= 0) {
            return false;
        } else {
            return true;
        }
    }

}

export const IECheck = imgAsyncCall.IECheck;
export const imgAsyncLoad = imgAsyncCall;