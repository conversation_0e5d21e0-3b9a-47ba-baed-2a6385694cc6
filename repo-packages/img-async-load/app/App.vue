<template>
  <div>
    <!-- <img data-src="http://**********:3001/img.jpg" alt="" is="async-img" />
    <img data-src="http://**********:3001/2.jpg" alt="" is="async-img" />
    <img data-src="http://**********:3001/1.jpg" alt="" is="async-img" />
    <img data-src="http://**********:3001/3.jpg" alt="" is="async-img" />
    <img data-src="http://**********:3001/4.jpg" alt="" is="async-img" />
    <img data-src="http://**********:3001/5.jpg" alt="" is="async-img" />
    <img data-src="http://**********:3001/6.jpg" alt="" is="async-img" /> -->
    <img data-src="http://**********:3001/img.jpg" alt="" />
    <img data-src="http://**********:3001/2.jpg" alt="" />
    <img data-src="http://**********:3001/1.jpg" alt="" />
    <img data-src="http://**********:3001/3.jpg" alt="" />
    <img data-src="http://**********:3001/4.jpg" alt="" />
    <img data-src="http://**********:3001/5.jpg" alt="" />
    <img data-src="http://**********:3001/6.jpg" alt="" />
  </div>
</template>
<script>
// import asyncImg from "./async-img.vue";
import { imgAsyncLoad } from "../dist/bundle";

export default {
  //   components: {
  //     asyncImg,
  //   },
  mounted() {
    // console.log(imgAsyncLoad);
    imgAsyncLoad.init([
      {
        header: "token",
        content: "123456789",
      },
    ]);
  },
};
</script>
