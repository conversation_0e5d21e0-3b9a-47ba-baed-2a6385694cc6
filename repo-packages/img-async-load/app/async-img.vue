<template>
  <img ref="img" :class="className" />
</template>
<script>
export default {
  //
  data() {
    return {
      headers: [
        {
          header: "token",
          content: "123",
        },
      ],
    };
  },
  props: {
    dataSrc: {
      required: true,
      type: String,
    },
    className: {
      required: false,
      type: String,
      default: function() {
        return "";
      },
    },
  },
  mounted() {
    console.log(this.$root.headers);
    this.imageRequest(this.dataSrc, this.$vnode);
  },
  methods: {
    imageRequest(url, ele) {
      const xhr = new XMLHttpRequest();
      xhr.open("get", url, true);
      if (this.headers.length) {
        this.headers.forEach((item) => {
          xhr.setRequestHeader(`${item.header}`, item.content);
        });
      }
      xhr.responseType = "blob";
      xhr.onreadystatechange = (e) => {
        if (xhr.status === 200 && xhr.readyState === XMLHttpRequest.DONE) {
          this.$refs["img"].src = URL.createObjectURL(xhr.response);
        }
      };
      xhr.send();
    },
  },
};
</script>
