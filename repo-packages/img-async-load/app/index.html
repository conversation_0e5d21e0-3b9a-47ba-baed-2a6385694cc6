<!DOCTYPE html>

<head>
    <!-- <script src="https://cdn.bootcss.com/babel-polyfill/6.23.0/polyfill.min.js"></script> -->
    <style>
        label {
            border: 1px solid;
            padding: 2px 5px;
            font-size: 12px;
        }
        
        a {
            font-size: 12px;
        }
    </style>
</head>

<body>
    <!--  -->
    <div id="app"></div>


    <!--  -->
    <button id="firstClick">第一种方法, 第一种全部兼容</button>
    <button id="secondClick">第二种方法, 第二种IE不兼容，但是方法写的挺好</button>
    <button id="clear">清空重来</button>
    <p>
        知识点： <label>window.customElements</label>

        <label>HTMLImageElement</label>

        <label>response.type = blob</label>

        <label>URL.createObjectURL</label>
        <label>URL.revokeObjectURL</label>
        <br/>
        <a href="https://www.cnblogs.com/China-Dream/p/13852758.html">customElements</a>
        <a href="https://www.w3cschool.cn/fetch_api/fetch_api-zilr2ng2.html">customElements</a>
        <a href="https://developer.mozilla.org/zh-CN/docs/Web/API/Window/customElements">customElements</a>

    </p>
</body>

</html>