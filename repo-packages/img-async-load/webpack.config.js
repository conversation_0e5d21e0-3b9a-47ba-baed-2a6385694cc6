//
const path = require('path')
    // const babelPolyfill = require('babel-polyfill')
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
var HtmlWebpackPlugin = require('html-webpack-plugin');
const vueLoaderPlugin = require('vue-loader/lib/plugin')

module.exports = {
    mode: process.env.ENV === 'development' ? 'development' : 'production',

    entry: process.env.ENV === 'development' ? path.resolve(__dirname, './app/app.js') : path.resolve(__dirname, './app/index.js'),
    output: {
        filename: "bundle.js",
        path: path.resolve(__dirname, './dist/'),
        libraryTarget: process.env.ENV === 'development' ? '' : 'umd'
    },
    module: {
        rules: [{
                test: /\.js$/,
                exclude: /(node_modules|bower_components)/,

                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env', {

                        }],
                        // plugins: ['@babel/plugin-transform-runtime']

                    }
                }
            },
            {
                test: /.vue$/,
                use: ['vue-loader']
            }
        ]
    },
    optimization: {
        minimize: true,
        minimizer: [
            // new UglifyJsPlugin({
            //     uglifyOptions: {
            //         // warnings: false,
            //         // parse: {},
            //         // compress: {},
            //         // mangle: true, // Note `mangle.properties` is `false` by default.
            //         // output: null,
            //         // toplevel: false,
            //         // nameCache: null,
            //         ie8: true,
            //         // keep_fnames: false,
            //     },
            // }),
        ],
    },
    devServer: {
        contentBase: path.join(__dirname, './app'),
        compress: true,
        port: 3333,
        hot: true
    },
    plugins: process.env.ENV === 'development' ? [new HtmlWebpackPlugin({
        template: path.resolve(__dirname, 'app/index.html'),
        filename: "index.html"
    }), new vueLoaderPlugin()] : [],
    // target: ['node']
    // target: ['web', 'es5']
    // runtimeCompiler: true
    resolve: {
        alias: {
            vue$: "vue/dist/vue.esm.js"
        }
    }
}