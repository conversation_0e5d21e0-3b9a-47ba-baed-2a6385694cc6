export = Watermark
declare class Watermark {
  /**
   * @param {WatermarkOptions} [options]
   */
  constructor(options?: WatermarkOptions)
  /**
   * 加载水印
   * @returns {Promise<void>}
   */
  load(): Promise<void>
  /**
   * 更新水印
   * 用户体验处不让改样式所以先注释了
   * @param {WatermarkOptions} options
   * @returns {Promise<void>}
   */
  update(options: WatermarkOptions): Promise<void>
  /**
   * 更新/获取配置
   * @param {WatermarkOptions} [options]
   * @return {WatermarkOptions}
   */
  options(options?: WatermarkOptions): WatermarkOptions
  /**
   * 移除水印
   */
  remove(): void
  #private
}
declare namespace Watermark {
  export { WatermarkOptions }
}
type WatermarkOptions = {
  /**
   * 水印的内容
   */
  text?: string | string[]
  /**
   * 水印字体
   */
  font?: string
  /**
   * 水印字体大小
   */
  fontSize?: string
  /**
   * 水印字体粗细
   */
  fontWeight?: number | string
  /**
   * 水印字体对齐方式
   */
  textAlign?: 'left' | 'center' | 'right'
  /**
   * 水印字体颜色
   */
  color?: string
  /**
   * 水印字体行高
   */
  lineHeight?: string
  /**
   * 水印透明度
   */
  alpha?: number
  /**
   * 水印间距 "16px [16px]"
   */
  padding?: string
  /**
   * 水印倾斜度数
   */
  angle?: number
  /**
   * 层级大小
   */
  zIndex?: number
  /**
   * 水印插件挂载的父元素elementId,不输入则默认挂在body上
   */
  container?: string | null
  /**
   * 水印是否全屏，默认false
   */
  fullscreen?: boolean
  /**
   * 不使用明水印，默认false
   */
  noLight?: boolean
  /**
   * 是否开启暗水印，默认为true
   */
  useBlind?: boolean
  /**
   * 暗水印是否全屏，默认true
   */
  blindFullscreen?: boolean
  /**
   * 暗水印的内容，默认使用明水印的内容
   */
  blindText?: string | string[]
  /**
   * 暗水印字体，默认使用明水印的字体
   */
  blindFont?: string
  /**
   * 暗水印的字体颜色，默认为 #000
   */
  blindColor?: string | string[]
  /**
   * 暗水印字体大小，默认使用明水印字体大小
   */
  blindFontSize?: string
  /**
   * 暗水印字体粗细，默认使用明水印字体粗细
   */
  blindFontWeight?: number | string
  /**
   * 暗水印字体行高，默认使用明水印字体行高
   */
  blindLineHeight?: string
  /**
   * 暗水印字体对齐方式，默认使用明水印字体对齐方式
   */
  blindTextAlign?: 'left' | 'center' | 'right'
  /**
   * 暗水印的透明度，默认0.015，需要比较小的数值
   */
  blindAlpha?: string | string[]
  /**
   * 暗水印倾斜度数，默认使用明水印倾斜度数
   */
  blindAngle?: number
  /**
   * 暗水印间距， 默认使用明水印间距
   */
  blindPadding?: string
  /**
   * 暗水印插件挂载的父元素elementId,不输入则默认挂在body上
   */
  blindContainer?: string | null
  /**
   * 是否使用shadowDom
   */
  shadowDom?: boolean
  /**
   * monitor 是否监控， true: 不可删除水印; false: 可删水印
   */
  monitor?: boolean
  /**
   * dirtyCheck 是否开启脏检查（monitor必须为true才生效）， true: 检测到水印节点被复制会销毁复制的水印节点所在节点; false: 不检测
   */
  dirtyCheck?: boolean
  /**
   * 水印图片，传入后不再使用canvas渲染
   */
  image?: string | null
  /**
   * 是否强制使用dom模式，为true是强制使用dom，为false时会判断浏览器ie使用dom模式，（浏览器打印时会出现没有背景色问题添加的该属性）
   */
  isForceDom?: boolean
}
