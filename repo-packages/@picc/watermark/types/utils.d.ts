export type ComputeSizeOptions = {
  text: string
  font: string
  fontSize: string
  lineHeight?: string
}
export type ComputeSizeResult = {
  width: number
  height: number
}
export type WatermarkCanvasOptions = {
  /**
   * 水印文字
   */
  text: string
  /**
   * 水印字体
   */
  font: string
  /**
   * 水印字体颜色
   */
  color: string
  /**
   * 水印字体大小
   */
  fontSize: string
  /**
   * 水印字体粗细
   */
  fontWeight: string
  /**
   * 水印字体行高
   */
  lineHeight: string
  /**
   * 水印字体对齐方式
   */
  textAlign: string
  /**
   * 水印透明度
   */
  alpha: number
  /**
   * 水印倾斜度数
   */
  angle: number
  /**
   * 水印间距
   */
  padding: string
  /**
   * 比例
   */
  ratio?: number
}
/**
 * @typedef {Object} WatermarkCanvasOptions
 * @property {string} text 水印文字
 * @property {string} font 水印字体
 * @property {string} color 水印字体颜色
 * @property {string} fontSize 水印字体大小
 * @property {string} fontWeight 水印字体粗细
 * @property {string} lineHeight 水印字体行高
 * @property {string} textAlign 水印字体对齐方式
 * @property {number} alpha 水印透明度
 * @property {number} angle 水印倾斜度数
 * @property {string} padding 水印间距
 * @property {number} [ratio] 比例
 */
/**
 * 创建水印Canvas
 * @param {WatermarkCanvasOptions} options
 * @return {HTMLCanvasElement}
 */
export function createWatermarkCanvas(options: WatermarkCanvasOptions): HTMLCanvasElement
/**
 * @typedef {Object} ComputeSizeOptions
 * @property {string} text
 * @property {string} font
 * @property {string} fontSize
 * @property {string} [lineHeight]
 */
/**
 * @typedef {Object} ComputeSizeResult
 * @property {number} width
 * @property {number} height
 */
/**
 * 计算字体大小
 * @param {ComputeSizeOptions} options
 * @return {ComputeSizeResult}
 */
export function computeFontSize(options: ComputeSizeOptions): ComputeSizeResult
/**
 * canvas转BlobUrl
 * @param {HTMLCanvasElement} canvas
 * @return {Promise<string>}
 */
export function canvas2Url(canvas: HTMLCanvasElement): Promise<string>
/**
 * 生成UUID
 * @return {string}
 */
export function uuid(): string
/**
 * 字符串Hash
 * @param {string} input
 * @return {string}
 */
export function hash(input: string): string
/**
 * 防抖
 * @param func
 * @param wait
 * @param immediate 默认false, true:防抖方法会在等待时间前执行 false:防抖方法会在等待时间后执行
 * @returns {function}
 */
export function debounce(func: any, wait: any, immediate?: boolean): Function
