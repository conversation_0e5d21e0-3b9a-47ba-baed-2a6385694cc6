# PICC Watermark

## 兼容性

> IE >= 9

## IE环境使用

由于组件使用了部分`ES6`语法特性，对于IE浏览器部分语法需要通过 `polyfill` 进行扩充，对于 `Vue` 框架项目，无需关心此问题，`cli` 会帮我们完成这部分操作，对于其他传统项目，需要在使用组件前添加 `polyfill.js` 文件**或者**使用 `legacy` 版本

[polyfill文件下载地址](http://**********:8082/#browse/browse:npm-all:babel-polyfill)

## 低版本IE浏览器表现说明

IE 10 及以下版本由于浏览器性能限制：

- 无法监听水印被修改
- 水印文字能够被用户选中

## 安装与使用

### 使用npm

1. 安装

    ```shell
    npm install @picc/watermark --save
    ```

2. 使用

    ```javascript
    import Watermark from '@picc/watermark';

    const wm = new Watermark({ ... });
    // 加载水印
    wm.load()
    // 移除水印
    wm.remove()
    // 更新水印
    wm.update({ ... })
    ```

### 直接引入

1. 下载代码

    [下载地址](http://**********:8082/#browse/search/npm=group%3D%40picc%20AND%20name.raw%3Dwatermark:c0ac2ab6c5e93a4a467e458afc96732a)

2. 引入

    ```html
    <script src="{path_to_folder}/index.js"></script>
    <script>
    const wm = new Watermark({ ... });
    // 加载水印
    wm.load()
    // 移除水印
    wm.remove()
    // 更新水印
    wm.update({ ... })
    </script>
    ```

## 参数

| 参数 | 说明 | 类型 | 默认值 |
| ------ | ------ | ------ | ------ |
| text | 水印的内容(多行使用字符串数组) | string &#124; string[] | '测试水印' |
| font | 水印字体 | string | 'Microsoft YaHei',Arial,SimHei,SimSun,sans-serif |
| fontSize | 水印字体大小 | string | '16px' |
| fontWeight | 水印字体粗细 | string | 'normal' |
| textAlign | 水印字体对齐方式 | 'left' &#124; 'center' &#124; 'right' | 'center' |
| color | 水印字体颜色 | string | '#000' |
| lineHeight | 水印字体行高 | string | '1.4' |
| alpha | 水印透明度 | number | 0.15 |
| padding | 水印间距('<上下左右>' 或 '<上下> <左右>') | string | '32px' |
| angle | 水印倾斜度数 | number | -22 |
| zIndex | 层级大小 | number | 99999 |
| container | 水印挂载的父元素elementId，默认挂载到body | string | null |
| fullscreen | 水印是否全屏（若为false,父元素不为body，会根据父元素宽高调整大小，若设为true，则全屏覆盖） | boolean | false |
| noLight | 不使用明水印 | boolean | false |
| useBlind | 使用暗水印 | boolean | true |
| blindFullscreen | 暗水印是否全屏（若为false,父元素不为body，会根据父元素宽高调整大小，若设为true，则全屏覆盖） | boolean | true |
| blindText | 暗水印的内容 | string | 水印的内容 |
| blindFont | 暗水印字体 | string | 水印字体 |
| blindColor | 暗水印字体颜色 | string | '#000' |
| blindFontSize | 暗水印字体大小 | string | 水印字体大小 |
| blindFontWeight | 暗水印字体粗细 | string | 水印字体粗细 |
| blindLineHeight | 暗水印字体行高 | string | 水印字体行高 |
| blindTextAlign | 暗水印字体对齐方式 | 'left' &#124; 'center' &#124; 'right' | 水印字体对齐方式 |
| blindAlpha | 暗水印透明度 | number | 0.008 |
| blindAngle | 暗水印倾斜度数 | number | 水印倾斜度数 |
| blindPadding | 暗水印间距('<上下左右>' 或 '<上下> <左右>') | string | 水印间距 |
| blindContainer | 暗水印挂载的父元素elementId，默认挂载到body | string | null |
| monitor | 是否监控， true: 非API调用不可删除水印; false: 可删水印 | boolean | true |
| dirtyCheck | 是否开启脏检查（monitor必须为true才生效）， true: 检测到水印节点被复制会销毁复制的水印节点所在节点; false: 不检测 | boolean | false |
| shadowDom | 是否使用shadowDom | boolean | false |
| image | 是否使用水印图片 | string | null |

## 方法

| 方法      | 说明 | 参数 | 返回值 |
|---------| ------ | ------ | ------ |
| load    | 加载水印 | void | Promise<void> |
| remove  | 移除水印 | void | void |
| update  | 更新水印 | WatermarkOptions | Promise<void> |
| options | 更新水印 | [WatermarkOptions] | WatermarkOptions |
