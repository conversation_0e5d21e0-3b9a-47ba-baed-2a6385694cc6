
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-Tools-add-nor {
  background-position: -300px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-add-black {
  background-position: -472px -192px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-add.disabled {
  background-position: -472px -224px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-add-o-sm-green {
  background-position: -472px -284px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-add-o-sm {
  background-position: -492px -224px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-add-o {
  background-position: -312px -196px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-add-sm {
  background-position: -508px -480px;
  width: 10px;
  height: 10px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-add {
  background-position: -472px -344px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-alipay {
  background-position: -360px -196px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-angle-double-down {
  background-position: -388px -244px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-angle-double-up {
  background-position: -384px -288px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-down {
  background-position: -412px -372px;
  width: 8px;
  height: 12px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-left-o.disabled {
  background-position: -472px -252px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-left-o:not(.disabled):hover {
  background-position: -472px -312px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-left-o {
  background-position: -356px -244px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-left-sm.disabled {
  background-position: -384px -308px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-left-sm:not(.disabled):hover {
  background-position: -384px -328px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-left-sm {
  background-position: -384px -348px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-right-o.disabled {
  background-position: -336px -384px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-right-o:not(.disabled):hover {
  background-position: -368px -384px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-right-o {
  background-position: -400px -384px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-right-sm.disabled {
  background-position: 0px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-right-sm:not(.disabled):hover {
  background-position: -20px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-right-sm {
  background-position: -40px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-arrow-up {
  background-position: -420px -372px;
  width: 8px;
  height: 12px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-asterisk {
  background-position: -60px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-batch.disabled {
  background-position: -312px -100px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-batch.selected {
  background-position: -156px -192px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-batch {
  background-position: 0px -288px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-bell {
  background-position: -80px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-calendar.disabled {
  background-position: -100px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-calendar:not(.disabled):hover {
  background-position: -120px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-calendar {
  background-position: -140px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-cards {
  background-position: -252px -192px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-circle-o {
  background-position: -160px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-close {
  background-position: -180px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-data-collection:not(.disabled):hover {
  background-position: -200px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-data-collection {
  background-position: -220px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-delete.disabled {
  background-position: -240px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-delete:not(.disabled):hover {
  background-position: -260px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-delete {
  background-position: -280px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-document-blank {
  background-position: -312px 0px;
  width: 100px;
  height: 100px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-document {
  background-position: -252px -240px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-download.disabled {
  background-position: -300px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-download:not(.disabled):hover {
  background-position: -320px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-download {
  background-position: -340px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-dropdown.disabled {
  background-position: -360px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-dropdown-group-down {
  background-position: -380px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-dropdown-group-up {
  background-position: -400px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-dropdown:not(.disabled):hover {
  background-position: -420px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-dropdown.selected {
  background-position: -440px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-dropdown {
  background-position: -460px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-edit.disabled {
  background-position: -480px -432px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-edit:not(.disabled):hover {
  background-position: 0px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-edit {
  background-position: -20px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-equal-black {
  background-position: -432px -384px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-error-sm {
  background-position: -40px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-error {
  background-position: -412px -192px;
  width: 60px;
  height: 60px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-excel.disabled {
  background-position: -60px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-excel:not(.disabled):hover {
  background-position: -80px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-excel {
  background-position: -100px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-file {
  background-position: -120px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-group.disabled {
  background-position: -96px -288px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-group.selected {
  background-position: -192px -288px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-group {
  background-position: -288px -288px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-heart.selected {
  background-position: -140px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-heart {
  background-position: -160px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-image {
  background-position: 0px -384px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-info-sm {
  background-position: -180px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-info {
  background-position: -200px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-link {
  background-position: -220px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-list:not(.disabled):hover {
  background-position: -240px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-list-o:not(.disabled):hover {
  background-position: -260px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-list-o {
  background-position: -280px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-list {
  background-position: -300px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-loading-mini {
  background-position: -492px -284px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-loading-sm {
  background-position: -492px -344px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-loading {
  background-position: -48px -384px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-message:not(.disabled):hover {
  background-position: -320px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-message {
  background-position: -340px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-minus-black {
  background-position: -464px -384px;
  width: 32px;
  height: 32px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-minus-plain.disabled {
  background-position: -360px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-minus-plain {
  background-position: -380px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-new {
  background-position: -312px -244px;
  width: 44px;
  height: 44px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-operate-switchcard:not(.disabled):hover {
  background-position: -400px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-operate-switchcard {
  background-position: -420px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-payment {
  background-position: -96px -384px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-phone-sm {
  background-position: -440px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-prosess.disabled {
  background-position: -460px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-prosess:not(.disabled):hover {
  background-position: -480px -452px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-prosess {
  background-position: 0px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-reject-mini {
  background-position: -20px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-reject-plain {
  background-position: -40px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-reject-sm-plain {
  background-position: -336px -416px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-reject-sm {
  background-position: -352px -416px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-remove-sm {
  background-position: -60px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-remove {
  background-position: -144px -384px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-search:not(.disabled):hover {
  background-position: -80px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-search {
  background-position: -100px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-select:not(.disabled):hover {
  background-position: -120px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-select-plain {
  background-position: -140px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-select {
  background-position: -160px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-settlement-icon {
  background-position: -180px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-single.selected {
  background-position: -412px 0px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-single {
  background-position: -412px -96px;
  width: 96px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-state-pop-loading {
  background-position: -200px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-success-md {
  background-position: -192px -384px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-success-mini {
  background-position: -220px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-success-sm.disabled {
  background-position: -240px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-success-sm {
  background-position: -260px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-success-square-plain {
  background-position: -384px -368px;
  width: 17px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-success-square {
  background-position: -368px -416px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-success {
  background-position: -412px -252px;
  width: 60px;
  height: 60px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-switch-vertical {
  background-position: -280px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-add {
  background-position: -320px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-calculator:not(.disabled):hover {
  background-position: -340px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-calculator {
  background-position: -360px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-consultation:not(.disabled):hover {
  background-position: -380px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-consultation {
  background-position: -400px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-database:not(.disabled):hover {
  background-position: -420px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-database {
  background-position: -440px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-flowchart:not(.disabled):hover {
  background-position: -460px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-flowchart {
  background-position: -480px -472px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-guestbook:not(.disabled):hover {
  background-position: -508px 0px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-guestbook {
  background-position: -508px -20px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-imagedata:not(.disabled):hover {
  background-position: -508px -40px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-imagedata {
  background-position: -508px -60px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-information:not(.disabled):hover {
  background-position: -508px -80px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-information {
  background-position: -508px -100px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-journal:not(.disabled):hover {
  background-position: -508px -120px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-journal {
  background-position: -508px -140px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-ocr:not(.disabled):hover {
  background-position: -508px -160px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-ocr {
  background-position: -508px -180px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-preview:not(.disabled):hover {
  background-position: -508px -200px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-preview {
  background-position: -508px -220px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-printer:not(.disabled):hover {
  background-position: -508px -240px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-printer {
  background-position: -508px -260px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-upload:not(.disabled):hover {
  background-position: -508px -280px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-tools-upload {
  background-position: -508px -300px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-transfer {
  background-position: -508px -320px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-bg-doc {
  background-position: 0px 0px;
  width: 156px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-bg-identity {
  background-position: -156px 0px;
  width: 156px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-bg-indentity-o {
  background-position: 0px -96px;
  width: 156px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-bg-video {
  background-position: -156px -96px;
  width: 156px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-card {
  background-position: 0px -192px;
  width: 156px;
  height: 96px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload.disabled {
  background-position: -508px -340px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload:not(.disabled):hover {
  background-position: -508px -360px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-o.disabled {
  background-position: -508px -380px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-o:not(.disabled):hover {
  background-position: -508px -400px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload-o {
  background-position: -508px -420px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-upload {
  background-position: -508px -440px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-video {
  background-position: -240px -384px;
  width: 48px;
  height: 48px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-waiting-plain {
  background-position: -508px -460px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-waiting-sm-plain {
  background-position: -384px -416px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-waiting-sm {
  background-position: -400px -416px;
  width: 16px;
  height: 16px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-waiting {
  background-position: 0px -492px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-warning-mini-red {
  background-position: -20px -492px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-warning-mini {
  background-position: -40px -492px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-warning-sm {
  background-position: -60px -492px;
  width: 20px;
  height: 20px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-warning {
  background-position: -412px -312px;
  width: 60px;
  height: 60px;
}
.picc-icon {
    background-image: url(picc-sprite.png);
    display: inline-block;
}
.picc-icon.picc-icon-wechat {
  background-position: -288px -384px;
  width: 48px;
  height: 48px;
}
