<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta charset="utf-8"/>
		<meta http-equiv="X-UA-Compatible" content="IE=Edge"/>
		<title>SVG &lt;view&gt; sprite preview | svg-sprite</title>
		<style>@charset "UTF-8";body{padding:0;margin:0;color:#666;background:#fafafa;font-family:Arial,Helvetica,sans-serif;font-size:1em;line-height:1.4}header{display:block;padding:3em 3em 2em 3em;background-color:#fff}header p{margin:2em 0 0 0}section{border-top:1px solid #eee;padding:2em 3em 0 3em}section ul{margin:0;padding:0}section li{display:inline;display:inline-block;background-color:#fff;position:relative;margin:0 2em 2em 0;vertical-align:top;border:1px solid #ccc;padding:1em 1em 3em 1em;cursor:default}.icon-box{margin:0;width:144px;height:144px;position:relative;display:table-cell;vertical-align:middle;text-align:center}.icon{display:inline;display:inline-block}h1{margin-top:0}h2{margin:0;padding:0;font-size:1em;font-weight:normal;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;position:absolute;left:1em;right:1em;bottom:1em}footer{display:block;margin:0;padding:0 3em 3em 3em}footer p{margin:0;font-size:.7em}footer a{color:#0f7595;margin-left:0}</style>
		<style>i{text-indent:200%;white-space:nowrap;overflow:hidden;display:inline-block}</style>

<!--
	
Sprite CSS
====================================================================================================
This is an all-in-one inline version of the CSS necessary to use the SVG sprite. 

-->

<style type="text/css">
.picc-op--add {
	background: url("picc-svgsprite.svg") 95.2755905511811% 39.02439024390244% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--add-disable {
	background: url("picc-svgsprite.svg") 92.91338582677166% 51.21951219512195% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--close {
	background: url("picc-svgsprite.svg") 92.91338582677166% 63.41463414634146% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--collect-nor {
	background: url("picc-svgsprite.svg") 81.10236220472441% 72.5609756097561% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--collect-selected {
	background: url("picc-svgsprite.svg") 85.03937007874016% 72.5609756097561% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--data-collect-hover {
	background: url("picc-svgsprite.svg") 88.97637795275591% 72.5609756097561% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--data-collect-nor {
	background: url("picc-svgsprite.svg") 92.91338582677166% 72.5609756097561% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--delete-disable {
	background: url("picc-svgsprite.svg") 73.22834645669292% 46.34146341463415% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--delete-hover {
	background: url("picc-svgsprite.svg") 77.16535433070867% 46.34146341463415% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--delete-nor {
	background: url("picc-svgsprite.svg") 75.59055118110236% 58.53658536585366% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--doc-hover {
	background: url("picc-svgsprite.svg") 75.59055118110236% 62.60162601626016% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--doc-nor {
	background: url("picc-svgsprite.svg") 75.59055118110236% 66.66666666666667% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--down-db {
	background: url("picc-svgsprite.svg") 75.59055118110236% 70.73170731707317% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--excel-disable {
	background: url("picc-svgsprite.svg") 66.14173228346456% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--excel-hover {
	background: url("picc-svgsprite.svg") 70.07874015748031% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--excel-nor {
	background: url("picc-svgsprite.svg") 74.01574803149606% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--filter-down {
	background: url("picc-svgsprite.svg") 77.95275590551181% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--filter-up {
	background: url("picc-svgsprite.svg") 81.88976377952756% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--flow-hover {
	background: url("picc-svgsprite.svg") 85.8267716535433% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--flow-nor {
	background: url("picc-svgsprite.svg") 89.76377952755905% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--input-delete {
	background: url("picc-svgsprite.svg") 93.7007874015748% 78.04878048780488% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--input-delete-nor {
	background: url("picc-svgsprite.svg") 66.14173228346456% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--left-disable {
	background: url("picc-svgsprite.svg") 70.07874015748031% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--left-hover {
	background: url("picc-svgsprite.svg") 74.01574803149606% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--left-nor {
	background: url("picc-svgsprite.svg") 77.95275590551181% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--left-o-disable {
	background: url("picc-svgsprite.svg") 95.16129032258064% 45.833333333333336% no-repeat;
	width: 32px;
	height: 32px;
}
.picc-op--left-o-hover {
	background: url("picc-svgsprite.svg") 95.16129032258064% 58.333333333333336% no-repeat;
	width: 32px;
	height: 32px;
}
.picc-op--left-o-nor {
	background: url("picc-svgsprite.svg") 75% 40.833333333333336% no-repeat;
	width: 32px;
	height: 32px;
}
.picc-op--list-hover {
	background: url("picc-svgsprite.svg") 81.88976377952756% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--list-nor {
	background: url("picc-svgsprite.svg") 85.8267716535433% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--loading-blue {
	background: url("picc-svgsprite.svg") 96.09375% 50.806451612903224% no-repeat;
	width: 16px;
	height: 16px;
}
.picc-op--loading-white {
	background: url("picc-svgsprite.svg") 96.09375% 62.903225806451616% no-repeat;
	width: 16px;
	height: 16px;
}
.picc-op--log-hover {
	background: url("picc-svgsprite.svg") 89.76377952755905% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--log-nor {
	background: url("picc-svgsprite.svg") 93.7007874015748% 82.11382113821138% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--msg {
	background: url("picc-svgsprite.svg") 0 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--msg-hover {
	background: url("picc-svgsprite.svg") 3.937007874015748% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--phone {
	background: url("picc-svgsprite.svg") 7.874015748031496% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--prosess-disable {
	background: url("picc-svgsprite.svg") 11.811023622047244% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--prosess-hover {
	background: url("picc-svgsprite.svg") 15.748031496062993% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--prosess-nor {
	background: url("picc-svgsprite.svg") 19.68503937007874% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--right-disable {
	background: url("picc-svgsprite.svg") 23.62204724409449% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--right-hover {
	background: url("picc-svgsprite.svg") 27.559055118110237% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--right-nor {
	background: url("picc-svgsprite.svg") 31.496062992125985% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--right-o-disable {
	background: url("picc-svgsprite.svg") 62.903225806451616% 53.333333333333336% no-repeat;
	width: 32px;
	height: 32px;
}
.picc-op--right-o-hover {
	background: url("picc-svgsprite.svg") 69.35483870967742% 53.333333333333336% no-repeat;
	width: 32px;
	height: 32px;
}
.picc-op--right-o-nor {
	background: url("picc-svgsprite.svg") 75.80645161290323% 53.333333333333336% no-repeat;
	width: 32px;
	height: 32px;
}
.picc-op--search {
	background: url("picc-svgsprite.svg") 35.43307086614173% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--search-hover {
	background: url("picc-svgsprite.svg") 39.37007874015748% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--up-db {
	background: url("picc-svgsprite.svg") 43.30708661417323% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--upload-disable {
	background: url("picc-svgsprite.svg") 47.24409448818898% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--upload-hover {
	background: url("picc-svgsprite.svg") 51.181102362204726% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-op--upload-nor {
	background: url("picc-svgsprite.svg") 55.118110236220474% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-others--alipay {
	background: url("picc-svgsprite.svg") 52.5% 41.37931034482759% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-others--card-id-back {
	background: url("picc-svgsprite.svg") 0 0 no-repeat;
	width: 156px;
	height: 96px;
}
.picc-others--card-id-front {
	background: url("picc-svgsprite.svg") 41.935483870967744% 0 no-repeat;
	width: 156px;
	height: 96px;
}
.picc-others--card-pl {
	background: url("picc-svgsprite.svg") 0 23.076923076923077% no-repeat;
	width: 156px;
	height: 96px;
}
.picc-others--cards {
	background: url("picc-svgsprite.svg") 52.5% 51.724137931034484% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-others--empty-search {
	background: url("picc-svgsprite.svg") 72.89719626168224% 0 no-repeat;
	width: 100px;
	height: 100px;
}
.picc-others--file {
	background: url("picc-svgsprite.svg") 0 82.75862068965517% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-others--payment {
	background: url("picc-svgsprite.svg") 10% 82.75862068965517% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-others--photo {
	background: url("picc-svgsprite.svg") 20% 82.75862068965517% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-others--upload-doc {
	background: url("picc-svgsprite.svg") 41.935483870967744% 23.076923076923077% no-repeat;
	width: 156px;
	height: 96px;
}
.picc-others--upload-video {
	background: url("picc-svgsprite.svg") 0 46.15384615384615% no-repeat;
	width: 156px;
	height: 96px;
}
.picc-others--wechat {
	background: url("picc-svgsprite.svg") 30% 82.75862068965517% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-sidebar--customer {
	background: url("picc-svgsprite.svg") 59.05511811023622% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--customer-hover {
	background: url("picc-svgsprite.svg") 62.99212598425197% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--customer-selected {
	background: url("picc-svgsprite.svg") 66.92913385826772% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--data {
	background: url("picc-svgsprite.svg") 70.86614173228347% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--data-hover {
	background: url("picc-svgsprite.svg") 74.80314960629921% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--data-selected {
	background: url("picc-svgsprite.svg") 78.74015748031496% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--home {
	background: url("picc-svgsprite.svg") 82.67716535433071% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--home-hover {
	background: url("picc-svgsprite.svg") 86.61417322834646% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--home-selected {
	background: url("picc-svgsprite.svg") 90.55118110236221% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--log {
	background: url("picc-svgsprite.svg") 94.48818897637796% 87.8048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--log-hover {
	background: url("picc-svgsprite.svg") 0 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--log-selected {
	background: url("picc-svgsprite.svg") 3.937007874015748% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--logo {
	background: url("picc-svgsprite.svg") 90.35087719298245% 39.66942148760331% no-repeat;
	width: 72px;
	height: 28px;
}
.picc-sidebar--logo-sm {
	background: url("picc-svgsprite.svg") 85.12396694214875% 68.68686868686869% no-repeat;
	width: 44px;
	height: 17px;
}
.picc-sidebar--task {
	background: url("picc-svgsprite.svg") 7.874015748031496% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--task-hover {
	background: url("picc-svgsprite.svg") 11.811023622047244% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--task-selected {
	background: url("picc-svgsprite.svg") 15.748031496062993% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--tools {
	background: url("picc-svgsprite.svg") 19.68503937007874% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--tools-hover {
	background: url("picc-svgsprite.svg") 23.62204724409449% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-sidebar--tools-selected {
	background: url("picc-svgsprite.svg") 27.559055118110237% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--add {
	background: url("picc-svgsprite.svg") 89.0625% 68.54838709677419% no-repeat;
	width: 16px;
	height: 16px;
}
.picc-status--add-large {
	background: url("picc-svgsprite.svg") 40% 82.75862068965517% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-status--claim-check {
	background: url("picc-svgsprite.svg") 31.496062992125985% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--claim-complete {
	background: url("picc-svgsprite.svg") 35.43307086614173% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--claim-refund {
	background: url("picc-svgsprite.svg") 39.37007874015748% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--failure {
	background: url("picc-svgsprite.svg") 43.30708661417323% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--loading {
	background: url("picc-svgsprite.svg") 50% 82.75862068965517% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-status--notice-remind {
	background: url("picc-svgsprite.svg") 47.24409448818898% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--notice-succeed {
	background: url("picc-svgsprite.svg") 51.181102362204726% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--notice-warning {
	background: url("picc-svgsprite.svg") 55.118110236220474% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--p-batch {
	background: url("picc-svgsprite.svg") 72.22222222222223% 24.03846153846154% no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--p-batch-disable {
	background: url("picc-svgsprite.svg") 36.111111111111114% 46.15384615384615% no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--p-batch-seleceted {
	background: url("picc-svgsprite.svg") 0 69.23076923076923% no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--p-group {
	background: url("picc-svgsprite.svg") 22.22222222222222% 69.23076923076923% no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--p-group-disable {
	background: url("picc-svgsprite.svg") 44.44444444444444% 69.23076923076923% no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--p-group-selected {
	background: url("picc-svgsprite.svg") 66.66666666666667% 69.23076923076923% no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--p-single {
	background: url("picc-svgsprite.svg") 95.37037037037037% 0 no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--p-single-selected {
	background: url("picc-svgsprite.svg") 95.37037037037037% 23.076923076923077% no-repeat;
	width: 96px;
	height: 96px;
}
.picc-status--pop-loading {
	background: url("picc-svgsprite.svg") 59.05511811023622% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--pop-notice {
	background: url("picc-svgsprite.svg") 62.99212598425197% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--pop-succeed {
	background: url("picc-svgsprite.svg") 66.92913385826772% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--pop-warning {
	background: url("picc-svgsprite.svg") 70.86614173228347% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--required {
	background: url("picc-svgsprite.svg") 74.80314960629921% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--result-failure {
	background: url("picc-svgsprite.svg") 88.03418803418803% 48.67256637168141% no-repeat;
	width: 60px;
	height: 60px;
}
.picc-status--result-succeed {
	background: url("picc-svgsprite.svg") 88.03418803418803% 61.94690265486726% no-repeat;
	width: 60px;
	height: 60px;
}
.picc-status--result-warning {
	background: url("picc-svgsprite.svg") 66.66666666666667% 43.36283185840708% no-repeat;
	width: 60px;
	height: 60px;
}
.picc-status--step-o {
	background: url("picc-svgsprite.svg") 78.74015748031496% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--step-refund {
	background: url("picc-svgsprite.svg") 82.67716535433071% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--step-succeed {
	background: url("picc-svgsprite.svg") 86.61417322834646% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--step-succeed-dis {
	background: url("picc-svgsprite.svg") 90.55118110236221% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--success-large {
	background: url("picc-svgsprite.svg") 60% 82.75862068965517% no-repeat;
	width: 48px;
	height: 48px;
}
.picc-status--toast-failure {
	background: url("picc-svgsprite.svg") 94.48818897637796% 91.869918699187% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--toast-succeed {
	background: url("picc-svgsprite.svg") 0 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-status--toast-warning {
	background: url("picc-svgsprite.svg") 3.937007874015748% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--_claim-transfer {
	background: url("picc-svgsprite.svg") 7.874015748031496% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--arrow-down {
	background: url("picc-svgsprite.svg") 97.6923076923077% 96% no-repeat;
	width: 8px;
	height: 12px;
}
.picc-tip--arrow_up {
	background: url("picc-svgsprite.svg") 99.23076923076923% 96% no-repeat;
	width: 8px;
	height: 12px;
}
.picc-tip--calendar-disable {
	background: url("picc-svgsprite.svg") 11.811023622047244% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--calendar-hover {
	background: url("picc-svgsprite.svg") 15.748031496062993% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--calendar-nor {
	background: url("picc-svgsprite.svg") 19.68503937007874% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--claim-first {
	background: url("picc-svgsprite.svg") 23.62204724409449% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--claim-icon {
	background: url("picc-svgsprite.svg") 27.559055118110237% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--claim-switch {
	background: url("picc-svgsprite.svg") 31.496062992125985% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--download_disable {
	background: url("picc-svgsprite.svg") 35.43307086614173% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--download_hover {
	background: url("picc-svgsprite.svg") 39.37007874015748% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--download_nor {
	background: url("picc-svgsprite.svg") 43.30708661417323% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--edit-disable {
	background: url("picc-svgsprite.svg") 47.24409448818898% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--edit-hover {
	background: url("picc-svgsprite.svg") 51.181102362204726% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--edit-nor {
	background: url("picc-svgsprite.svg") 55.118110236220474% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--file {
	background: url("picc-svgsprite.svg") 59.05511811023622% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--information {
	background: url("picc-svgsprite.svg") 62.99212598425197% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--link {
	background: url("picc-svgsprite.svg") 66.92913385826772% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--triange-down-selected {
	background: url("picc-svgsprite.svg") 70.86614173228347% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--triangle-down-disable {
	background: url("picc-svgsprite.svg") 74.80314960629921% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--triangle-down-nor {
	background: url("picc-svgsprite.svg") 78.74015748031496% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--triangle-up {
	background: url("picc-svgsprite.svg") 82.67716535433071% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--upload-disable {
	background: url("picc-svgsprite.svg") 86.61417322834646% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--upload-hover {
	background: url("picc-svgsprite.svg") 90.55118110236221% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tip--upload-nor {
	background: url("picc-svgsprite.svg") 94.48818897637796% 95.9349593495935% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--add {
	background: url("picc-svgsprite.svg") 100% 0 no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--add-disable {
	background: url("picc-svgsprite.svg") 100% 4.065040650406504% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--add-o {
	background: url("picc-svgsprite.svg") 100% 8.130081300813009% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--calculator-hover {
	background: url("picc-svgsprite.svg") 100% 12.195121951219512% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--calculator-nor {
	background: url("picc-svgsprite.svg") 100% 16.260162601626018% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--consult-hover {
	background: url("picc-svgsprite.svg") 100% 20.32520325203252% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--consult-nor {
	background: url("picc-svgsprite.svg") 100% 24.390243902439025% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--delete {
	background: url("picc-svgsprite.svg") 100% 28.45528455284553% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--feedback-hover {
	background: url("picc-svgsprite.svg") 100% 32.520325203252035% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--feedback-nor {
	background: url("picc-svgsprite.svg") 100% 36.58536585365854% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--files-hover {
	background: url("picc-svgsprite.svg") 100% 40.65040650406504% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--files-nor {
	background: url("picc-svgsprite.svg") 100% 44.71544715447155% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--flows-hover {
	background: url("picc-svgsprite.svg") 100% 48.78048780487805% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--flows-nor {
	background: url("picc-svgsprite.svg") 100% 52.84552845528455% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--info-hover {
	background: url("picc-svgsprite.svg") 100% 56.91056910569106% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--info-nor {
	background: url("picc-svgsprite.svg") 100% 60.97560975609756% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--logs-hover {
	background: url("picc-svgsprite.svg") 100% 65.04065040650407% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--logs-nor {
	background: url("picc-svgsprite.svg") 100% 69.10569105691057% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--ocr-hover {
	background: url("picc-svgsprite.svg") 100% 73.17073170731707% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--ocr-nor {
	background: url("picc-svgsprite.svg") 100% 77.23577235772358% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--previdw-hover {
	background: url("picc-svgsprite.svg") 100% 81.30081300813008% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--preview-nor {
	background: url("picc-svgsprite.svg") 100% 85.36585365853658% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--printer-hover {
	background: url("picc-svgsprite.svg") 100% 89.4308943089431% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--printer-nor {
	background: url("picc-svgsprite.svg") 100% 93.4959349593496% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--upload-hover {
	background: url("picc-svgsprite.svg") 0 100% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--upload-nor {
	background: url("picc-svgsprite.svg") 3.937007874015748% 100% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--video-hover {
	background: url("picc-svgsprite.svg") 7.874015748031496% 100% no-repeat;
	width: 20px;
	height: 20px;
}
.picc-tools--video-nor {
	background: url("picc-svgsprite.svg") 11.811023622047244% 100% no-repeat;
	width: 20px;
	height: 20px;
}
</style>
		
<!--
	
Sprite shape dimensions
====================================================================================================
If you use the sprite in conjunction with the pre-defined views (respectively fragment identifiers),
you will need to set the shape dimensions via CSS unless you want them to become a huge 100% in
size. You may use the following CSS classes for doing so. They might well be outsourced to an
external stylesheet of course.

-->

<style type="text/css">
	.picc-op--add-dims { width: 20px; height: 20px; }
	.picc-op--add-disable-dims { width: 20px; height: 20px; }
	.picc-op--close-dims { width: 20px; height: 20px; }
	.picc-op--collect-nor-dims { width: 20px; height: 20px; }
	.picc-op--collect-selected-dims { width: 20px; height: 20px; }
	.picc-op--data-collect-hover-dims { width: 20px; height: 20px; }
	.picc-op--data-collect-nor-dims { width: 20px; height: 20px; }
	.picc-op--delete-disable-dims { width: 20px; height: 20px; }
	.picc-op--delete-hover-dims { width: 20px; height: 20px; }
	.picc-op--delete-nor-dims { width: 20px; height: 20px; }
	.picc-op--doc-hover-dims { width: 20px; height: 20px; }
	.picc-op--doc-nor-dims { width: 20px; height: 20px; }
	.picc-op--down-db-dims { width: 20px; height: 20px; }
	.picc-op--excel-disable-dims { width: 20px; height: 20px; }
	.picc-op--excel-hover-dims { width: 20px; height: 20px; }
	.picc-op--excel-nor-dims { width: 20px; height: 20px; }
	.picc-op--filter-down-dims { width: 20px; height: 20px; }
	.picc-op--filter-up-dims { width: 20px; height: 20px; }
	.picc-op--flow-hover-dims { width: 20px; height: 20px; }
	.picc-op--flow-nor-dims { width: 20px; height: 20px; }
	.picc-op--input-delete-dims { width: 20px; height: 20px; }
	.picc-op--input-delete-nor-dims { width: 20px; height: 20px; }
	.picc-op--left-disable-dims { width: 20px; height: 20px; }
	.picc-op--left-hover-dims { width: 20px; height: 20px; }
	.picc-op--left-nor-dims { width: 20px; height: 20px; }
	.picc-op--left-o-disable-dims { width: 32px; height: 32px; }
	.picc-op--left-o-hover-dims { width: 32px; height: 32px; }
	.picc-op--left-o-nor-dims { width: 32px; height: 32px; }
	.picc-op--list-hover-dims { width: 20px; height: 20px; }
	.picc-op--list-nor-dims { width: 20px; height: 20px; }
	.picc-op--loading-blue-dims { width: 16px; height: 16px; }
	.picc-op--loading-white-dims { width: 16px; height: 16px; }
	.picc-op--log-hover-dims { width: 20px; height: 20px; }
	.picc-op--log-nor-dims { width: 20px; height: 20px; }
	.picc-op--msg-dims { width: 20px; height: 20px; }
	.picc-op--msg-hover-dims { width: 20px; height: 20px; }
	.picc-op--phone-dims { width: 20px; height: 20px; }
	.picc-op--prosess-disable-dims { width: 20px; height: 20px; }
	.picc-op--prosess-hover-dims { width: 20px; height: 20px; }
	.picc-op--prosess-nor-dims { width: 20px; height: 20px; }
	.picc-op--right-disable-dims { width: 20px; height: 20px; }
	.picc-op--right-hover-dims { width: 20px; height: 20px; }
	.picc-op--right-nor-dims { width: 20px; height: 20px; }
	.picc-op--right-o-disable-dims { width: 32px; height: 32px; }
	.picc-op--right-o-hover-dims { width: 32px; height: 32px; }
	.picc-op--right-o-nor-dims { width: 32px; height: 32px; }
	.picc-op--search-dims { width: 20px; height: 20px; }
	.picc-op--search-hover-dims { width: 20px; height: 20px; }
	.picc-op--up-db-dims { width: 20px; height: 20px; }
	.picc-op--upload-disable-dims { width: 20px; height: 20px; }
	.picc-op--upload-hover-dims { width: 20px; height: 20px; }
	.picc-op--upload-nor-dims { width: 20px; height: 20px; }
	.picc-others--alipay-dims { width: 48px; height: 48px; }
	.picc-others--card-id-back-dims { width: 156px; height: 96px; }
	.picc-others--card-id-front-dims { width: 156px; height: 96px; }
	.picc-others--card-pl-dims { width: 156px; height: 96px; }
	.picc-others--cards-dims { width: 48px; height: 48px; }
	.picc-others--empty-search-dims { width: 100px; height: 100px; }
	.picc-others--file-dims { width: 48px; height: 48px; }
	.picc-others--payment-dims { width: 48px; height: 48px; }
	.picc-others--photo-dims { width: 48px; height: 48px; }
	.picc-others--upload-doc-dims { width: 156px; height: 96px; }
	.picc-others--upload-video-dims { width: 156px; height: 96px; }
	.picc-others--wechat-dims { width: 48px; height: 48px; }
	.picc-sidebar--customer-dims { width: 20px; height: 20px; }
	.picc-sidebar--customer-hover-dims { width: 20px; height: 20px; }
	.picc-sidebar--customer-selected-dims { width: 20px; height: 20px; }
	.picc-sidebar--data-dims { width: 20px; height: 20px; }
	.picc-sidebar--data-hover-dims { width: 20px; height: 20px; }
	.picc-sidebar--data-selected-dims { width: 20px; height: 20px; }
	.picc-sidebar--home-dims { width: 20px; height: 20px; }
	.picc-sidebar--home-hover-dims { width: 20px; height: 20px; }
	.picc-sidebar--home-selected-dims { width: 20px; height: 20px; }
	.picc-sidebar--log-dims { width: 20px; height: 20px; }
	.picc-sidebar--log-hover-dims { width: 20px; height: 20px; }
	.picc-sidebar--log-selected-dims { width: 20px; height: 20px; }
	.picc-sidebar--logo-dims { width: 72px; height: 28px; }
	.picc-sidebar--logo-sm-dims { width: 44px; height: 17px; }
	.picc-sidebar--task-dims { width: 20px; height: 20px; }
	.picc-sidebar--task-hover-dims { width: 20px; height: 20px; }
	.picc-sidebar--task-selected-dims { width: 20px; height: 20px; }
	.picc-sidebar--tools-dims { width: 20px; height: 20px; }
	.picc-sidebar--tools-hover-dims { width: 20px; height: 20px; }
	.picc-sidebar--tools-selected-dims { width: 20px; height: 20px; }
	.picc-status--add-dims { width: 16px; height: 16px; }
	.picc-status--add-large-dims { width: 48px; height: 48px; }
	.picc-status--claim-check-dims { width: 20px; height: 20px; }
	.picc-status--claim-complete-dims { width: 20px; height: 20px; }
	.picc-status--claim-refund-dims { width: 20px; height: 20px; }
	.picc-status--failure-dims { width: 20px; height: 20px; }
	.picc-status--loading-dims { width: 48px; height: 48px; }
	.picc-status--notice-remind-dims { width: 20px; height: 20px; }
	.picc-status--notice-succeed-dims { width: 20px; height: 20px; }
	.picc-status--notice-warning-dims { width: 20px; height: 20px; }
	.picc-status--p-batch-dims { width: 96px; height: 96px; }
	.picc-status--p-batch-disable-dims { width: 96px; height: 96px; }
	.picc-status--p-batch-seleceted-dims { width: 96px; height: 96px; }
	.picc-status--p-group-dims { width: 96px; height: 96px; }
	.picc-status--p-group-disable-dims { width: 96px; height: 96px; }
	.picc-status--p-group-selected-dims { width: 96px; height: 96px; }
	.picc-status--p-single-dims { width: 96px; height: 96px; }
	.picc-status--p-single-selected-dims { width: 96px; height: 96px; }
	.picc-status--pop-loading-dims { width: 20px; height: 20px; }
	.picc-status--pop-notice-dims { width: 20px; height: 20px; }
	.picc-status--pop-succeed-dims { width: 20px; height: 20px; }
	.picc-status--pop-warning-dims { width: 20px; height: 20px; }
	.picc-status--required-dims { width: 20px; height: 20px; }
	.picc-status--result-failure-dims { width: 60px; height: 60px; }
	.picc-status--result-succeed-dims { width: 60px; height: 60px; }
	.picc-status--result-warning-dims { width: 60px; height: 60px; }
	.picc-status--step-o-dims { width: 20px; height: 20px; }
	.picc-status--step-refund-dims { width: 20px; height: 20px; }
	.picc-status--step-succeed-dims { width: 20px; height: 20px; }
	.picc-status--step-succeed-dis-dims { width: 20px; height: 20px; }
	.picc-status--success-large-dims { width: 48px; height: 48px; }
	.picc-status--toast-failure-dims { width: 20px; height: 20px; }
	.picc-status--toast-succeed-dims { width: 20px; height: 20px; }
	.picc-status--toast-warning-dims { width: 20px; height: 20px; }
	.picc-tip--_claim-transfer-dims { width: 20px; height: 20px; }
	.picc-tip--arrow-down-dims { width: 8px; height: 12px; }
	.picc-tip--arrow_up-dims { width: 8px; height: 12px; }
	.picc-tip--calendar-disable-dims { width: 20px; height: 20px; }
	.picc-tip--calendar-hover-dims { width: 20px; height: 20px; }
	.picc-tip--calendar-nor-dims { width: 20px; height: 20px; }
	.picc-tip--claim-first-dims { width: 20px; height: 20px; }
	.picc-tip--claim-icon-dims { width: 20px; height: 20px; }
	.picc-tip--claim-switch-dims { width: 20px; height: 20px; }
	.picc-tip--download_disable-dims { width: 20px; height: 20px; }
	.picc-tip--download_hover-dims { width: 20px; height: 20px; }
	.picc-tip--download_nor-dims { width: 20px; height: 20px; }
	.picc-tip--edit-disable-dims { width: 20px; height: 20px; }
	.picc-tip--edit-hover-dims { width: 20px; height: 20px; }
	.picc-tip--edit-nor-dims { width: 20px; height: 20px; }
	.picc-tip--file-dims { width: 20px; height: 20px; }
	.picc-tip--information-dims { width: 20px; height: 20px; }
	.picc-tip--link-dims { width: 20px; height: 20px; }
	.picc-tip--triange-down-selected-dims { width: 20px; height: 20px; }
	.picc-tip--triangle-down-disable-dims { width: 20px; height: 20px; }
	.picc-tip--triangle-down-nor-dims { width: 20px; height: 20px; }
	.picc-tip--triangle-up-dims { width: 20px; height: 20px; }
	.picc-tip--upload-disable-dims { width: 20px; height: 20px; }
	.picc-tip--upload-hover-dims { width: 20px; height: 20px; }
	.picc-tip--upload-nor-dims { width: 20px; height: 20px; }
	.picc-tools--add-dims { width: 20px; height: 20px; }
	.picc-tools--add-disable-dims { width: 20px; height: 20px; }
	.picc-tools--add-o-dims { width: 20px; height: 20px; }
	.picc-tools--calculator-hover-dims { width: 20px; height: 20px; }
	.picc-tools--calculator-nor-dims { width: 20px; height: 20px; }
	.picc-tools--consult-hover-dims { width: 20px; height: 20px; }
	.picc-tools--consult-nor-dims { width: 20px; height: 20px; }
	.picc-tools--delete-dims { width: 20px; height: 20px; }
	.picc-tools--feedback-hover-dims { width: 20px; height: 20px; }
	.picc-tools--feedback-nor-dims { width: 20px; height: 20px; }
	.picc-tools--files-hover-dims { width: 20px; height: 20px; }
	.picc-tools--files-nor-dims { width: 20px; height: 20px; }
	.picc-tools--flows-hover-dims { width: 20px; height: 20px; }
	.picc-tools--flows-nor-dims { width: 20px; height: 20px; }
	.picc-tools--info-hover-dims { width: 20px; height: 20px; }
	.picc-tools--info-nor-dims { width: 20px; height: 20px; }
	.picc-tools--logs-hover-dims { width: 20px; height: 20px; }
	.picc-tools--logs-nor-dims { width: 20px; height: 20px; }
	.picc-tools--ocr-hover-dims { width: 20px; height: 20px; }
	.picc-tools--ocr-nor-dims { width: 20px; height: 20px; }
	.picc-tools--previdw-hover-dims { width: 20px; height: 20px; }
	.picc-tools--preview-nor-dims { width: 20px; height: 20px; }
	.picc-tools--printer-hover-dims { width: 20px; height: 20px; }
	.picc-tools--printer-nor-dims { width: 20px; height: 20px; }
	.picc-tools--upload-hover-dims { width: 20px; height: 20px; }
	.picc-tools--upload-nor-dims { width: 20px; height: 20px; }
	.picc-tools--video-hover-dims { width: 20px; height: 20px; }
	.picc-tools--video-nor-dims { width: 20px; height: 20px; }
</style>

<!--
====================================================================================================
-->

	</head>
	<body>
		<!-- <header>

		</header> -->
		<section>

<!--
	
A) Conventional CSS sprite
====================================================================================================
This technique uses CSS classes to display portions of the sprite as background image of
appropriately sized elements.

-->

			<h3>PICC 图标</h3>
			<ul>
				
				<li title="op--add">
					<div class="icon-box">
						
						<!-- op--add -->
						<i class="picc-op--add">op--add</i>
						
					</div>
					<h2 title="op--add">op--add</h2>
				</li>
				<li title="op--add-disable">
					<div class="icon-box">
						
						<!-- op--add-disable -->
						<i class="picc-op--add-disable">op--add-disable</i>
						
					</div>
					<h2 title="op--add-disable">op--add-disable</h2>
				</li>
				<li title="op--close">
					<div class="icon-box">
						
						<!-- op--close -->
						<i class="picc-op--close">op--close</i>
						
					</div>
					<h2 title="op--close">op--close</h2>
				</li>
				<li title="op--collect-nor">
					<div class="icon-box">
						
						<!-- op--collect-nor -->
						<i class="picc-op--collect-nor">op--collect-nor</i>
						
					</div>
					<h2 title="op--collect-nor">op--collect-nor</h2>
				</li>
				<li title="op--collect-selected">
					<div class="icon-box">
						
						<!-- op--collect-selected -->
						<i class="picc-op--collect-selected">op--collect-selected</i>
						
					</div>
					<h2 title="op--collect-selected">op--collect-selected</h2>
				</li>
				<li title="op--data-collect-hover">
					<div class="icon-box">
						
						<!-- op--data-collect-hover -->
						<i class="picc-op--data-collect-hover">op--data-collect-hover</i>
						
					</div>
					<h2 title="op--data-collect-hover">op--data-collect-hover</h2>
				</li>
				<li title="op--data-collect-nor">
					<div class="icon-box">
						
						<!-- op--data-collect-nor -->
						<i class="picc-op--data-collect-nor">op--data-collect-nor</i>
						
					</div>
					<h2 title="op--data-collect-nor">op--data-collect-nor</h2>
				</li>
				<li title="op--delete-disable">
					<div class="icon-box">
						
						<!-- op--delete-disable -->
						<i class="picc-op--delete-disable">op--delete-disable</i>
						
					</div>
					<h2 title="op--delete-disable">op--delete-disable</h2>
				</li>
				<li title="op--delete-hover">
					<div class="icon-box">
						
						<!-- op--delete-hover -->
						<i class="picc-op--delete-hover">op--delete-hover</i>
						
					</div>
					<h2 title="op--delete-hover">op--delete-hover</h2>
				</li>
				<li title="op--delete-nor">
					<div class="icon-box">
						
						<!-- op--delete-nor -->
						<i class="picc-op--delete-nor">op--delete-nor</i>
						
					</div>
					<h2 title="op--delete-nor">op--delete-nor</h2>
				</li>
				<li title="op--doc-hover">
					<div class="icon-box">
						
						<!-- op--doc-hover -->
						<i class="picc-op--doc-hover">op--doc-hover</i>
						
					</div>
					<h2 title="op--doc-hover">op--doc-hover</h2>
				</li>
				<li title="op--doc-nor">
					<div class="icon-box">
						
						<!-- op--doc-nor -->
						<i class="picc-op--doc-nor">op--doc-nor</i>
						
					</div>
					<h2 title="op--doc-nor">op--doc-nor</h2>
				</li>
				<li title="op--down-db">
					<div class="icon-box">
						
						<!-- op--down-db -->
						<i class="picc-op--down-db">op--down-db</i>
						
					</div>
					<h2 title="op--down-db">op--down-db</h2>
				</li>
				<li title="op--excel-disable">
					<div class="icon-box">
						
						<!-- op--excel-disable -->
						<i class="picc-op--excel-disable">op--excel-disable</i>
						
					</div>
					<h2 title="op--excel-disable">op--excel-disable</h2>
				</li>
				<li title="op--excel-hover">
					<div class="icon-box">
						
						<!-- op--excel-hover -->
						<i class="picc-op--excel-hover">op--excel-hover</i>
						
					</div>
					<h2 title="op--excel-hover">op--excel-hover</h2>
				</li>
				<li title="op--excel-nor">
					<div class="icon-box">
						
						<!-- op--excel-nor -->
						<i class="picc-op--excel-nor">op--excel-nor</i>
						
					</div>
					<h2 title="op--excel-nor">op--excel-nor</h2>
				</li>
				<li title="op--filter-down">
					<div class="icon-box">
						
						<!-- op--filter-down -->
						<i class="picc-op--filter-down">op--filter-down</i>
						
					</div>
					<h2 title="op--filter-down">op--filter-down</h2>
				</li>
				<li title="op--filter-up">
					<div class="icon-box">
						
						<!-- op--filter-up -->
						<i class="picc-op--filter-up">op--filter-up</i>
						
					</div>
					<h2 title="op--filter-up">op--filter-up</h2>
				</li>
				<li title="op--flow-hover">
					<div class="icon-box">
						
						<!-- op--flow-hover -->
						<i class="picc-op--flow-hover">op--flow-hover</i>
						
					</div>
					<h2 title="op--flow-hover">op--flow-hover</h2>
				</li>
				<li title="op--flow-nor">
					<div class="icon-box">
						
						<!-- op--flow-nor -->
						<i class="picc-op--flow-nor">op--flow-nor</i>
						
					</div>
					<h2 title="op--flow-nor">op--flow-nor</h2>
				</li>
				<li title="op--input-delete">
					<div class="icon-box">
						
						<!-- op--input-delete -->
						<i class="picc-op--input-delete">op--input-delete</i>
						
					</div>
					<h2 title="op--input-delete">op--input-delete</h2>
				</li>
				<li title="op--input-delete-nor">
					<div class="icon-box">
						
						<!-- op--input-delete-nor -->
						<i class="picc-op--input-delete-nor">op--input-delete-nor</i>
						
					</div>
					<h2 title="op--input-delete-nor">op--input-delete-nor</h2>
				</li>
				<li title="op--left-disable">
					<div class="icon-box">
						
						<!-- op--left-disable -->
						<i class="picc-op--left-disable">op--left-disable</i>
						
					</div>
					<h2 title="op--left-disable">op--left-disable</h2>
				</li>
				<li title="op--left-hover">
					<div class="icon-box">
						
						<!-- op--left-hover -->
						<i class="picc-op--left-hover">op--left-hover</i>
						
					</div>
					<h2 title="op--left-hover">op--left-hover</h2>
				</li>
				<li title="op--left-nor">
					<div class="icon-box">
						
						<!-- op--left-nor -->
						<i class="picc-op--left-nor">op--left-nor</i>
						
					</div>
					<h2 title="op--left-nor">op--left-nor</h2>
				</li>
				<li title="op--left-o-disable">
					<div class="icon-box">
						
						<!-- op--left-o-disable -->
						<i class="picc-op--left-o-disable">op--left-o-disable</i>
						
					</div>
					<h2 title="op--left-o-disable">op--left-o-disable</h2>
				</li>
				<li title="op--left-o-hover">
					<div class="icon-box">
						
						<!-- op--left-o-hover -->
						<i class="picc-op--left-o-hover">op--left-o-hover</i>
						
					</div>
					<h2 title="op--left-o-hover">op--left-o-hover</h2>
				</li>
				<li title="op--left-o-nor">
					<div class="icon-box">
						
						<!-- op--left-o-nor -->
						<i class="picc-op--left-o-nor">op--left-o-nor</i>
						
					</div>
					<h2 title="op--left-o-nor">op--left-o-nor</h2>
				</li>
				<li title="op--list-hover">
					<div class="icon-box">
						
						<!-- op--list-hover -->
						<i class="picc-op--list-hover">op--list-hover</i>
						
					</div>
					<h2 title="op--list-hover">op--list-hover</h2>
				</li>
				<li title="op--list-nor">
					<div class="icon-box">
						
						<!-- op--list-nor -->
						<i class="picc-op--list-nor">op--list-nor</i>
						
					</div>
					<h2 title="op--list-nor">op--list-nor</h2>
				</li>
				<li title="op--loading-blue">
					<div class="icon-box">
						
						<!-- op--loading-blue -->
						<i class="picc-op--loading-blue">op--loading-blue</i>
						
					</div>
					<h2 title="op--loading-blue">op--loading-blue</h2>
				</li>
				<li title="op--loading-white">
					<div class="icon-box">
						
						<!-- op--loading-white -->
						<i class="picc-op--loading-white">op--loading-white</i>
						
					</div>
					<h2 title="op--loading-white">op--loading-white</h2>
				</li>
				<li title="op--log-hover">
					<div class="icon-box">
						
						<!-- op--log-hover -->
						<i class="picc-op--log-hover">op--log-hover</i>
						
					</div>
					<h2 title="op--log-hover">op--log-hover</h2>
				</li>
				<li title="op--log-nor">
					<div class="icon-box">
						
						<!-- op--log-nor -->
						<i class="picc-op--log-nor">op--log-nor</i>
						
					</div>
					<h2 title="op--log-nor">op--log-nor</h2>
				</li>
				<li title="op--msg">
					<div class="icon-box">
						
						<!-- op--msg -->
						<i class="picc-op--msg">op--msg</i>
						
					</div>
					<h2 title="op--msg">op--msg</h2>
				</li>
				<li title="op--msg-hover">
					<div class="icon-box">
						
						<!-- op--msg-hover -->
						<i class="picc-op--msg-hover">op--msg-hover</i>
						
					</div>
					<h2 title="op--msg-hover">op--msg-hover</h2>
				</li>
				<li title="op--phone">
					<div class="icon-box">
						
						<!-- op--phone -->
						<i class="picc-op--phone">op--phone</i>
						
					</div>
					<h2 title="op--phone">op--phone</h2>
				</li>
				<li title="op--prosess-disable">
					<div class="icon-box">
						
						<!-- op--prosess-disable -->
						<i class="picc-op--prosess-disable">op--prosess-disable</i>
						
					</div>
					<h2 title="op--prosess-disable">op--prosess-disable</h2>
				</li>
				<li title="op--prosess-hover">
					<div class="icon-box">
						
						<!-- op--prosess-hover -->
						<i class="picc-op--prosess-hover">op--prosess-hover</i>
						
					</div>
					<h2 title="op--prosess-hover">op--prosess-hover</h2>
				</li>
				<li title="op--prosess-nor">
					<div class="icon-box">
						
						<!-- op--prosess-nor -->
						<i class="picc-op--prosess-nor">op--prosess-nor</i>
						
					</div>
					<h2 title="op--prosess-nor">op--prosess-nor</h2>
				</li>
				<li title="op--right-disable">
					<div class="icon-box">
						
						<!-- op--right-disable -->
						<i class="picc-op--right-disable">op--right-disable</i>
						
					</div>
					<h2 title="op--right-disable">op--right-disable</h2>
				</li>
				<li title="op--right-hover">
					<div class="icon-box">
						
						<!-- op--right-hover -->
						<i class="picc-op--right-hover">op--right-hover</i>
						
					</div>
					<h2 title="op--right-hover">op--right-hover</h2>
				</li>
				<li title="op--right-nor">
					<div class="icon-box">
						
						<!-- op--right-nor -->
						<i class="picc-op--right-nor">op--right-nor</i>
						
					</div>
					<h2 title="op--right-nor">op--right-nor</h2>
				</li>
				<li title="op--right-o-disable">
					<div class="icon-box">
						
						<!-- op--right-o-disable -->
						<i class="picc-op--right-o-disable">op--right-o-disable</i>
						
					</div>
					<h2 title="op--right-o-disable">op--right-o-disable</h2>
				</li>
				<li title="op--right-o-hover">
					<div class="icon-box">
						
						<!-- op--right-o-hover -->
						<i class="picc-op--right-o-hover">op--right-o-hover</i>
						
					</div>
					<h2 title="op--right-o-hover">op--right-o-hover</h2>
				</li>
				<li title="op--right-o-nor">
					<div class="icon-box">
						
						<!-- op--right-o-nor -->
						<i class="picc-op--right-o-nor">op--right-o-nor</i>
						
					</div>
					<h2 title="op--right-o-nor">op--right-o-nor</h2>
				</li>
				<li title="op--search">
					<div class="icon-box">
						
						<!-- op--search -->
						<i class="picc-op--search">op--search</i>
						
					</div>
					<h2 title="op--search">op--search</h2>
				</li>
				<li title="op--search-hover">
					<div class="icon-box">
						
						<!-- op--search-hover -->
						<i class="picc-op--search-hover">op--search-hover</i>
						
					</div>
					<h2 title="op--search-hover">op--search-hover</h2>
				</li>
				<li title="op--up-db">
					<div class="icon-box">
						
						<!-- op--up-db -->
						<i class="picc-op--up-db">op--up-db</i>
						
					</div>
					<h2 title="op--up-db">op--up-db</h2>
				</li>
				<li title="op--upload-disable">
					<div class="icon-box">
						
						<!-- op--upload-disable -->
						<i class="picc-op--upload-disable">op--upload-disable</i>
						
					</div>
					<h2 title="op--upload-disable">op--upload-disable</h2>
				</li>
				<li title="op--upload-hover">
					<div class="icon-box">
						
						<!-- op--upload-hover -->
						<i class="picc-op--upload-hover">op--upload-hover</i>
						
					</div>
					<h2 title="op--upload-hover">op--upload-hover</h2>
				</li>
				<li title="op--upload-nor">
					<div class="icon-box">
						
						<!-- op--upload-nor -->
						<i class="picc-op--upload-nor">op--upload-nor</i>
						
					</div>
					<h2 title="op--upload-nor">op--upload-nor</h2>
				</li>
				<li title="others--alipay">
					<div class="icon-box">
						
						<!-- others--alipay -->
						<i class="picc-others--alipay">others--alipay</i>
						
					</div>
					<h2 title="others--alipay">others--alipay</h2>
				</li>
				<li title="others--card-id-back">
					<div class="icon-box">
						
						<!-- others--card-id-back -->
						<i class="picc-others--card-id-back">others--card-id-back</i>
						
					</div>
					<h2 title="others--card-id-back">others--card-id-back</h2>
				</li>
				<li title="others--card-id-front">
					<div class="icon-box">
						
						<!-- others--card-id-front -->
						<i class="picc-others--card-id-front">others--card-id-front</i>
						
					</div>
					<h2 title="others--card-id-front">others--card-id-front</h2>
				</li>
				<li title="others--card-pl">
					<div class="icon-box">
						
						<!-- others--card-pl -->
						<i class="picc-others--card-pl">others--card-pl</i>
						
					</div>
					<h2 title="others--card-pl">others--card-pl</h2>
				</li>
				<li title="others--cards">
					<div class="icon-box">
						
						<!-- others--cards -->
						<i class="picc-others--cards">others--cards</i>
						
					</div>
					<h2 title="others--cards">others--cards</h2>
				</li>
				<li title="others--empty-search">
					<div class="icon-box">
						
						<!-- others--empty-search -->
						<i class="picc-others--empty-search">others--empty-search</i>
						
					</div>
					<h2 title="others--empty-search">others--empty-search</h2>
				</li>
				<li title="others--file">
					<div class="icon-box">
						
						<!-- others--file -->
						<i class="picc-others--file">others--file</i>
						
					</div>
					<h2 title="others--file">others--file</h2>
				</li>
				<li title="others--payment">
					<div class="icon-box">
						
						<!-- others--payment -->
						<i class="picc-others--payment">others--payment</i>
						
					</div>
					<h2 title="others--payment">others--payment</h2>
				</li>
				<li title="others--photo">
					<div class="icon-box">
						
						<!-- others--photo -->
						<i class="picc-others--photo">others--photo</i>
						
					</div>
					<h2 title="others--photo">others--photo</h2>
				</li>
				<li title="others--upload-doc">
					<div class="icon-box">
						
						<!-- others--upload-doc -->
						<i class="picc-others--upload-doc">others--upload-doc</i>
						
					</div>
					<h2 title="others--upload-doc">others--upload-doc</h2>
				</li>
				<li title="others--upload-video">
					<div class="icon-box">
						
						<!-- others--upload-video -->
						<i class="picc-others--upload-video">others--upload-video</i>
						
					</div>
					<h2 title="others--upload-video">others--upload-video</h2>
				</li>
				<li title="others--wechat">
					<div class="icon-box">
						
						<!-- others--wechat -->
						<i class="picc-others--wechat">others--wechat</i>
						
					</div>
					<h2 title="others--wechat">others--wechat</h2>
				</li>
				<li title="sidebar--customer">
					<div class="icon-box">
						
						<!-- sidebar--customer -->
						<i class="picc-sidebar--customer">sidebar--customer</i>
						
					</div>
					<h2 title="sidebar--customer">sidebar--customer</h2>
				</li>
				<li title="sidebar--customer-hover">
					<div class="icon-box">
						
						<!-- sidebar--customer-hover -->
						<i class="picc-sidebar--customer-hover">sidebar--customer-hover</i>
						
					</div>
					<h2 title="sidebar--customer-hover">sidebar--customer-hover</h2>
				</li>
				<li title="sidebar--customer-selected">
					<div class="icon-box">
						
						<!-- sidebar--customer-selected -->
						<i class="picc-sidebar--customer-selected">sidebar--customer-selected</i>
						
					</div>
					<h2 title="sidebar--customer-selected">sidebar--customer-selected</h2>
				</li>
				<li title="sidebar--data">
					<div class="icon-box">
						
						<!-- sidebar--data -->
						<i class="picc-sidebar--data">sidebar--data</i>
						
					</div>
					<h2 title="sidebar--data">sidebar--data</h2>
				</li>
				<li title="sidebar--data-hover">
					<div class="icon-box">
						
						<!-- sidebar--data-hover -->
						<i class="picc-sidebar--data-hover">sidebar--data-hover</i>
						
					</div>
					<h2 title="sidebar--data-hover">sidebar--data-hover</h2>
				</li>
				<li title="sidebar--data-selected">
					<div class="icon-box">
						
						<!-- sidebar--data-selected -->
						<i class="picc-sidebar--data-selected">sidebar--data-selected</i>
						
					</div>
					<h2 title="sidebar--data-selected">sidebar--data-selected</h2>
				</li>
				<li title="sidebar--home">
					<div class="icon-box">
						
						<!-- sidebar--home -->
						<i class="picc-sidebar--home">sidebar--home</i>
						
					</div>
					<h2 title="sidebar--home">sidebar--home</h2>
				</li>
				<li title="sidebar--home-hover">
					<div class="icon-box">
						
						<!-- sidebar--home-hover -->
						<i class="picc-sidebar--home-hover">sidebar--home-hover</i>
						
					</div>
					<h2 title="sidebar--home-hover">sidebar--home-hover</h2>
				</li>
				<li title="sidebar--home-selected">
					<div class="icon-box">
						
						<!-- sidebar--home-selected -->
						<i class="picc-sidebar--home-selected">sidebar--home-selected</i>
						
					</div>
					<h2 title="sidebar--home-selected">sidebar--home-selected</h2>
				</li>
				<li title="sidebar--log">
					<div class="icon-box">
						
						<!-- sidebar--log -->
						<i class="picc-sidebar--log">sidebar--log</i>
						
					</div>
					<h2 title="sidebar--log">sidebar--log</h2>
				</li>
				<li title="sidebar--log-hover">
					<div class="icon-box">
						
						<!-- sidebar--log-hover -->
						<i class="picc-sidebar--log-hover">sidebar--log-hover</i>
						
					</div>
					<h2 title="sidebar--log-hover">sidebar--log-hover</h2>
				</li>
				<li title="sidebar--log-selected">
					<div class="icon-box">
						
						<!-- sidebar--log-selected -->
						<i class="picc-sidebar--log-selected">sidebar--log-selected</i>
						
					</div>
					<h2 title="sidebar--log-selected">sidebar--log-selected</h2>
				</li>
				<li title="sidebar--logo">
					<div class="icon-box">
						
						<!-- sidebar--logo -->
						<i class="picc-sidebar--logo">sidebar--logo</i>
						
					</div>
					<h2 title="sidebar--logo">sidebar--logo</h2>
				</li>
				<li title="sidebar--logo-sm">
					<div class="icon-box">
						
						<!-- sidebar--logo-sm -->
						<i class="picc-sidebar--logo-sm">sidebar--logo-sm</i>
						
					</div>
					<h2 title="sidebar--logo-sm">sidebar--logo-sm</h2>
				</li>
				<li title="sidebar--task">
					<div class="icon-box">
						
						<!-- sidebar--task -->
						<i class="picc-sidebar--task">sidebar--task</i>
						
					</div>
					<h2 title="sidebar--task">sidebar--task</h2>
				</li>
				<li title="sidebar--task-hover">
					<div class="icon-box">
						
						<!-- sidebar--task-hover -->
						<i class="picc-sidebar--task-hover">sidebar--task-hover</i>
						
					</div>
					<h2 title="sidebar--task-hover">sidebar--task-hover</h2>
				</li>
				<li title="sidebar--task-selected">
					<div class="icon-box">
						
						<!-- sidebar--task-selected -->
						<i class="picc-sidebar--task-selected">sidebar--task-selected</i>
						
					</div>
					<h2 title="sidebar--task-selected">sidebar--task-selected</h2>
				</li>
				<li title="sidebar--tools">
					<div class="icon-box">
						
						<!-- sidebar--tools -->
						<i class="picc-sidebar--tools">sidebar--tools</i>
						
					</div>
					<h2 title="sidebar--tools">sidebar--tools</h2>
				</li>
				<li title="sidebar--tools-hover">
					<div class="icon-box">
						
						<!-- sidebar--tools-hover -->
						<i class="picc-sidebar--tools-hover">sidebar--tools-hover</i>
						
					</div>
					<h2 title="sidebar--tools-hover">sidebar--tools-hover</h2>
				</li>
				<li title="sidebar--tools-selected">
					<div class="icon-box">
						
						<!-- sidebar--tools-selected -->
						<i class="picc-sidebar--tools-selected">sidebar--tools-selected</i>
						
					</div>
					<h2 title="sidebar--tools-selected">sidebar--tools-selected</h2>
				</li>
				<li title="status--add">
					<div class="icon-box">
						
						<!-- status--add -->
						<i class="picc-status--add">status--add</i>
						
					</div>
					<h2 title="status--add">status--add</h2>
				</li>
				<li title="status--add-large">
					<div class="icon-box">
						
						<!-- status--add-large -->
						<i class="picc-status--add-large">status--add-large</i>
						
					</div>
					<h2 title="status--add-large">status--add-large</h2>
				</li>
				<li title="status--claim-check">
					<div class="icon-box">
						
						<!-- status--claim-check -->
						<i class="picc-status--claim-check">status--claim-check</i>
						
					</div>
					<h2 title="status--claim-check">status--claim-check</h2>
				</li>
				<li title="status--claim-complete">
					<div class="icon-box">
						
						<!-- status--claim-complete -->
						<i class="picc-status--claim-complete">status--claim-complete</i>
						
					</div>
					<h2 title="status--claim-complete">status--claim-complete</h2>
				</li>
				<li title="status--claim-refund">
					<div class="icon-box">
						
						<!-- status--claim-refund -->
						<i class="picc-status--claim-refund">status--claim-refund</i>
						
					</div>
					<h2 title="status--claim-refund">status--claim-refund</h2>
				</li>
				<li title="status--failure">
					<div class="icon-box">
						
						<!-- status--failure -->
						<i class="picc-status--failure">status--failure</i>
						
					</div>
					<h2 title="status--failure">status--failure</h2>
				</li>
				<li title="status--loading">
					<div class="icon-box">
						
						<!-- status--loading -->
						<i class="picc-status--loading">status--loading</i>
						
					</div>
					<h2 title="status--loading">status--loading</h2>
				</li>
				<li title="status--notice-remind">
					<div class="icon-box">
						
						<!-- status--notice-remind -->
						<i class="picc-status--notice-remind">status--notice-remind</i>
						
					</div>
					<h2 title="status--notice-remind">status--notice-remind</h2>
				</li>
				<li title="status--notice-succeed">
					<div class="icon-box">
						
						<!-- status--notice-succeed -->
						<i class="picc-status--notice-succeed">status--notice-succeed</i>
						
					</div>
					<h2 title="status--notice-succeed">status--notice-succeed</h2>
				</li>
				<li title="status--notice-warning">
					<div class="icon-box">
						
						<!-- status--notice-warning -->
						<i class="picc-status--notice-warning">status--notice-warning</i>
						
					</div>
					<h2 title="status--notice-warning">status--notice-warning</h2>
				</li>
				<li title="status--p-batch">
					<div class="icon-box">
						
						<!-- status--p-batch -->
						<i class="picc-status--p-batch">status--p-batch</i>
						
					</div>
					<h2 title="status--p-batch">status--p-batch</h2>
				</li>
				<li title="status--p-batch-disable">
					<div class="icon-box">
						
						<!-- status--p-batch-disable -->
						<i class="picc-status--p-batch-disable">status--p-batch-disable</i>
						
					</div>
					<h2 title="status--p-batch-disable">status--p-batch-disable</h2>
				</li>
				<li title="status--p-batch-seleceted">
					<div class="icon-box">
						
						<!-- status--p-batch-seleceted -->
						<i class="picc-status--p-batch-seleceted">status--p-batch-seleceted</i>
						
					</div>
					<h2 title="status--p-batch-seleceted">status--p-batch-seleceted</h2>
				</li>
				<li title="status--p-group">
					<div class="icon-box">
						
						<!-- status--p-group -->
						<i class="picc-status--p-group">status--p-group</i>
						
					</div>
					<h2 title="status--p-group">status--p-group</h2>
				</li>
				<li title="status--p-group-disable">
					<div class="icon-box">
						
						<!-- status--p-group-disable -->
						<i class="picc-status--p-group-disable">status--p-group-disable</i>
						
					</div>
					<h2 title="status--p-group-disable">status--p-group-disable</h2>
				</li>
				<li title="status--p-group-selected">
					<div class="icon-box">
						
						<!-- status--p-group-selected -->
						<i class="picc-status--p-group-selected">status--p-group-selected</i>
						
					</div>
					<h2 title="status--p-group-selected">status--p-group-selected</h2>
				</li>
				<li title="status--p-single">
					<div class="icon-box">
						
						<!-- status--p-single -->
						<i class="picc-status--p-single">status--p-single</i>
						
					</div>
					<h2 title="status--p-single">status--p-single</h2>
				</li>
				<li title="status--p-single-selected">
					<div class="icon-box">
						
						<!-- status--p-single-selected -->
						<i class="picc-status--p-single-selected">status--p-single-selected</i>
						
					</div>
					<h2 title="status--p-single-selected">status--p-single-selected</h2>
				</li>
				<li title="status--pop-loading">
					<div class="icon-box">
						
						<!-- status--pop-loading -->
						<i class="picc-status--pop-loading">status--pop-loading</i>
						
					</div>
					<h2 title="status--pop-loading">status--pop-loading</h2>
				</li>
				<li title="status--pop-notice">
					<div class="icon-box">
						
						<!-- status--pop-notice -->
						<i class="picc-status--pop-notice">status--pop-notice</i>
						
					</div>
					<h2 title="status--pop-notice">status--pop-notice</h2>
				</li>
				<li title="status--pop-succeed">
					<div class="icon-box">
						
						<!-- status--pop-succeed -->
						<i class="picc-status--pop-succeed">status--pop-succeed</i>
						
					</div>
					<h2 title="status--pop-succeed">status--pop-succeed</h2>
				</li>
				<li title="status--pop-warning">
					<div class="icon-box">
						
						<!-- status--pop-warning -->
						<i class="picc-status--pop-warning">status--pop-warning</i>
						
					</div>
					<h2 title="status--pop-warning">status--pop-warning</h2>
				</li>
				<li title="status--required">
					<div class="icon-box">
						
						<!-- status--required -->
						<i class="picc-status--required">status--required</i>
						
					</div>
					<h2 title="status--required">status--required</h2>
				</li>
				<li title="status--result-failure">
					<div class="icon-box">
						
						<!-- status--result-failure -->
						<i class="picc-status--result-failure">status--result-failure</i>
						
					</div>
					<h2 title="status--result-failure">status--result-failure</h2>
				</li>
				<li title="status--result-succeed">
					<div class="icon-box">
						
						<!-- status--result-succeed -->
						<i class="picc-status--result-succeed">status--result-succeed</i>
						
					</div>
					<h2 title="status--result-succeed">status--result-succeed</h2>
				</li>
				<li title="status--result-warning">
					<div class="icon-box">
						
						<!-- status--result-warning -->
						<i class="picc-status--result-warning">status--result-warning</i>
						
					</div>
					<h2 title="status--result-warning">status--result-warning</h2>
				</li>
				<li title="status--step-o">
					<div class="icon-box">
						
						<!-- status--step-o -->
						<i class="picc-status--step-o">status--step-o</i>
						
					</div>
					<h2 title="status--step-o">status--step-o</h2>
				</li>
				<li title="status--step-refund">
					<div class="icon-box">
						
						<!-- status--step-refund -->
						<i class="picc-status--step-refund">status--step-refund</i>
						
					</div>
					<h2 title="status--step-refund">status--step-refund</h2>
				</li>
				<li title="status--step-succeed">
					<div class="icon-box">
						
						<!-- status--step-succeed -->
						<i class="picc-status--step-succeed">status--step-succeed</i>
						
					</div>
					<h2 title="status--step-succeed">status--step-succeed</h2>
				</li>
				<li title="status--step-succeed-dis">
					<div class="icon-box">
						
						<!-- status--step-succeed-dis -->
						<i class="picc-status--step-succeed-dis">status--step-succeed-dis</i>
						
					</div>
					<h2 title="status--step-succeed-dis">status--step-succeed-dis</h2>
				</li>
				<li title="status--success-large">
					<div class="icon-box">
						
						<!-- status--success-large -->
						<i class="picc-status--success-large">status--success-large</i>
						
					</div>
					<h2 title="status--success-large">status--success-large</h2>
				</li>
				<li title="status--toast-failure">
					<div class="icon-box">
						
						<!-- status--toast-failure -->
						<i class="picc-status--toast-failure">status--toast-failure</i>
						
					</div>
					<h2 title="status--toast-failure">status--toast-failure</h2>
				</li>
				<li title="status--toast-succeed">
					<div class="icon-box">
						
						<!-- status--toast-succeed -->
						<i class="picc-status--toast-succeed">status--toast-succeed</i>
						
					</div>
					<h2 title="status--toast-succeed">status--toast-succeed</h2>
				</li>
				<li title="status--toast-warning">
					<div class="icon-box">
						
						<!-- status--toast-warning -->
						<i class="picc-status--toast-warning">status--toast-warning</i>
						
					</div>
					<h2 title="status--toast-warning">status--toast-warning</h2>
				</li>
				<li title="tip--_claim-transfer">
					<div class="icon-box">
						
						<!-- tip--_claim-transfer -->
						<i class="picc-tip--_claim-transfer">tip--_claim-transfer</i>
						
					</div>
					<h2 title="tip--_claim-transfer">tip--_claim-transfer</h2>
				</li>
				<li title="tip--arrow-down">
					<div class="icon-box">
						
						<!-- tip--arrow-down -->
						<i class="picc-tip--arrow-down">tip--arrow-down</i>
						
					</div>
					<h2 title="tip--arrow-down">tip--arrow-down</h2>
				</li>
				<li title="tip--arrow_up">
					<div class="icon-box">
						
						<!-- tip--arrow_up -->
						<i class="picc-tip--arrow_up">tip--arrow_up</i>
						
					</div>
					<h2 title="tip--arrow_up">tip--arrow_up</h2>
				</li>
				<li title="tip--calendar-disable">
					<div class="icon-box">
						
						<!-- tip--calendar-disable -->
						<i class="picc-tip--calendar-disable">tip--calendar-disable</i>
						
					</div>
					<h2 title="tip--calendar-disable">tip--calendar-disable</h2>
				</li>
				<li title="tip--calendar-hover">
					<div class="icon-box">
						
						<!-- tip--calendar-hover -->
						<i class="picc-tip--calendar-hover">tip--calendar-hover</i>
						
					</div>
					<h2 title="tip--calendar-hover">tip--calendar-hover</h2>
				</li>
				<li title="tip--calendar-nor">
					<div class="icon-box">
						
						<!-- tip--calendar-nor -->
						<i class="picc-tip--calendar-nor">tip--calendar-nor</i>
						
					</div>
					<h2 title="tip--calendar-nor">tip--calendar-nor</h2>
				</li>
				<li title="tip--claim-first">
					<div class="icon-box">
						
						<!-- tip--claim-first -->
						<i class="picc-tip--claim-first">tip--claim-first</i>
						
					</div>
					<h2 title="tip--claim-first">tip--claim-first</h2>
				</li>
				<li title="tip--claim-icon">
					<div class="icon-box">
						
						<!-- tip--claim-icon -->
						<i class="picc-tip--claim-icon">tip--claim-icon</i>
						
					</div>
					<h2 title="tip--claim-icon">tip--claim-icon</h2>
				</li>
				<li title="tip--claim-switch">
					<div class="icon-box">
						
						<!-- tip--claim-switch -->
						<i class="picc-tip--claim-switch">tip--claim-switch</i>
						
					</div>
					<h2 title="tip--claim-switch">tip--claim-switch</h2>
				</li>
				<li title="tip--download_disable">
					<div class="icon-box">
						
						<!-- tip--download_disable -->
						<i class="picc-tip--download_disable">tip--download_disable</i>
						
					</div>
					<h2 title="tip--download_disable">tip--download_disable</h2>
				</li>
				<li title="tip--download_hover">
					<div class="icon-box">
						
						<!-- tip--download_hover -->
						<i class="picc-tip--download_hover">tip--download_hover</i>
						
					</div>
					<h2 title="tip--download_hover">tip--download_hover</h2>
				</li>
				<li title="tip--download_nor">
					<div class="icon-box">
						
						<!-- tip--download_nor -->
						<i class="picc-tip--download_nor">tip--download_nor</i>
						
					</div>
					<h2 title="tip--download_nor">tip--download_nor</h2>
				</li>
				<li title="tip--edit-disable">
					<div class="icon-box">
						
						<!-- tip--edit-disable -->
						<i class="picc-tip--edit-disable">tip--edit-disable</i>
						
					</div>
					<h2 title="tip--edit-disable">tip--edit-disable</h2>
				</li>
				<li title="tip--edit-hover">
					<div class="icon-box">
						
						<!-- tip--edit-hover -->
						<i class="picc-tip--edit-hover">tip--edit-hover</i>
						
					</div>
					<h2 title="tip--edit-hover">tip--edit-hover</h2>
				</li>
				<li title="tip--edit-nor">
					<div class="icon-box">
						
						<!-- tip--edit-nor -->
						<i class="picc-tip--edit-nor">tip--edit-nor</i>
						
					</div>
					<h2 title="tip--edit-nor">tip--edit-nor</h2>
				</li>
				<li title="tip--file">
					<div class="icon-box">
						
						<!-- tip--file -->
						<i class="picc-tip--file">tip--file</i>
						
					</div>
					<h2 title="tip--file">tip--file</h2>
				</li>
				<li title="tip--information">
					<div class="icon-box">
						
						<!-- tip--information -->
						<i class="picc-tip--information">tip--information</i>
						
					</div>
					<h2 title="tip--information">tip--information</h2>
				</li>
				<li title="tip--link">
					<div class="icon-box">
						
						<!-- tip--link -->
						<i class="picc-tip--link">tip--link</i>
						
					</div>
					<h2 title="tip--link">tip--link</h2>
				</li>
				<li title="tip--triange-down-selected">
					<div class="icon-box">
						
						<!-- tip--triange-down-selected -->
						<i class="picc-tip--triange-down-selected">tip--triange-down-selected</i>
						
					</div>
					<h2 title="tip--triange-down-selected">tip--triange-down-selected</h2>
				</li>
				<li title="tip--triangle-down-disable">
					<div class="icon-box">
						
						<!-- tip--triangle-down-disable -->
						<i class="picc-tip--triangle-down-disable">tip--triangle-down-disable</i>
						
					</div>
					<h2 title="tip--triangle-down-disable">tip--triangle-down-disable</h2>
				</li>
				<li title="tip--triangle-down-nor">
					<div class="icon-box">
						
						<!-- tip--triangle-down-nor -->
						<i class="picc-tip--triangle-down-nor">tip--triangle-down-nor</i>
						
					</div>
					<h2 title="tip--triangle-down-nor">tip--triangle-down-nor</h2>
				</li>
				<li title="tip--triangle-up">
					<div class="icon-box">
						
						<!-- tip--triangle-up -->
						<i class="picc-tip--triangle-up">tip--triangle-up</i>
						
					</div>
					<h2 title="tip--triangle-up">tip--triangle-up</h2>
				</li>
				<li title="tip--upload-disable">
					<div class="icon-box">
						
						<!-- tip--upload-disable -->
						<i class="picc-tip--upload-disable">tip--upload-disable</i>
						
					</div>
					<h2 title="tip--upload-disable">tip--upload-disable</h2>
				</li>
				<li title="tip--upload-hover">
					<div class="icon-box">
						
						<!-- tip--upload-hover -->
						<i class="picc-tip--upload-hover">tip--upload-hover</i>
						
					</div>
					<h2 title="tip--upload-hover">tip--upload-hover</h2>
				</li>
				<li title="tip--upload-nor">
					<div class="icon-box">
						
						<!-- tip--upload-nor -->
						<i class="picc-tip--upload-nor">tip--upload-nor</i>
						
					</div>
					<h2 title="tip--upload-nor">tip--upload-nor</h2>
				</li>
				<li title="tools--add">
					<div class="icon-box">
						
						<!-- tools--add -->
						<i class="picc-tools--add">tools--add</i>
						
					</div>
					<h2 title="tools--add">tools--add</h2>
				</li>
				<li title="tools--add-disable">
					<div class="icon-box">
						
						<!-- tools--add-disable -->
						<i class="picc-tools--add-disable">tools--add-disable</i>
						
					</div>
					<h2 title="tools--add-disable">tools--add-disable</h2>
				</li>
				<li title="tools--add-o">
					<div class="icon-box">
						
						<!-- tools--add-o -->
						<i class="picc-tools--add-o">tools--add-o</i>
						
					</div>
					<h2 title="tools--add-o">tools--add-o</h2>
				</li>
				<li title="tools--calculator-hover">
					<div class="icon-box">
						
						<!-- tools--calculator-hover -->
						<i class="picc-tools--calculator-hover">tools--calculator-hover</i>
						
					</div>
					<h2 title="tools--calculator-hover">tools--calculator-hover</h2>
				</li>
				<li title="tools--calculator-nor">
					<div class="icon-box">
						
						<!-- tools--calculator-nor -->
						<i class="picc-tools--calculator-nor">tools--calculator-nor</i>
						
					</div>
					<h2 title="tools--calculator-nor">tools--calculator-nor</h2>
				</li>
				<li title="tools--consult-hover">
					<div class="icon-box">
						
						<!-- tools--consult-hover -->
						<i class="picc-tools--consult-hover">tools--consult-hover</i>
						
					</div>
					<h2 title="tools--consult-hover">tools--consult-hover</h2>
				</li>
				<li title="tools--consult-nor">
					<div class="icon-box">
						
						<!-- tools--consult-nor -->
						<i class="picc-tools--consult-nor">tools--consult-nor</i>
						
					</div>
					<h2 title="tools--consult-nor">tools--consult-nor</h2>
				</li>
				<li title="tools--delete">
					<div class="icon-box">
						
						<!-- tools--delete -->
						<i class="picc-tools--delete">tools--delete</i>
						
					</div>
					<h2 title="tools--delete">tools--delete</h2>
				</li>
				<li title="tools--feedback-hover">
					<div class="icon-box">
						
						<!-- tools--feedback-hover -->
						<i class="picc-tools--feedback-hover">tools--feedback-hover</i>
						
					</div>
					<h2 title="tools--feedback-hover">tools--feedback-hover</h2>
				</li>
				<li title="tools--feedback-nor">
					<div class="icon-box">
						
						<!-- tools--feedback-nor -->
						<i class="picc-tools--feedback-nor">tools--feedback-nor</i>
						
					</div>
					<h2 title="tools--feedback-nor">tools--feedback-nor</h2>
				</li>
				<li title="tools--files-hover">
					<div class="icon-box">
						
						<!-- tools--files-hover -->
						<i class="picc-tools--files-hover">tools--files-hover</i>
						
					</div>
					<h2 title="tools--files-hover">tools--files-hover</h2>
				</li>
				<li title="tools--files-nor">
					<div class="icon-box">
						
						<!-- tools--files-nor -->
						<i class="picc-tools--files-nor">tools--files-nor</i>
						
					</div>
					<h2 title="tools--files-nor">tools--files-nor</h2>
				</li>
				<li title="tools--flows-hover">
					<div class="icon-box">
						
						<!-- tools--flows-hover -->
						<i class="picc-tools--flows-hover">tools--flows-hover</i>
						
					</div>
					<h2 title="tools--flows-hover">tools--flows-hover</h2>
				</li>
				<li title="tools--flows-nor">
					<div class="icon-box">
						
						<!-- tools--flows-nor -->
						<i class="picc-tools--flows-nor">tools--flows-nor</i>
						
					</div>
					<h2 title="tools--flows-nor">tools--flows-nor</h2>
				</li>
				<li title="tools--info-hover">
					<div class="icon-box">
						
						<!-- tools--info-hover -->
						<i class="picc-tools--info-hover">tools--info-hover</i>
						
					</div>
					<h2 title="tools--info-hover">tools--info-hover</h2>
				</li>
				<li title="tools--info-nor">
					<div class="icon-box">
						
						<!-- tools--info-nor -->
						<i class="picc-tools--info-nor">tools--info-nor</i>
						
					</div>
					<h2 title="tools--info-nor">tools--info-nor</h2>
				</li>
				<li title="tools--logs-hover">
					<div class="icon-box">
						
						<!-- tools--logs-hover -->
						<i class="picc-tools--logs-hover">tools--logs-hover</i>
						
					</div>
					<h2 title="tools--logs-hover">tools--logs-hover</h2>
				</li>
				<li title="tools--logs-nor">
					<div class="icon-box">
						
						<!-- tools--logs-nor -->
						<i class="picc-tools--logs-nor">tools--logs-nor</i>
						
					</div>
					<h2 title="tools--logs-nor">tools--logs-nor</h2>
				</li>
				<li title="tools--ocr-hover">
					<div class="icon-box">
						
						<!-- tools--ocr-hover -->
						<i class="picc-tools--ocr-hover">tools--ocr-hover</i>
						
					</div>
					<h2 title="tools--ocr-hover">tools--ocr-hover</h2>
				</li>
				<li title="tools--ocr-nor">
					<div class="icon-box">
						
						<!-- tools--ocr-nor -->
						<i class="picc-tools--ocr-nor">tools--ocr-nor</i>
						
					</div>
					<h2 title="tools--ocr-nor">tools--ocr-nor</h2>
				</li>
				<li title="tools--previdw-hover">
					<div class="icon-box">
						
						<!-- tools--previdw-hover -->
						<i class="picc-tools--previdw-hover">tools--previdw-hover</i>
						
					</div>
					<h2 title="tools--previdw-hover">tools--previdw-hover</h2>
				</li>
				<li title="tools--preview-nor">
					<div class="icon-box">
						
						<!-- tools--preview-nor -->
						<i class="picc-tools--preview-nor">tools--preview-nor</i>
						
					</div>
					<h2 title="tools--preview-nor">tools--preview-nor</h2>
				</li>
				<li title="tools--printer-hover">
					<div class="icon-box">
						
						<!-- tools--printer-hover -->
						<i class="picc-tools--printer-hover">tools--printer-hover</i>
						
					</div>
					<h2 title="tools--printer-hover">tools--printer-hover</h2>
				</li>
				<li title="tools--printer-nor">
					<div class="icon-box">
						
						<!-- tools--printer-nor -->
						<i class="picc-tools--printer-nor">tools--printer-nor</i>
						
					</div>
					<h2 title="tools--printer-nor">tools--printer-nor</h2>
				</li>
				<li title="tools--upload-hover">
					<div class="icon-box">
						
						<!-- tools--upload-hover -->
						<i class="picc-tools--upload-hover">tools--upload-hover</i>
						
					</div>
					<h2 title="tools--upload-hover">tools--upload-hover</h2>
				</li>
				<li title="tools--upload-nor">
					<div class="icon-box">
						
						<!-- tools--upload-nor -->
						<i class="picc-tools--upload-nor">tools--upload-nor</i>
						
					</div>
					<h2 title="tools--upload-nor">tools--upload-nor</h2>
				</li>
				<li title="tools--video-hover">
					<div class="icon-box">
						
						<!-- tools--video-hover -->
						<i class="picc-tools--video-hover">tools--video-hover</i>
						
					</div>
					<h2 title="tools--video-hover">tools--video-hover</h2>
				</li>
				<li title="tools--video-nor">
					<div class="icon-box">
						
						<!-- tools--video-nor -->
						<i class="picc-tools--video-nor">tools--video-nor</i>
						
					</div>
					<h2 title="tools--video-nor">tools--video-nor</h2>
				</li>
			</ul>

<!--
====================================================================================================
-->

		</section>
		<section>

<!--
	
B) External sprite with pre-defined views referenced by fragment identifiers
====================================================================================================
These SVG images make use of fragment identifiers (IDs) and are referencing the <view> elements
pre-defined in the external sprite.

-->

<!--
====================================================================================================
-->

		</section>
		<!-- <footer>
		</footer> -->
	</body>
</html>
