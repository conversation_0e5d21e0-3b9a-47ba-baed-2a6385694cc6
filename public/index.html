<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" /> -->
    <!--start
            集成嵌码（用户行为监控）：
            1、可以直接通过如下地址进行引入：https://**********/monitordistribute/ws.picc.min.js
            2、本地对https://**********/monitordistribute/ws.picc.min.js 这个地址进行反向代理，将服务请求转发的这个地址上
            3、集成门户时门户已经写了monitordistribute的反向代理，可以通过地址/monitordistribute/ws.picc.min.js实现引入
        -->
    <!-- <script src="https://**********/monitordistribute/ws.picc.min.js"></script> -->
    <!--end 集成嵌码（用户行为监控）：-->
    <link rel="icon" href="./favicon.ico" />
    <link rel="stylesheet" id="theme_link" data-base='<%= BASE_URL %>'
        href="<%= BASE_URL %>static/mutiTheme/index.min.css" />
    <link rel="stylesheet" id="currentStyle" data-base='<%= BASE_URL %>' href="<%= BASE_URL %>">
    <!-- <link rel="stylesheet" href="http://**********/theme/3.1.4/index.css" /> -->
    <!-- 引入JS -->
    <% for(var js of htmlWebpackPlugin.options.cdn.js) { %>
        <script src="<%=js%>"></script>
        <% } %>
            <!-- 引入CSS -->
            <% for(var css of htmlWebpackPlugin.options.cdn.css) { %>
                <link rel="stylesheet" href="<%=css%>" />
                <% } %>
                    <title>
                        <%= webpackConfig.name %>
                    </title>
</head>

<body>
    <div id="app"></div>
</body>

</html>