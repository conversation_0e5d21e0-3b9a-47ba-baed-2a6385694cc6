user  nginx;
worker_processes  4;
error_log /data/log/nginx/error.log;
#pid        logs/nginx.pid;

events {
    worker_connections  65535;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main 'server="" host="âdest_port=""'               
                     'src="" user="" '
                     'time_local="" http_status="" '
                     'http_referer="" http_user_agent="" '
                     'http_x_forwarded_for="" '
                     'http_x_header="" uri_query="" uri_path="" '
                     'request= http_method="" '; 
    access_log  /data/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  65;

        vhost_traffic_status_zone;
    vhost_traffic_status_filter_by_host on;

    server {
        listen       8888;
        server_name  localhost;

       # if (  ~ "^(\d{4})-(\d{2})-(\d{2})" ) {
       #     set  ;
       #     set  ;
       #     set  ;
       # }

        location / {
            access_log  /data/log/nginx/access_--.log  main;
            root   html;
            index  index.html index.htm;
        }

                location /nginx_status {
                        stub_status on;
                        access_log off;
        }
            
                location /status {
                        vhost_traffic_status_display;    
                        vhost_traffic_status_display_format html;
        }

                location ~ /\. {deny all; return 404; }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
	server {        
		listen  9006;        
		server_name  localhost;        
		charset utf-8;        
		location / {            
			root  /demo/main;            
			try_files  $uri $uri/ /index.html;        
		}	    
	}
	server {        
		listen  9008;        
		server_name  localhost;        
		charset utf-8;        
		location / {			
			add_header Access-Control-Allow-Origin *;			
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';			
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';            
			root  /demo/sub9008;            
			try_files  $uri $uri/ /index.html;        
		}	    
	}

}


