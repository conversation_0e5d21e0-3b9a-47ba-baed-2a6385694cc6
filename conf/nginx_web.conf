user  nginx;
worker_processes  4;
error_log /data/log/nginx/error.log;
#pid        logs/nginx.pid;

events {
    worker_connections  65535;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main 'server="$server_name" host="$hostâdest_port="$server_port"'               
                     'src="$remote_addr" user="$remote_user" '
                     'time_local="$time_local" http_status="$status" '
                     'http_referer="$http_referer" http_user_agent="$http_user_agent" '
                     'http_x_forwarded_for="$http_x_forwarded_for" '
                     'http_x_header="$http_x_header" uri_query="$query_string" uri_path="$uri" '
                     'request=$request http_method="$request_method" '; 
    access_log  /data/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  65;

        vhost_traffic_status_zone;
    vhost_traffic_status_filter_by_host on;

        # gzip模块设置
        gzip  on;         
        gzip_min_length  1k;       
        gzip_buffers     32 4k;     
        gzip_http_version 1.1;      
        gzip_comp_level 6;          
        gzip_types       text/plain application/x-javascript text/css application/xml text/javascript application/x-httpd-php application/javascript application/json;     
        gzip_disable "MSIE [1-6]\.";    
        gzip_vary on;               


    server {
        listen       8888;

                location /status {
                        vhost_traffic_status_display;    
                        vhost_traffic_status_display_format html;
        }

                #配置缓存区域
                location ~*\.(html)$ {                                      
                access_log off;         #不记录日å
                                                                        add_header  Cache-Control  max-age=no-cache;
        }
        server_name  www.123.com;

                if ( $time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})" ) {
            set $year $1;
            set $month $2;
            set $day $3;
        }


        location / {
                        access_log  /data/log/nginx/access_$year-$month-$day.log  main;
            root   html;
            index  index.html index.htm;
                        try_files $uri $uri/ /baseurl/index.html;
            etag off;               #集群部署状态下建议关闭，单机部署建议开å
                                                                                                            
            # html不缓存，或协商缓å
                                                                if ($request_filename ~* .*\.(htm|html)$)  
                        {
                                expires 0;
                                add_header Cache-Control no-cache;
                                add_header Pragma no-cache;
                        }

                        # 以下扩展名文件开启强制缓å
                                                                                        if ($request_filename ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$) {
                                add_header Cache-Control max-age=86400;
                                expires 1d;
                        }
                        if ($request_filename ~* .*\.(ttf|woff)$) {
                                add_header Cache-Control max-age=2592000;
                                expires 30d;
                        }
                        if ($request_filename ~* .*\.(js|css)$) {
                                add_header Cache-Control max-age=604800;
                                expires 7d;
                        }
        }

                location ~ /\. {deny all; return 404; }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
