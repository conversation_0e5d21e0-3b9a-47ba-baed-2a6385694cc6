user  nginx;
worker_processes  4;
error_log /data/log/nginx/error.log;
#pid        logs/nginx.pid;

events {
    worker_connections  65535;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main 'server="$server_name" host="$hostâdest_port="$server_port"'               
                     'src="$remote_addr" user="$remote_user" '
                     'time_local="$time_local" http_status="$status" '
                     'http_referer="$http_referer" http_user_agent="$http_user_agent" '
                     'http_x_forwarded_for="$http_x_forwarded_for" '
                     'http_x_header="$http_x_header" uri_query="$query_string" uri_path="$uri" '
                     'request=$request http_method="$request_method" '; 
    access_log  /data/log/nginx/access.log  main;
    sendfile        on;
    keepalive_timeout  65;

        vhost_traffic_status_zone;
    vhost_traffic_status_filter_by_host on;

    server {
        listen       8888;
        server_name  localhost;


                if ( $time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})" ) {
            set $year $1;
            set $month $2;
            set $day $3;
        }

                location /status {
                        vhost_traffic_status_display;    
                        vhost_traffic_status_display_format html;
        }

        location / {
                        # 按天输出日志
                        access_log  /data/log/nginx/access_$year-$month-$day.log  main;
                        #设置nginx保存HTTP报文头的hash表的上限，默认为512字节
                        proxy_headers_hash_max_size 512;
                        #申请nginx保存HTTP报文头的hash表的空间大小，默认为64个字è
                                                                                                                   proxy_headers_hash_bucket_size 128; 
                        #更改客户端的请求头部信息内容并转发至后端服务å
                                                                                                                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                        # 设置Nginx使用代理模式时传递客户端源IP信息
                        proxy_set_header X-Real-IP $remote_addr;  
                        # 隐藏服务端X-Powered-By信息
                        proxy_hide_header X-Powered-By;
                        # 隐藏服务端Server响应头信æ
                                                                                proxy_hide_header Server; 
            # 代理目标地址
            proxy_pass http://xxx;
        }

                location ~ /\. {deny all; return 404; }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
