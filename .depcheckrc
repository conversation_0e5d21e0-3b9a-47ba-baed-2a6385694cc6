// autoprefixer：自动补全css前缀，兼容不通浏览器的css差异
// compression-webpack-plugin：对打包后文件进行gzip压缩
// gzip-loader：webpack 的 gzip 加载器模块，允许加载 gzip 压缩的资源。
// happypack：将文件解析任务分解成多个子进程并发执行。子进程处理完任务后再将结果发送给主进程。所以可以大大提升 Webpack 的项目构件速度
// hard-source-webpack-plugin：为模块中间提供缓存，在项目第一次构建将花费正常的时间，第二次构建时间将会大幅度缩减。
// html-webpack-plugin：将 webpack 打包生成的文件（比如 js 文件、css 文件）嵌入到 html 文件中
// mini-css-extract-plugin：将 CSS 代码从 JavaScript 中分离出来，生成单独的 CSS 文件
// svg-inline-loader分析 SVG 的内容，去除其中不必要的部分代码，以减少 SVG 的文件大小，把文本文件的内容读取出来，注入到 JavaScript 或 CSS 中去
// svg-sprite-loader：svg图片加载器，svg-icon组件用到
// svgo-loader： 是 svg 优化器，包含很多插件。它可以删除和修改SVG元素，折叠内容，移动属性等等。
// terser-webpack-plugin：用来压缩 JavaScript。我们项目主要用来去除生产环境中的console以及debugger等信息。
// thread-loader：并发打包，可能会报错
// vue-highlightjs：vue代码语法高亮
ignores: ["eslint*", "babel-*", "@babel/*","@vue/*","core-js","sass*"]
skip-missing: true
